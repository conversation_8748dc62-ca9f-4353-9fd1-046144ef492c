// 使用Web Audio API生成音頻
function playBeep() {
    try {
        // 創建音頻上下文
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // 創建振蕩器
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        // 設置振蕩器參數
        oscillator.type = 'sine'; // 正弦波
        oscillator.frequency.value = 800; // 頻率800Hz
        gainNode.gain.value = 0.5; // 音量

        // 連接節點
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // 開始播放
        oscillator.start();

        // 0.3秒後停止
        setTimeout(() => {
            oscillator.stop();
            console.log('Beep played successfully');
        }, 300);

        return true;
    } catch (error) {
        console.error('Error playing beep:', error);
        return false;
    }
}
