// 聲音通知腳本
document.addEventListener('DOMContentLoaded', function() {
    console.log('聲音通知腳本已加載');

    // 創建音頻元素
    const audio = new Audio('/sounds/alert-2316.mp3');

    // 預加載音頻
    audio.load();

    // 創建UI元素
    const container = document.createElement('div');
    container.className = 'fixed bottom-4 right-4 z-50 flex flex-col items-end space-y-2 p-4 bg-white rounded-lg shadow';
    container.style.cssText = 'position: fixed; bottom: 20px; right: 20px; z-index: 9999; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);';

    // 創建標題
    const title = document.createElement('h3');
    title.textContent = '聲音通知設置';
    title.style.cssText = 'margin: 0 0 10px 0; font-size: 16px; font-weight: 600;';
    container.appendChild(title);

    // 創建按鈕容器
    const buttonContainer = document.createElement('div');
    buttonContainer.style.cssText = 'display: flex; gap: 10px; margin-bottom: 10px;';

    // 創建測試聲音按鈕
    const testButton = document.createElement('button');
    testButton.textContent = '測試聲音';
    testButton.style.cssText = 'background: #3b82f6; color: white; padding: 5px 10px; border-radius: 4px; border: none; cursor: pointer;';
    testButton.addEventListener('click', function() {
        playSound();
        updateStatus('正在測試聲音...');
    });
    buttonContainer.appendChild(testButton);

    // 創建檢查未指派記錄按鈕
    const checkButton = document.createElement('button');
    checkButton.textContent = '檢查未指派記錄';
    checkButton.style.cssText = 'background: #22c55e; color: white; padding: 5px 10px; border-radius: 4px; border: none; cursor: pointer;';
    checkButton.addEventListener('click', function() {
        checkUnassigned(true);
        updateStatus('正在檢查未指派記錄...');
    });
    buttonContainer.appendChild(checkButton);

    container.appendChild(buttonContainer);

    // 創建狀態消息元素
    const statusElement = document.createElement('div');
    statusElement.textContent = '系統將自動檢測未指派的記錄並播放提示音';
    statusElement.style.cssText = 'font-size: 14px; color: #666;';
    container.appendChild(statusElement);

    // 將容器添加到頁面
    document.body.appendChild(container);

    // 上一次檢查時的未指派記錄數量
    let previousUnassignedCount = 0;

    // 初始化時獲取未指派記錄數量
    setTimeout(() => {
        previousUnassignedCount = countUnassignedElements();
        updateStatus(`初始化完成，找到 ${previousUnassignedCount} 個未指派記錄`);
        console.log(`初始未指派數量: ${previousUnassignedCount}`);
    }, 1000);

    // 設置定期檢查
    setInterval(checkUnassigned, 5000); // 每5秒檢查一次

    // 更新狀態消息
    function updateStatus(message) {
        statusElement.textContent = message;
        console.log(message);
    }

    // 播放聲音的函數
    function playSound() {
        // 重置音頻並播放
        audio.currentTime = 0;

        // 嘗試播放聲音
        const playPromise = audio.play();

        // 處理可能的錯誤
        if (playPromise !== undefined) {
            playPromise
                .then(() => {
                    updateStatus('聲音播放成功');
                    console.log('聲音播放成功');
                })
                .catch(error => {
                    console.error('播放聲音失敗:', error);
                    updateStatus('無法自動播放聲音，請點擊頁面以啟用聲音');

                    // 添加一次性點擊事件來啟用聲音
                    document.addEventListener('click', function enableAudio() {
                        audio.play()
                            .then(() => {
                                audio.pause();
                                audio.currentTime = 0;
                                updateStatus('聲音已啟用');
                                console.log('聲音已啟用');
                                document.removeEventListener('click', enableAudio);
                            })
                            .catch(e => console.error('仍然無法播放聲音:', e));
                    }, { once: false });
                });
        }
    }

    // 計算未指派元素數量的函數
    function countUnassignedElements() {
        // 嘗試多種選擇器
        let elements = document.querySelectorAll('[data-status="未指派"]');

        // 如果沒找到，嘗試查找包含文本的元素
        if (elements.length === 0) {
            const badges = document.querySelectorAll('.fi-badge, .badge, [role="status"]');
            const unassignedBadges = Array.from(badges).filter(el =>
                el.textContent.includes('未指派')
            );
            elements = unassignedBadges;
        }

        console.log(`找到未指派元素數量: ${elements.length}`);
        return elements.length;
    }

    // 檢查未指派記錄的函數
    function checkUnassigned(forceCheck = false) {
        const currentCount = countUnassignedElements();
        console.log(`當前未指派數量: ${currentCount}, 先前數量: ${previousUnassignedCount}`);

        // 如果未指派記錄數量增加或強制檢查
        if ((currentCount > previousUnassignedCount && previousUnassignedCount > 0) || forceCheck) {
            if (forceCheck) {
                updateStatus(`找到 ${currentCount} 個未指派記錄`);
            } else {
                updateStatus(`檢測到新的未指派記錄！從 ${previousUnassignedCount} 增加到 ${currentCount}`);
                console.log('檢測到新的未指派記錄，播放聲音');
                playSound();
            }
        }

        // 更新先前數量
        previousUnassignedCount = currentCount;
    }
});
