// 使用Web Audio API生成音頻
function playBeep() {
    try {
        // 創建音頻上下文
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // 創建振蕩器
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        // 設置振蕩器參數
        oscillator.type = "sine"; // 正弦波
        oscillator.frequency.value = 800; // 頻率800Hz
        gainNode.gain.value = 0.5; // 音量

        // 連接節點
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // 開始播放
        oscillator.start();

        // 0.3秒後停止
        setTimeout(() => {
            oscillator.stop();
            console.log("Beep played successfully");
        }, 300);

        return true;
    } catch (error) {
        console.error("Error playing beep:", error);
        return false;
    }
}

// 頁面加載完成後執行
document.addEventListener("DOMContentLoaded", function() {
    console.log("聲音通知腳本已加載");

    // 創建UI元素
    const container = document.createElement("div");
    container.style.cssText = "position: fixed; bottom: 20px; right: 20px; z-index: 9999; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);";

    // 創建標題
    const title = document.createElement("h3");
    title.textContent = "聲音通知設置";
    title.style.cssText = "margin: 0 0 10px 0; font-size: 16px; font-weight: 600;";
    container.appendChild(title);

    // 創建按鈕容器
    const buttonContainer = document.createElement("div");
    buttonContainer.style.cssText = "display: flex; gap: 10px; margin-bottom: 10px;";

    // 創建測試聲音按鈕
    const testButton = document.createElement("button");
    testButton.textContent = "測試聲音";
    testButton.style.cssText = "background: #3b82f6; color: white; padding: 5px 10px; border-radius: 4px; border: none; cursor: pointer;";
    testButton.addEventListener("click", function() {
        playBeep();
        updateStatus("正在測試聲音...");
    });
    buttonContainer.appendChild(testButton);

    // 創建檢查未指派記錄按鈕
    const checkButton = document.createElement("button");
    checkButton.textContent = "檢查未指派記錄";
    checkButton.style.cssText = "background: #22c55e; color: white; padding: 5px 10px; border-radius: 4px; border: none; cursor: pointer;";
    checkButton.addEventListener("click", function() {
        checkUnassigned(true);
        updateStatus("正在檢查未指派記錄...");
    });
    buttonContainer.appendChild(checkButton);

    container.appendChild(buttonContainer);

    // 創建狀態消息元素
    const statusElement = document.createElement("div");
    statusElement.textContent = "系統將自動檢測未指派的記錄並播放提示音 (首次點擊按鈕可能需要啟用聲音)";
    statusElement.style.cssText = "font-size: 14px; color: #666;";
    container.appendChild(statusElement);

    // 將容器添加到頁面
    document.body.appendChild(container);

    // 上一次檢查時的未指派記錄數量
    let previousUnassignedCount = 0;

    // 創建一個覆蓋整個頁面的透明按鈕，用於啟用聲音
    const enableSoundOverlay = document.createElement("div");
    enableSoundOverlay.style.cssText = "position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; flex-direction: column; justify-content: center; align-items: center; color: white; font-size: 20px; text-align: center;";
    enableSoundOverlay.innerHTML = `
        <div style="background: white; padding: 30px; border-radius: 10px; max-width: 500px; color: black;">
            <h2 style="margin-top: 0;">啟用聲音通知</h2>
            <p>系統檢測到有 <span id="unassigned-count" style="font-weight: bold; color: red;"></span> 個未指派記錄。</p>
            <p>由於瀏覽器安全限制，需要您點擊下方按鈕來啟用自動聲音通知功能。</p>
            <button id="enable-sound-button" style="background: #3b82f6; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; margin-top: 10px;">啟用聲音通知</button>
        </div>
    `;

    // 初始化時獲取未指派記錄數量
    setTimeout(() => {
        previousUnassignedCount = countUnassignedElements();
        updateStatus(`初始化完成，找到 ${previousUnassignedCount} 個未指派記錄`);
        console.log(`初始未指派數量: ${previousUnassignedCount}`);

        // 如果有未指派記錄，顯示啟用聲音的覆蓋層
        if (previousUnassignedCount > 0) {
            document.getElementById("unassigned-count").textContent = previousUnassignedCount;
            document.body.appendChild(enableSoundOverlay);

            // 點擊啟用聲音按鈕
            document.getElementById("enable-sound-button").addEventListener("click", function() {
                // 播放聲音
                playBeep();
                // 移除覆蓋層
                document.body.removeChild(enableSoundOverlay);
                // 更新狀態
                updateStatus(`聲音通知已啟用，找到 ${previousUnassignedCount} 個未指派記錄`);
            });
        }
    }, 1000);

    // 設置定期檢查
    setInterval(checkUnassigned, 5000); // 每5秒檢查一次

    // 更新狀態消息
    function updateStatus(message) {
        statusElement.textContent = message;
        console.log(message);
    }

    // 計算未指派元素數量的函數
    function countUnassignedElements() {
        // 嘗試多種選擇器
        let elements = document.querySelectorAll("[data-status='未指派']");

        // 如果沒找到，嘗試查找包含文本的元素
        if (elements.length === 0) {
            const badges = document.querySelectorAll(".fi-badge, .badge, [role='status']");
            const unassignedBadges = Array.from(badges).filter(el =>
                el.textContent.includes("未指派")
            );
            elements = unassignedBadges;
        }

        console.log(`找到未指派元素數量: ${elements.length}`);
        return elements.length;
    }

    // 檢查未指派記錄的函數
    function checkUnassigned(forceCheck = false) {
        const currentCount = countUnassignedElements();
        console.log(`當前未指派數量: ${currentCount}, 先前數量: ${previousUnassignedCount}`);

        // 如果有未指派記錄，每次檢查都播放聲音
        if (currentCount > 0) {
            if (forceCheck) {
                updateStatus(`找到 ${currentCount} 個未指派記錄，播放提示音`);
                playBeep();
            } else {
                // 如果未指派記錄數量變化，更新狀態
                if (currentCount !== previousUnassignedCount) {
                    updateStatus(`未指派記錄數量變化：從 ${previousUnassignedCount} 變為 ${currentCount}`);
                } else {
                    updateStatus(`檢測到 ${currentCount} 個未指派記錄，播放提示音`);
                }
                console.log(`檢測到 ${currentCount} 個未指派記錄，播放提示音`);
                playBeep();
            }
        } else {
            updateStatus("No unassigned records");
        }

        // 更新先前數量
        previousUnassignedCount = currentCount;
    }
});
