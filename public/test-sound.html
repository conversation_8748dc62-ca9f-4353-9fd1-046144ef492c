<!DOCTYPE html>
<html>
<head>
    <title>測試聲音</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 8px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
    </style>
    <!-- 引入Web Audio API腳本 -->
    <script src="/sounds/beep.js"></script>
</head>
<body>
    <div class="container">
        <h1>聲音測試頁面</h1>
        <p>這個頁面用於測試聲音是否能正常播放。</p>

        <h2>1. 使用Web Audio API播放聲音</h2>
        <button id="beepButton">播放嗶聲</button>

        <h2>2. 使用HTML5 Audio播放聲音</h2>
        <p>嘗試不同的音頻格式：</p>

        <h3>MP3格式</h3>
        <audio id="audioMp3" src="/sounds/alert-2316.mp3" controls></audio>
        <button onclick="document.getElementById('audioMp3').play()">播放MP3</button>

        <h3>WAV格式</h3>
        <audio id="audioWav" src="/sounds/alert-2316.wav" controls></audio>
        <button onclick="document.getElementById('audioWav').play()">播放WAV</button>

        <h2>3. 模擬檢查未指派記錄</h2>
        <button id="checkButton">檢查未指派記錄</button>

        <div class="status" id="status">狀態: 等待操作</div>
    </div>

    <script>
        // 獲取元素
        const beepButton = document.getElementById('beepButton');
        const checkButton = document.getElementById('checkButton');
        const statusElement = document.getElementById('status');

        // 添加事件監聽器
        beepButton.addEventListener('click', function() {
            const success = playBeep();
            if (success) {
                updateStatus('嗶聲播放成功');
            } else {
                updateStatus('嗶聲播放失敗');
            }
        });

        checkButton.addEventListener('click', function() {
            const count = Math.floor(Math.random() * 5) + 1; // 隨機生成1-5的數字
            updateStatus(`找到 ${count} 個未指派記錄`);

            if (count > 0) {
                playBeep();
            }
        });

        // 更新狀態消息
        function updateStatus(message) {
            statusElement.textContent = '狀態: ' + message;
            console.log(message);
        }

        // 頁面加載完成後顯示瀏覽器信息
        document.addEventListener('DOMContentLoaded', function() {
            const browserInfo = `瀏覽器: ${navigator.userAgent}`;
            console.log(browserInfo);

            // 檢查Web Audio API支持
            if (window.AudioContext || window.webkitAudioContext) {
                console.log('Web Audio API 支持: 是');
            } else {
                console.log('Web Audio API 支持: 否');
                updateStatus('警告: 您的瀏覽器不支持Web Audio API');
            }
        });
    </script>
</body>
</html>
