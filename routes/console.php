<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

// Artisan::command('inspire', function () {
//     $this->comment(Inspiring::quote());
// })->purpose('Display an inspiring quote')->hourly();

// Schedule::command('send:user-count')->everyMinute();
Schedule::command('send:cannon-user-count')->timezone('Asia/Taipei')->dailyAt('04:00');
Schedule::command('send:yantan-dispatch')->timezone('Asia/Taipei')->dailyAt('04:00');
// Schedule::timezone('Asia/Taipei');
