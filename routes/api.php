<?php

use App\Http\Controllers\Api\DriverAccommodationController;
use App\Http\Controllers\api\LineBotController;
use App\Http\Controllers\Api\PriceController;
use App\Http\Controllers\ADispatchController;
use App\Http\Controllers\CostController;
use App\Http\Controllers\LineNotifyController;
use App\Http\Controllers\SlackController;
use App\Models\Dispatch;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::post('/webhook', [LineBotController::class, 'webhook']);

Route::get('/vendor', [App\Http\Controllers\api\VendorController::class, 'index']);
Route::post('/driver', [App\Http\Controllers\api\DriverController::class, 'store']);
Route::post('/edit', [App\Http\Controllers\api\DriverController::class, 'edit']);
Route::post('/person', [App\Http\Controllers\api\DriverController::class, 'person']);
Route::post('/history', [App\Http\Controllers\api\DriverController::class, 'history']);
Route::post('/historyv2', [App\Http\Controllers\api\DriverController::class, 'historyv2']);
// 雅潭矽品 chrome ext 建立資料
Route::post('/createdispatchbychrome', [App\Http\Controllers\api\ReserveController::class, 'createdispatchbychrome']);
// 雅潭 admin
Route::post('/historyv2admin', [App\Http\Controllers\api\DriverController::class, 'historyv2admin']);
Route::post('/updatedata', [App\Http\Controllers\api\DriverController::class, 'updatedata']);
Route::post('/deldata', [App\Http\Controllers\api\DriverController::class, 'deldata']);
Route::post('/dispatch', [App\Http\Controllers\api\DispatchController::class, 'store']);
Route::post('/dispatchv2', [App\Http\Controllers\api\DispatchController::class, 'storev2']);
// api edit dispatch
Route::post('/editdispatch', [App\Http\Controllers\api\DispatchController::class, 'editdispatch']);
Route::post('/reserve', [App\Http\Controllers\api\ReserveController::class, 'store']);
Route::post('/sendbookingemail', [App\Http\Controllers\api\ReserveController::class, 'sendBookingEmail']);
Route::post('/sendcontactemail', [App\Http\Controllers\api\ReserveController::class, 'sendContactEmail']);
Route::post('/uploadsignature', [App\Http\Controllers\api\DispatchController::class, 'uploadsignature']);
Route::post('/uploadmultisignatures', [App\Http\Controllers\api\DispatchController::class, 'uploadMultiSignatures']);  // <--- Add this line

Route::get('/mytest', [App\Http\Controllers\api\DriverController::class, 'getdata']);
Route::get('/myview', [App\Http\Controllers\api\DispatchController::class, 'myMonthSignatureView']);
Route::get('/mylistview', [App\Http\Controllers\api\DispatchController::class, 'myMonthSignatureListView']);
Route::post('/mkepaper', [App\Http\Controllers\api\DispatchController::class, 'mkepaper']);
// 手動更新派車單
Route::post('/menueditdispatch', [App\Http\Controllers\api\DispatchController::class, 'makeMenuPublicImage']);
Route::match(['get', 'post'], '/line_notify', [LineNotifyController::class, 'index']);
Route::get('/phpinfo', function () {
    // $arrCustomerAndDriver = ['passenger' =>
    //         ['河野廣瀨', '今村、沼野、福田', '牧野、瀧上、小島', '加藤，石塚，安永'],
    //          'driver' =>
    //           [944,948,957,946],
    //         'route' =>
    //         ['文心 至 潭子','太子國寶、星捷市、宏台松築  至  佳能','文心One  至  佳能','文心One  至  佳能']];
    //     // $vendor = Vendor::where('id', $vendor_id)->first();

    echo 'Done';
});
Route::get('/my-form', [SlackController::class, 'form']);
Route::post('/store-form', [SlackController::class, 'store']);
Route::post('/textadddispatch', [App\Http\Controllers\api\ReserveController::class, 'textadddispatch']);
Route::post('/n8ncreatedispatch', [App\Http\Controllers\api\DispatchController::class, 'n8ncreatedispatch']);
// 空白表單建立單據
Route::post('/blankdispatch', [App\Http\Controllers\api\ReserveController::class, 'blankdispatch']);
Route::post('/register', [App\Http\Controllers\api\ReserveController::class, 'register']);
Route::post('/point', [App\Http\Controllers\api\ReserveController::class, 'point']);
Route::post('/blanklist', [App\Http\Controllers\api\ReserveController::class, 'blanklist']);
Route::post('/depositlist', [App\Http\Controllers\api\ReserveController::class, 'depositlist']);
Route::post('/editblankdispatch', [App\Http\Controllers\api\ReserveController::class, 'editblankdispatch']);
Route::post('/editblanksignature', [App\Http\Controllers\api\ReserveController::class, 'editblanksignature']);
Route::post('/checkdriver', [App\Http\Controllers\api\ReserveController::class, 'checkdriver']);
Route::post('/updatedriver', [App\Http\Controllers\api\ReserveController::class, 'updatedriver']);
Route::post('/addblankdispatch', [App\Http\Controllers\api\ReserveController::class, 'addblankdispatch']);
Route::post('/delete-blank-dispatch', [App\Http\Controllers\api\ReserveController::class, 'deleteBlankDispatch']);
Route::post('/topup', [App\Http\Controllers\api\ReserveController::class, 'topup']);
// 旅行社車行自動化 樂虎 雅潭
Route::post('/parse-dispatch', [ADispatchController::class, 'parseAndStoreDispatch']);
Route::post('/caselist', [ADispatchController::class, 'caselist']);
Route::post('/updatestart', [ADispatchController::class, 'updatestart']);
Route::post('/updatecustomerup', [ADispatchController::class, 'updatecustomerup']);
Route::post('/updatecustomerdown', [ADispatchController::class, 'updatecustomerdown']);
Route::post('/updatesignature', [ADispatchController::class, 'updatesignature']);
Route::post('/getcarorder', [ADispatchController::class, 'getcarorder']);
Route::post('/confirmorder', [ADispatchController::class, 'confirmorder']);
Route::post('/adriver/register', [ADispatchController::class, 'registerDriver']);  // <--- 加入此行
Route::post('/adriver/update', [ADispatchController::class, 'updateDriver']);  // <--- 加入此行
Route::post('/getdriverprofile', [ADispatchController::class, 'getdriverprofile']);  // <--- 加入此行
Route::post('/checkadriver', [ADispatchController::class, 'checkadriver']);  // <--- 檢查司機是否存在

// web liff 取得報價 時宜 LINE LIFF 詢價/預約介面
Route::post('/getquote', [App\Http\Controllers\api\ReserveController::class, 'getquote']);
Route::post('/getquobyorder', [App\Http\Controllers\api\ReserveController::class, 'getquobyorder']);
Route::post('/carorder', [App\Http\Controllers\api\ReserveController::class, 'carorder']);
Route::post('/getcitys', [App\Http\Controllers\api\ReserveController::class, 'getcitys']);
Route::post('/getcitys2', [App\Http\Controllers\api\ReserveController::class, 'getcitys2']);
Route::post('/getcitys21', [App\Http\Controllers\api\ReserveController::class, 'getcitys21']);
Route::post('/getcitys3', [App\Http\Controllers\api\ReserveController::class, 'getcitys3']);
Route::post('/updateposition', [App\Http\Controllers\api\ReserveController::class, 'updateposition']);
Route::post('/getquote/v2', [App\Http\Controllers\api\ReserveController::class, 'getquotev2']);
Route::post('/carorder/v2', [App\Http\Controllers\api\ReserveController::class, 'carorderv2']);
Route::post('/bulletin', [App\Http\Controllers\api\ReserveController::class, 'bulletin']);
// Route::get('/run', function(){
//     $count = Dispatch::where('customer_name', 'like', '%佳能%')
//                 ->whereDate('start_time', today())->count();
//     echo $count;
// });
// Route::post('/costestimate', [App\Http\Controllers\CostController::class, 'costestimate']);
Route::post('/costestimatemultipoint', [CostController::class, 'costEstimateMultiPoint']);  // <-- Add this line
Route::post('/getmultipointroutedetails', [CostController::class, 'getMultiPointRouteDetails']);
// 樂虎司機住宿查詢
Route::get('/driver-accommodations', [DriverAccommodationController::class, 'search']);

// 价格查询 API
Route::post('/price', [PriceController::class, 'getPrice']);
Route::post('/carorder/v3', [App\Http\Controllers\api\ReserveController::class, 'carorderv3']);

// 安興問卷
Route::post('/questionnaire', [App\Http\Controllers\api\ReserveController::class, 'storeQuestionnaire']);
