<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reserves', function (Blueprint $table) {
            $table->id();
            $table->string('reserve_id')->nullable()->comment('預約單號');
            $table->string('customer_name')->nullable()->comment('客戶名稱');
            $table->string('customer_mobile')->nullable()->comment('客戶手機');
            $table->unsignedSmallInteger('type')->default(0)->comment('預約類別');
            $table->string('flight_no')->nullable()->comment('航班編號');
            $table->unsignedSmallInteger('no_of_passengers')->default(0)->comment('乘車人數');
            $table->unsignedSmallInteger('no_of_bags')->default(0)->comment('行李數量');
            $table->string('vehicle_type')->nullable()->comment('租用車型');
            $table->unsignedSmallInteger('charter_hours')->nullable()->comment('包車時數');
            $table->date('reserve_date')->nullable()->comment('用車日期');
            $table->time('reserve_time')->nullable()->comment('用車時間');
            $table->string('boarding_address')->nullable()->comment('上車地址');
            $table->string('drop_off_address')->nullable()->comment('下車地址');
            $table->unsignedTinyInteger('status')->default(0)->comment('派車狀態');
            $table->unsignedSmallInteger('driver_id')->default(0)->comment('指派司機');
            $table->string('note')->nullable()->comment('備註說明');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reserves');
    }
};
