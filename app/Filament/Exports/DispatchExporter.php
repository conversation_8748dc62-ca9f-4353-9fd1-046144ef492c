<?php

namespace App\Filament\Exports;

use Carbon\Carbon;
use App\Models\Dispatch;
use Filament\Actions\Exports\Exporter;
use Illuminate\Database\Eloquent\Model;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Models\Export;

class DispatchExporter extends Exporter
{
    protected static ?string $model = Dispatch::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('start_date')
                ->label('日期')
                ->formatStateUsing(function (Model $record) {
                    return Carbon::parse($record['start_time'])->format('Y-m-d');
                }),
            // ExportColumn::make('start_date2')
            //     ->label('時間(起)')
            //     ->formatStateUsing(function (Model $record) {
            //         return Carbon::parse($record['start_date'])->format('H:i');
            //     }),
            ExportColumn::make('customer_address')
                ->label('乘車貴賓'),
            ExportColumn::make('driver.name')
                ->label('司機'),
            ExportColumn::make('route')
                ->label('路程'),
            ExportColumn::make('rental_cost')
                ->label('車資'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your dispatch export has completed and ' . number_format($export->successful_rows) . ' ' . str('row')->plural($export->successful_rows) . ' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to export.';
        }

        return $body;
    }
}
