<?php

namespace App\Filament\Exports;

use Filament\Exports\Exporter;
use App\Models\Quotation;

class QuotationExporter extends Exporter
{
    public static function getModel(): string
    {
        return Quotation::class;
    }

    public function getColumns(): array
    {
        return [
            'id',
            'status',
            'order_type',
            'car_type',
            'passenger_name',
            'passenger_mobile',
            'passenger_address',
            'appointment_date',
            'location_from_name',
            'location_city_name',
            'location_district_name',
            'num_of_people',
            'num_of_bags',
            'total',
        ];
    }
}
