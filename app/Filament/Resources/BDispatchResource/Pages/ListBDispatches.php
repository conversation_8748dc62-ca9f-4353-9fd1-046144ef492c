<?php

namespace App\Filament\Resources\BDispatchResource\Pages;

use App\Filament\Resources\BDispatchResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBDispatches extends ListRecords
{
    protected static string $resource = BDispatchResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
