<?php

namespace App\Filament\Resources\YatanResource\Pages;

use App\Filament\Resources\YatanResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListYatans extends ListRecords
{
    protected static string $resource = YatanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
