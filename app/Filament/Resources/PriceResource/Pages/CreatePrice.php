<?php

namespace App\Filament\Resources\PriceResource\Pages;

use App\Models\City;
use App\Models\Price;
use Filament\Actions;
use Illuminate\Support\Arr;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use App\Filament\Resources\PriceResource;
use Filament\Resources\Pages\CreateRecord;

class CreatePrice extends CreateRecord
{
    protected static string $resource = PriceResource::class;
    protected static ?string $title = '建立價格';
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function handleRecordCreation(array $data): Model
    {
        Log::debug('Data received in handleRecordCreation:', $data); // <--- 加入日誌
        $selectedCarTypes = Arr::wrap($data['car_type'] ?? []); // 確保是陣列
        $selectedFromDistricts = Arr::wrap($data['from_district_id'] ?? []); // 確保是陣列
        $selectedToDistricts = Arr::wrap($data['to_district_id'] ?? []); // 確保是陣列
        $priceValue = $data['price'] ?? null;

        // 提取其他共用數據 (排除掉會變動的欄位)
        $commonData = collect($data)->except([
            'car_type',
            'from_district_id', 'from_district_name', // from_district_name 會在迴圈中獲取
            'to_district_id', 'to_district_name'     // to_district_name 會在迴圈中獲取
        ])->all();

        // 基本驗證
        if (empty($selectedCarTypes) || empty($selectedFromDistricts) || empty($selectedToDistricts) || $priceValue === null) {
            Notification::make()
                ->title('缺少必要資訊')
                ->body('請確保已選擇車型、機場、目的地鄉鎮區，並填寫了價格。')
                ->danger()
                ->send();
            // 拋出異常阻止創建
            throw new \Exception('缺少必要資訊，無法進行批量新增。');
        }

        $createdCount = 0;
        $skippedCount = 0;
        $errorCount = 0;
        $lastCreatedModel = null;

        // --- 預先查詢名稱以減少資料庫查詢次數 ---
        $fromDistrictNames = City::whereIn('district_code', $selectedFromDistricts)
                                ->whereNull('area_code')->whereNull('city_code')
                                ->pluck('city', 'district_code');
        $toDistrictNames = City::whereIn('district_code', $selectedToDistricts)
                               ->pluck('city', 'district_code');
        // --- 查詢結束 ---


        // --- 三層迴圈生成所有組合 ---
        foreach ($selectedCarTypes as $carType) {
            foreach ($selectedFromDistricts as $fromDistrictId) {
                $fromDistrictName = $fromDistrictNames->get($fromDistrictId);
                if (!$fromDistrictName) {
                    Log::warning("找不到出發地名稱 for district_code: {$fromDistrictId}");
                    continue; // 跳過無效的出發地
                }

                foreach ($selectedToDistricts as $toDistrictId) {
                    $toDistrictName = $toDistrictNames->get($toDistrictId);
                    if (!$toDistrictName) {
                        Log::warning("找不到目的地名稱 for district_code: {$toDistrictId}");
                        continue; // 跳過無效的目的地
                    }

                    // 組合當前循環的數據
                    $recordData = array_merge($commonData, [
                        'car_type' => $carType,
                        'from_district_id' => $fromDistrictId,
                        'from_district_name' => $fromDistrictName,
                        // to_area_id, to_area_name, to_city_id, to_city_name 已經在 $commonData 中
                        'to_district_id' => $toDistrictId,
                        'to_district_name' => $toDistrictName,
                        'price' => $priceValue,
                        'status' => 0, // 確保 status 正確
                    ]);

                    try {
                        // --- 可選：檢查重複 ---
                        // 根據你的唯一性規則調整 where 條件
                        $existing = Price::where('vendor_id', $recordData['vendor_id'])
                                        ->where('order_type', $recordData['order_type'])
                                        ->where('car_type', $recordData['car_type'])
                                        ->where('from_district_id', $recordData['from_district_id'])
                                        ->where('to_area_id', $recordData['to_area_id'])
                                        ->where('to_city_id', $recordData['to_city_id'])
                                        ->where('to_district_id', $recordData['to_district_id'])
                                        ->whereNull('deleted_at') // 確保檢查未刪除的
                                        ->first();

                        if ($existing) {
                            Log::info("價格記錄已存在，跳過: ", $recordData);
                            $skippedCount++;
                            continue; // 如果已存在，跳過此組合
                        }
                        // --- 檢查重複結束 ---

                        // 創建記錄
                        $createdModel = static::getModel()::create($recordData);
                        $lastCreatedModel = $createdModel; // 記錄最後一個創建的模型
                        $createdCount++;
                        Log::info("成功新增價格記錄: ", $recordData);

                    } catch (\Exception $e) {
                        Log::error("新增價格記錄失敗: " . $e->getMessage(), $recordData);
                        $errorCount++;
                        // 可選擇是否在這裡發送單筆錯誤通知
                    }
                } // end foreach to_district
            } // end foreach from_district
        } // end foreach car_type

        // --- 發送最終通知 ---
        $notificationBody = [];
        if ($createdCount > 0) {
            $notificationBody[] = "成功新增 {$createdCount} 筆價格記錄。";
        }
        if ($skippedCount > 0) {
            $notificationBody[] = "跳過 {$skippedCount} 筆已存在的記錄。";
        }
        if ($errorCount > 0) {
            $notificationBody[] = "{$errorCount} 筆記錄新增失敗，請檢查日誌。";
        }

        if (empty($notificationBody)) {
             Notification::make()
                ->title('未執行任何操作')
                ->body('沒有符合條件的價格記錄需要新增。')
                ->warning()
                ->send();
        } else {
            Notification::make()
                ->title('批量新增完成')
                ->body(implode("\n", $notificationBody))
                ->success($errorCount == 0) // 如果有錯誤則不是 success
                ->danger($errorCount > 0)   // 如果有錯誤則是 danger
                ->warning($createdCount == 0 && $errorCount == 0) // 只有跳過是 warning
                ->send();
        }

        // --- 返回 Model ---
        // 即使批量創建，Filament 仍期望返回一個 Model 實例
        // 返回最後一個成功創建的，或者如果全部失敗/跳過，返回一個新的空實例
        return $lastCreatedModel ?? new (static::getModel());
    }

    // 可選：禁用預設的單筆創建成功通知
    // protected function getCreatedNotification(): ?Notification
    // {
    //     return null;
    // }
}
