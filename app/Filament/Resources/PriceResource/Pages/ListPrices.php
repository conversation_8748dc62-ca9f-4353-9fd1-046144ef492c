<?php

namespace App\Filament\Resources\PriceResource\Pages;

use Filament\Actions;
use Filament\Facades\Filament;
use App\Filament\Resources\PriceResource;
use Filament\Resources\Pages\ListRecords;

class ListPrices extends ListRecords
{
    protected static string $resource = PriceResource::class;
    protected static ?string $title = '價格列表';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('新增價格')
                ->visible(function () {
                    // 取得當前登入使用者的 vendor_id
                    $userVendorId = Filament::auth()->user()->vendor_id;

                    // 只有當 vendor_id 為 0 或 10 時才顯示按鈕
                    return in_array($userVendorId, [null, 10]);
                }),
        ];
    }
}
