<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\Questionnaire;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\QuestionnaireResource\Pages;
use App\Filament\Resources\QuestionnaireResource\RelationManagers;

class QuestionnaireResource extends Resource
{
    protected static ?string $model = Questionnaire::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = '問卷管理';
    public static function canViewAny(): bool
    {
        // Log::info('canViewAny00--'.auth()->user()->id);
        if(Filament::auth()->user()->id==1 || Filament::auth()->user()->id==15){
            return true;
        }
        return false;
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')->sortable()->searchable()->label('ID'),
                Tables\Columns\TextColumn::make('nickname')->searchable()->label('暱稱'),
                Tables\Columns\TextColumn::make('booker_name')->searchable()->label('預訂人姓名'),
                Tables\Columns\TextColumn::make('ride_date')->date()->sortable()->label('乘車日期'),
                Tables\Columns\TextColumn::make('customer_service_rating')->sortable()->label('客服評分'),
                Tables\Columns\TextColumn::make('driver_service_rating')->sortable()->label('司機評分'),
                Tables\Columns\TextColumn::make('vehicle_condition_rating')->sortable()->label('車輛狀況評分'),
                Tables\Columns\TextColumn::make('vehicle_cleanliness_rating')->sortable()->label('車輛清潔評分'),
                Tables\Columns\TextColumn::make('has_recommended_ansing')->label('是否推薦安興'), // 欄位名稱 'has_recommended_ansing' 保持不變
                Tables\Columns\TextColumn::make('likelihood_to_recommend')->sortable()->label('推薦可能性'),
                Tables\Columns\TextColumn::make('how_heard_about_ansing')->label('如何得知安興'), // 欄位名稱 'how_heard_about_ansing' 保持不變
                Tables\Columns\TextColumn::make('booking_channel_awareness')->label('訂車渠道知曉度'),
                Tables\Columns\TextColumn::make('additional_feedback')->limit(50)->tooltip(fn ($record) => $record->additional_feedback)->label('其他回饋'),
                // Tables\Columns\TextColumn::make('vendor_id')->sortable()->searchable()->label('廠商ID')->hidden(), // 隱藏廠商ID
                Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable()->label('建立時間'),
            ])
            ->filters([
                //
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQuestionnaires::route('/'),
            // 'create' => Pages\CreateQuestionnaire::route('/create'),
            // 'edit' => Pages\EditQuestionnaire::route('/{record}/edit'),
        ];
    }
}
