<?php

namespace App\Filament\Resources;

use Carbon\Carbon;
use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use App\Models\ADispatch;
use Filament\Tables\Table;
use Illuminate\Support\Js;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Repeater;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\ADispatchResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\ADispatchResource\RelationManagers;

class ADispatchResource extends Resource
{
    protected static ?string $model = ADispatch::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = '空白簽單管理';
    protected static ?string $navigationLabel = '車輛派車單管理';
    protected static ?int $navigationSort = 4;

    public static function canViewAny(): bool
    {
        // Log::info('canViewAny00--'.Filament::auth()->user()->id);
        if(Filament::auth()->user()->id==1){
            return true;
        }
        return false;
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('dispatch_no')
                    ->label('派車單號')
                    ->disabled()
                    ->required(),
                    Forms\Components\Select::make('status')
                    ->label('狀態')
                    ->options([
                        '未指派' => '未指派',
                        '已指派' => '已指派',
                        '已出發' => '已出發',
                        '客上' => '客上',
                        '簽名' => '簽名',
                        '已完成' => '已完成',
                        '已取消' => '已取消',
                    ])
                    ->required(),
                Forms\Components\Select::make('service_type')
                    ->label('服務類別')
                    ->options([
                        '接機' => '接機',
                        '送機' => '送機',
                        '包車' => '包車',
                        '其他' => '其他',
                    ])
                    ->required(),
                Forms\Components\TextInput::make('passenger')
                    ->label('乘客姓名')
                    ->required(),
                Forms\Components\TextInput::make('mobile')
                    ->label('乘客手機')
                    ->tel()
                    ->required(),
                Forms\Components\DateTimePicker::make('start_date')
                    ->label('預約日期/時間')
                    ->displayFormat('Y-m-d H:i') // Hide seconds
                    ->seconds(false) // Hide seconds
                    ->minutesStep(10) // 10-minute intervals
                    ->required(),
                Forms\Components\TextInput::make('start_location')
                    ->label('出發地點')
                    ->required(),
                Forms\Components\TextInput::make('end_location')
                    ->label('目的地')
                    ->required(),
                Forms\Components\TextInput::make('cost')
                    ->label('費用')
                    ->numeric()
                    ->required(),
                Forms\Components\Select::make('paytype')
                    ->label('付款方式')
                    ->options([
                        '1' => '現金',
                        '2' => '信用卡',
                        '3' => '轉帳',
                    ])
                    ->default('1')
                    ->required(),
                Forms\Components\Textarea::make('note')
                    ->label('備註')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('adriver_name')
                    ->label('司機姓名'),
                Forms\Components\TextInput::make('adriver_mobile')
                    ->label('司機電話')
                    ->tel(),
                Forms\Components\TextInput::make('adriver_car_license')
                    ->label('車號'),
                    // --- START: 新增 Repeater 管理額外乘客 ---
                Forms\Components\Repeater::make('passenger_multi') // <--- 名稱對應 JSON 欄位
                    ->label('其他乘客資訊') // 標籤明確指出是額外的
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('姓名')
                            ->required() // 額外乘客也需要姓名
                            ->maxLength(255),
                        Forms\Components\TextInput::make('mobile')
                            ->label('手機')
                            ->tel()
                            ->required() // 額外乘客也需要手機
                            ->maxLength(20),
                    ])
                    ->columns(2)
                    ->minItems(0) // <--- 允許沒有額外乘客
                    ->default([]) // <--- 預設為空陣列
                    ->addActionLabel('新增其他乘客')
                    ->reorderable(false)
                    ->cloneable()
                    ->deleteAction(
                        fn (\Filament\Forms\Components\Actions\Action $action) => $action->requiresConfirmation(),
                    )
                    ->columnSpanFull(),
                // --- END: 新增 Repeater 管理額外乘客 ---
            ]);
    }
    /**
     * 在儲存資料前修改表單資料。
     * 確保 passenger_multi 中的每個項目都有 id, name, mobile, signature_path。
     *
     * @param  array  $data 表單送出的原始資料
     * @return array 修改後的資料
     */
    public static function mutateFormDataBeforeSave(array $data): array
    {
        Log::debug('ADispatchResource::mutateFormDataBeforeSave executed!', ['data_keys' => array_keys($data)]);

        $mainPassengerName = $data['passenger'] ?? null;
        $mainPassengerMobile = $data['mobile'] ?? null;
        $repeaterPassengers = $data['passenger_multi'] ?? [];
        if (!is_array($repeaterPassengers)) {
            $repeaterPassengers = [];
        }

        // --- Step 3 Refined: 確保主要乘客項目包含所有欄位 ---
        $currentMainPassengerEntry = [
            'id' => 'main_' . uniqid(), // 為主要乘客生成唯一的 ID
            'name' => $mainPassengerName,
            'mobile' => $mainPassengerMobile,
            'signature_path' => null, // 新增 signature_path，預設為 null
        ];

        $isMainPassengerAlreadyFirst = false;
        if (!empty($repeaterPassengers) && isset($repeaterPassengers[0])) {
            $firstPassengerInRepeater = $repeaterPassengers[0];
            // 檢查 Repeater 的第一筆是否代表主要乘客 (基於姓名和手機)
            if (($firstPassengerInRepeater['name'] ?? null) === $mainPassengerName &&
                ($firstPassengerInRepeater['mobile'] ?? null) === $mainPassengerMobile) {
                $isMainPassengerAlreadyFirst = true;
            }
        }

        $processedPassengers = []; // 用於儲存處理過的乘客陣列

        if ($isMainPassengerAlreadyFirst) {
            // 主要乘客已是第一筆，更新其資料並確保結構完整
            $repeaterPassengers[0]['id'] = $repeaterPassengers[0]['id'] ?? 'main_' . uniqid(); // 如果 repeater 來的沒有 id，補上
            $repeaterPassengers[0]['name'] = $mainPassengerName;
            $repeaterPassengers[0]['mobile'] = $mainPassengerMobile;
            // signature_path 可能從 repeater 來，如果沒有則設為 null
            $repeaterPassengers[0]['signature_path'] = $repeaterPassengers[0]['signature_path'] ?? null;
            Log::debug('Main passenger already first in repeater, updated its data and ensured structure.');
            $processedPassengers = $repeaterPassengers;
        } else {
            // 主要乘客不是第一筆，將其加到最前面
            array_unshift($repeaterPassengers, $currentMainPassengerEntry);
            Log::debug('Prepended main passenger to repeater data.');
            $processedPassengers = $repeaterPassengers;
        }

        // --- 新增步驟：遍歷所有乘客，確保結構一致 ---
        $finalPassengerMulti = array_map(function ($passenger) {
            // 確保 'id' 存在，如果不存在則生成一個 (非主要乘客用不同前綴)
            $passenger['id'] = $passenger['id'] ?? 'passenger_' . uniqid();

            // 確保 'name' 存在 (雖然 Repeater 通常會強制)
            $passenger['name'] = $passenger['name'] ?? null;

            // 確保 'mobile' 存在 (雖然 Repeater 通常會強制)
            $passenger['mobile'] = $passenger['mobile'] ?? null;

            // 確保 'signature_path' 存在，如果不存在則設為 null
            $passenger['signature_path'] = $passenger['signature_path'] ?? null;

            return $passenger;
        }, $processedPassengers); // 使用處理過的陣列

        // --- Step 6: 過濾掉 name 和 mobile 都為空的項目 ---
        $finalPassengerMulti = array_filter($finalPassengerMulti, function($passenger) {
            return !empty(trim($passenger['name'] ?? '')) || !empty(trim($passenger['mobile'] ?? ''));
        });
        $finalPassengerMulti = array_values($finalPassengerMulti); // 重新索引陣列

        // --- Step 7 & 8 ---
        $data['passenger_multi'] = $finalPassengerMulti;
        Log::debug('Final passenger_multi before save:', ['data' => $data['passenger_multi']]);
        return $data;
    }
    public static function table(Table $table): Table
    {
        return $table
            ->poll('10s')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('編號')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\SelectColumn::make('status')
                    ->label('狀態')
                    ->sortable()
                    ->searchable()
                    // ->badge()
                    ->options([
                        '未指派' => '未指派',
                        '已指派' => '已指派',
                        '已出發' => '已出發',
                        '客上' => '客上',
                        '簽名' => '簽名',
                        '已完成' => '已完成',
                        '已取消' => '已取消',
                    ])
                    ->extraAttributes(function ($record) {
                        // 先設置 data-status 屬性
                        $attributes = ['data-status' => $record->status, 'class' => 'status-badge status-' . $record->status];

                        // 然後根據狀態添加樣式
                        if ($record->status == '已指派') {
                            $attributes['style'] = 'background-color: #aaaaaa;'; // 更深的紅色
                        } elseif ($record->status == '已出發') {
                            $attributes['style'] = 'background-color: #88ff88;'; // 更深的綠色
                        } elseif ($record->status == '未指派') {
                            $attributes['style'] = 'background-color: #ff8888;'; // 更深的灰色
                        } elseif ($record->status == '客上') {
                            $attributes['style'] = 'background-color: #88aaff;'; // 更深的藍色
                        } elseif ($record->status == '簽名') {
                            $attributes['style'] = 'background-color: #ff4d4d;'; // 更深的紅色
                        } elseif ($record->status == '已完成') {
                            $attributes['style'] = 'background-color: #99e600;'; // 更深的綠色
                        } elseif ($record->status == '已取消') {
                            $attributes['style'] = 'background-color: #ff8888;'; // 更深的紅色
                        }

                        return $attributes; // 返回包含 data-status 和樣式的屬性數組
                    })
                    // ->color(fn (string $state): string => match ($state) {
                    //     '未指派' => 'info',
                    //     '已指派' => 'info',
                    //     '已出發' => 'info',
                    //     '客上' => 'info',
                    //     '簽名' => 'info',
                    //     '已完成' => 'info',
                    //     '已取消' => 'info',
                    //     default => 'gray',
                    // })
                    // 不使用afterStateHydrated方法，因為它在當前版本的Filament中不存在
                    ,
                Tables\Columns\TextColumn::make('service_type')
                    ->label('服務類別')
                    ->sortable()
                    ->searchable(),
                    Tables\Columns\TextColumn::make('driver_info')
                    ->label('司機資訊')
                    ->getStateUsing(function (ADispatch $record): HtmlString {
                        $name = $record->adriver_name ?? 'N/A';
                        $mobile = $record->adriver_mobile ?? 'N/A';
                        $license = $record->adriver_car_license ?? 'N/A';
                        return new HtmlString("<div>{$name}</div><div>{$mobile}</div><div>{$license}</div>");
                    })
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->where('adriver_name', 'like', "%{$search}%")
                                     ->orWhere('adriver_mobile', 'like', "%{$search}%")
                                     ->orWhere('adriver_car_license', 'like', "%{$search}%");
                    })
                    ->html(),
                Tables\Columns\TextColumn::make('passenger')
                    ->label('乘客姓名')
                    ->getStateUsing(function (ADispatch $record): HtmlString {
                        return new HtmlString("<div>{$record->passenger}</div><div>{$record->mobile}</div>");
                    })
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->where('passenger', 'like', "%{$search}%")
                                     ->orWhere('mobile', 'like', "%{$search}%");
                    })
                    ->html(),
                Tables\Columns\TextColumn::make('start_date')
                    ->label('預約日期/時間')
                    ->getStateUsing(function (ADispatch $record): HtmlString {
                        // Log::info('預約日期/時間 ADispatch ID: ' . $record->id);
                        if ($record->start_date) {
                            $date = \Carbon\Carbon::parse($record->start_date)->format('Y-m-d');
                            $time = \Carbon\Carbon::parse($record->start_date)->format('H:i');
                            return new HtmlString("<div>{$date}</div><div>{$time}</div>");
                        }
                        return new HtmlString('');
                    })
                    ->sortable()
                    ->searchable()
                    ->html(),
                Tables\Columns\TextColumn::make('location_info')
                    ->label('起訖地點')
                    ->getStateUsing(function (ADispatch $record): HtmlString {
                        // Log::info('ADispatch ID: ' . $record->id);
                        // Log::info('start_location: ' . $record->start_location);
                        // Log::info('end_location: ' . $record->end_location);
                        $startLocation = $record->start_location ? "<div>{$record->start_location}</div>" : '';
                        $endLocation = $record->end_location ? "<div>{$record->end_location}</div>" : '';
                        return new HtmlString($startLocation . $endLocation);
                    })
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->where('start_location', 'like', "%{$search}%")
                                     ->orWhere('end_location', 'like', "%{$search}%");
                    })
                    ->html(),
                Tables\Columns\TextInputColumn::make('cost') // Changed from TextColumn to TextInputColumn
                    ->label('費用')
                    ->sortable()
                    // ->width('60px') // Removed width setting
                    // ->extraInputAttributes(['style' => 'width: 60px; min-width: 60px;']) // Removed input attribute setting
                    ->extraCellAttributes(['style' => 'width: 60px; min-width: 60px;']) // Set cell width instead
                    ->extraAttributes(['class' => 'cost-column-input']) // Add custom class for CSS targeting
                    ->searchable(),
                Tables\Columns\TextColumn::make('note')
                    ->label('備註')
                    ->sortable()
                    ->searchable(),

            ])
            ->defaultSort('start_date', 'desc')
            ->filters([
                //
            ])
            ->actions([
                // --- START: 將 Actions 放入 ActionGroup ---
                ActionGroup::make([
                    Tables\Actions\EditAction::make(), // 編輯保持不變

                    Tables\Actions\Action::make('copy-text') // 複製文字 Action
                        ->label('拷貝文字')
                        ->icon('heroicon-o-document-duplicate')
                        ->action(function ($livewire, $record) {
                            $orderBaseUrl = config('app.order_app_base_url');
                            $orderUrl = $orderBaseUrl . '?oid=' . $record->id;
                            $formattedDate = $record->start_date ? Carbon::parse($record->start_date)->format('Y-m-d H:i') : '';

                            $passengerList = '';
                            if (!empty($record->passenger) || !empty($record->mobile)) {
                                $passengerList .= "乘客: " . e($record->passenger ?? '') . " (" . e($record->mobile ?? '') . ") [主要]\n";
                            }
                            if (is_array($record->passenger_multi) && !empty($record->passenger_multi)) {
                                $additionalList = collect($record->passenger_multi)->map(function ($p) {
                                    return "乘客: " . e($p['name'] ?? '') . " (" . e($p['mobile'] ?? '') . ")";
                                })->implode("\n");
                                $passengerList .= $additionalList . "\n";
                            }
                            $passengerList = rtrim($passengerList, "\n");

                            $rawText = "預約日期時間: " . $formattedDate . "\n" .
                                       "服務類別: " . $record->service_type . "\n" .
                                       ($passengerList ? $passengerList . "\n" : '') .
                                       "上車地點: " . e($record->start_location) . "\n" .
                                       "下車地點: " . e($record->end_location) . "\n" .
                                       "車資: " . $record->cost . "\n" .
                                       ($record->note ? "備註: " . e($record->note) . "\n" : '') .
                                       "承接案件請點擊連結 " . "\n" .
                                        $orderUrl . "\n" ;

                            $textToCopy = str_replace("\n", "\\n", trim($rawText));
                            $tooltipStr = empty(trim($rawText)) ? '目前無文字內容' : '已拷貝到剪貼簿中!';
                            $livewire->js(
                                'window.navigator.clipboard.writeText("'.$textToCopy.'");
                                $tooltip("'.__($tooltipStr).'", { timeout: 1500 });'
                            );
                        }),

                    Tables\Actions\Action::make('clear-driver') // 清除司機 Action
                        ->label('清除司機資訊')
                        ->icon('heroicon-o-user-minus')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->modalHeading('確認清除司機資訊')
                        ->modalSubheading('您確定要將此派車單狀態改為「未指派」並清除所有司機相關資訊嗎？此操作無法復原。')
                        ->modalButton('確認清除')
                        ->action(function (ADispatch $record) {
                            $record->status = '未指派';
                            $record->adriver_id = null;
                            $record->adriver_lineid = null;
                            $record->adriver_name = null;
                            $record->adriver_mobile = null;
                            $record->adriver_car_license = null;
                            $record->adriver_car_type = null;
                            $record->save();
                            Notification::make()
                                ->title('司機資訊已清除')
                                ->success()
                                ->send();
                        }),
                    Tables\Actions\Action::make('clear-passenger-info')
                        ->label('清除乘車資訊')
                        ->icon('heroicon-o-user-minus')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->modalHeading('確認清除乘車資訊')
                        ->modalSubheading('您確定要將此派車單的乘車相關時間全部清空嗎？此操作無法復原。')
                        ->modalButton('確認清除')
                        ->action(function (ADispatch $record) {
                            $record->outset = null;
                            $record->on_the_car = null;
                            $record->off_the_car = null;
                            $record->signature_time = null;
                            $record->save();
                            Notification::make()
                                ->title('乘車資訊已清除')
                                ->success()
                                ->send();
                        }),
                ])
                ->tooltip('更多操作') // 可以給 ActionGroup 加上提示文字
                ->icon('heroicon-m-ellipsis-vertical'), // 可以指定 ActionGroup 的圖示
                // --- END: 將 Actions 放入 ActionGroup ---
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListADispatches::route('/'),
            'create' => Pages\CreateADispatch::route('/create'),
            'edit' => Pages\EditADispatch::route('/{record}/edit'),
        ];
    }
}
