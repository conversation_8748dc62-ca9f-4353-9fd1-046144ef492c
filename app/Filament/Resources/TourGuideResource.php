<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use App\Models\TourGuide;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\TourGuideResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\TourGuideResource\RelationManagers;

class TourGuideResource extends Resource
{
    protected static ?string $model = TourGuide::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = '旅遊管理';
    protected static ?string $navigationLabel = '導遊管理';
    protected static ?int $navigationSort = 4;
    public static function canViewAny(): bool
    {
        // Log::info('canViewAny00--'.auth()->user()->id);
        if(auth()->user()->id==1 || auth()->user()->id==2 || auth()->user()->id==3){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', auth()->user()->vendor_id);
        }
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make()->schema([
                    Section::make('基本資料')->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('姓名')
                            ->required(),
                        Forms\Components\TextInput::make('mobile')
                            ->label('連絡電話')
                            ->required(),
                        Forms\Components\Radio::make('sex')
                            ->label('性別')
                            ->inline()
                            ->default(0)
                            ->options([
                                0 => '女性',
                                1 => '男性',
                            ])
                            ->required(),
                        Forms\Components\DatePicker::make('birthday')
                            ->label('出生年月日'),
                        Forms\Components\TextInput::make('address')
                            ->label('聯絡地址'),
                        Forms\Components\Textarea::make('note')
                            ->label('備註'),
                    ])->columns(2),
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('mobile')
                    ->searchable(),
                Tables\Columns\TextColumn::make('sex')
                    ->searchable(),
                Tables\Columns\TextColumn::make('birthday')
                    ->searchable(),
                Tables\Columns\TextColumn::make('address')
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTourGuides::route('/'),
            'create' => Pages\CreateTourGuide::route('/create'),
            'edit' => Pages\EditTourGuide::route('/{record}/edit'),
        ];
    }
}
