<?php

namespace App\Filament\Resources\VendorResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Resources\RelationManagers\RelationManager;

class ContractRelationManager extends RelationManager
{
    protected static string $relationship = 'contract';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('contract_id')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('type')
                    ->label('方案')
                    ->placeholder('請選擇')
                    ->options([
                        'A' => 'A',
                        'B' => 'B',
                        'C' => 'C',
                    ]),
                Forms\Components\TextInput::make('fee')
                    ->maxLength(255),
                Forms\Components\Select::make('status')
                    ->label('狀態')
                    ->placeholder('請選擇')
                    ->options([
                        '0' => '未付款',
                        '1' => '以收款',
                    ]),
                Forms\Components\DatePicker::make('start_date')
                    ->format('Y-m-d'),
                Forms\Components\DatePicker::make('end_date')
                    ->format('Y-m-d'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('contract_id')
            ->columns([
                Tables\Columns\TextColumn::make('contract_id'),
                Tables\Columns\TextColumn::make('vendor.title'),
                Tables\Columns\TextColumn::make('start_date')
                    ->date(),
                Tables\Columns\TextColumn::make('end_date')
                    ->date(),
                Tables\Columns\TextColumn::make('fee'),
                Tables\Columns\TextColumn::make('status')
                    ->getStateUsing( function (Model $record){
                        if($record['status'] == 1){
                            return '已付款';
                        }else{
                            return '未付款';
                        }
                    }),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
