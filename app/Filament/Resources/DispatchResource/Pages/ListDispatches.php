<?php

namespace App\Filament\Resources\DispatchResource\Pages;

use Filament\Resources\Pages\ListRecords;
use App\Filament\Resources\DispatchResource;

class ListDispatches extends ListRecords
{
    protected static string $resource = DispatchResource::class;
    protected static ?string $title = '派車單列表';

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return
        [
            // url('/') => '首頁',
            url('/admin/dispatches') => '派車單',
            url('/admin/dispatches#') => '派車單列表',
        ];
    }
}
