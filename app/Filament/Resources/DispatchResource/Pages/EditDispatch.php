<?php

namespace App\Filament\Resources\DispatchResource\Pages;

use App\Models\Car;
use Filament\Actions;
use App\Models\Driver;
use App\Models\Dispatch;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\DispatchResource;

class EditDispatch extends EditRecord
{
    protected static string $resource = DispatchResource::class;
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    public function handleRecordUpdate(Model $record, array $data):Dispatch
    {
        // dd($record);
        if($record->vendor_id == 7){
            // 雅潭
            $driver = Driver::where('id',  $record->driver_id)->first();
            $car = Car::where('driver_id', $record->driver_id)->first();
            $record->route = $data['route'];
            $record->start_time = $data['start_time'];
            $record->rental_cost = $data['rental_cost'];
            $record->driver_name = $driver->name;
            $record->car_license = $car->car_license;
            $record->regular = $data['regular'];
            $record->customer_department = $record['customer_address'];
            $rs = $this->updateYatanImage($record);
            if ($rs === null) {
                // Log the error or handle it in another way
                Log::error('圖片產生失敗', ['dispatch_id' => $record->dispatch_id]);
                // Option 1: Do not update image path.
                // Option 2: set the image_path to placeholder
                // $rs = 'images/placeholder.jpg'
            }else{
                $record->image_path = $rs;
            }
            $record->image_path = $rs;
            unset($record->driver_name);
            unset($record->customer_department);
            unset($record->car_license);
            $record->save();
        }
        // dd($record);
        return $record;
    }
    protected function updateYatanImage($data)
    {
        $mydate = explode(" ", $data['start_time']);
        // dd($mydate);
        $myDateStr = $mydate[0];
        if($mydate[1]=='06:00:00'){
            $myTimeStr = '日 DAY  06:00~23:00';
        }else{
            $myTimeStr = '夜 NIGHT  23:00~06:00';
        }
        // $myTimeStr = $data['start_time'];
        // $mydate = (string)$input['start_date'] . ' ' . $hours.':'.$minutes;
        $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_7.jpg'));
        if(!empty($data['signature_file'])){
            $signature = imagecreatefrompng(storage_path('app/public/' . $data['signature_file']));
            $signature_width = imagesx($signature);
            $signature_height = imagesy($signature);
            $new_width = $signature_width / 2;
            $new_height = $signature_height / 2;
            $resized_signature = imagecreatetruecolor($new_width, $new_height);
            imagealphablending($resized_signature, false);
            imagesavealpha($resized_signature, true);
            imagecopyresampled(
                $resized_signature,  // 目标图像资源
                $signature,          // 源图像资源
                0, 0,                // 目标图像的 x, y 坐标
                0, 0,                // 源图像的 x, y 坐标
                $new_width, $new_height,  // 目标图像的宽度和高度
                $signature_width, $signature_height  // 源图像的宽度和高度
            );
        }

        $fontPath = public_path('fonts/SimHei.ttf');
        $black = imagecolorallocate($background, 0, 0, 0);
        $dispatch_id_dest_x = 1500; // 合并时在背景图上的x坐标
        $dispatch_id_dest_y = 140; // 合并时在背景图上的y坐标
        $customer_name_dest_x = 1325; // 合并时在背景图上的x坐标
        $customer_name_dest_y = 410; // 合并时在背景图上的y坐标
        $customer_department_x = 1325; // 合并时在背景图上的x坐标
        $customer_department_y = 573; // 合并时在背景图上的y坐标
        $start_date_dest_x = 539; // 合并时在背景图上的x坐标
        $start_date_dest_y = 260; // 合并时在背景图上的y坐标
        $start_time_dest_x = 355; // 合并时在背景图上的y坐标
        $start_time_dest_y = 355; // 合并时在背景图上的y坐标
        $rental_cost_dest_x = 760; // 合并时在背景图上的y坐标
        $rental_cost_dest_y = 410; // 合并时在背景图上的y坐标
        $driver_name_dest_x = 780; // 合并时在背景图上的y坐标
        $driver_name_dest_y = 760; // 合并时在背景图上的y坐标
        $car_license_dest_x = 500; // 合并时在背景图上的y坐标
        $car_license_dest_y = 760; // 合并时在背景图上的y坐标
        $start_time_dest_x = 1330; // 合并时在背景图上的y坐标
        $start_time_dest_y = 260; // 合并时在背景图上的y坐标
        $route_start_dest_x = 530; // 合并时在背景图上的y坐标
        $route_start_dest_y = 550; // 合并时在背景图上的y坐标
        // $route_end_dest_x = 800; // 合并时在背景图上的y坐标
        // $route_end_dest_y = 550; // 合并时在背景图上的y坐标
        imagettftext($background, 24, 0, $dispatch_id_dest_x, $dispatch_id_dest_y, $black, $fontPath, $data['dispatch_id']);
        imagettftext($background, 32, 0, $customer_name_dest_x, $customer_name_dest_y, $black, $fontPath, $data['customer_name']);
        imagettftext($background, 40, 0, $customer_department_x, $customer_department_y, $black, $fontPath, $data['customer_department']);
        imagettftext($background, 40, 0, $start_date_dest_x, $start_date_dest_y, $black, $fontPath, $myDateStr);
        imagettftext($background, 36, 0, $rental_cost_dest_x, $rental_cost_dest_y, $black, $fontPath, $data['rental_cost']);
        imagettftext($background, 40, 0, $driver_name_dest_x, $driver_name_dest_y, $black, $fontPath, $data['driver_name']);
        imagettftext($background, 40, 0, $car_license_dest_x, $car_license_dest_y, $black, $fontPath, $data['car_license']);
        imagettftext($background, 30, 0, $start_time_dest_x, $start_time_dest_y, $black, $fontPath, $myTimeStr);
        if(strlen($data['route']) > 36){
            $y = $route_start_dest_y-30;
            $maxWidth = 400;
            $lines = $this->wrapText(20, 0, $fontPath, $data['route'], $maxWidth);
            foreach ($lines as $line) {
                imagettftext($background, 24, 0, $route_start_dest_x, $y, $black, $fontPath, $line);
                $y += 40;
            }
        }else{
            imagettftext($background, 30, 0, $route_start_dest_x, $route_start_dest_y, $black, $fontPath, $data['route']);
        }
        // imagettftext($background, 30, 0, $route_end_dest_x, $route_end_dest_y, $black, $fontPath, $data['route_end']);
        $outputFileName = $data['dispatch_id'] . '.jpg'; // 'DP_' . date("YmdHis") . Str::random(3) . '.jpg';
        $outputFilePath = public_path('images/' . $outputFileName);
        if(!empty($data['signature_file'])){
            imagecopy($background, $resized_signature, 1325, 633, 0, 0, $new_width,$new_height);
        }
        if (file_exists($outputFilePath)){
            @unlink($outputFilePath);
        }
        if(imagejpeg($background, $outputFilePath)){
            $result =  'images/' . $outputFileName;
        }else{
            $result = null;
        }
        imagedestroy($background);
        if(!empty($data['signature_file'])){
            imagedestroy($signature);
        }
        // imagedestroy($resized_signature);
        return $result;
    }
    public function wrapText($fontSize, $angle, $fontPath, $text, $maxWidth)
    {
        $lines = [];
        $line = "";
        for ($i = 0; $i < mb_strlen($text); $i++) {
            $char = mb_substr($text, $i, 1);
            $testString = $line . $char;
            $size = imagettfbbox($fontSize, $angle, $fontPath, $testString);
            if ($size[2] > $maxWidth && !empty($line)) {
                $lines[] = $line;
                $line = $char;
            }else{
                $line = $testString;
            }
        }
        $lines[] = $line;
        return $lines;
    }
    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
