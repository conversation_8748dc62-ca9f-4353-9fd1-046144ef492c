<?php

namespace App\Filament\Resources\DispatchResource\Widgets;

use Carbon\Carbon;
use App\Models\Dispatch;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class DispatchCountChart extends ChartWidget
{
    protected static ?string $heading = 'Chart';

    protected function getData(): array
    {
        if(auth()->user()->vendor_id==0){
            $data = Trend::model(Dispatch::class)
                ->between(
                    start: now()->addDays(-6),
                    end: now(),
                )
                ->perDay()
                ->count();
        }else{

            $data = Trend::query(
                    Dispatch::query()
                        ->where('vendor_id', '=', auth()->user()->vendor_id)
                )
                // ->where('vendor_id', '=', auth()->user()->vendor_id)
                ->between(
                    start: now()->addDays(-6),
                    end: now(),
                )
                ->perDay()
                ->count();
        }

        // dd($data);
        return [
            'labels' => $data->map(fn (TrendValue $item) => $item->date),
            'datasets' => [
                [
                    'label' => '最後7天派車數使用數',
                    'data' => $data->map(fn (TrendValue $item) => $item->aggregate),
                    'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'borderWidth' => 1,
                ],
            ],
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
