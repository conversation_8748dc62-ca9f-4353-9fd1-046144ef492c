<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\BlankDriver;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\BDriverResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\BDriverResource\RelationManagers;

class BDriverResource extends Resource
{
    protected static ?string $model = BlankDriver::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = '空白簽單管理';
    protected static ?string $navigationLabel = '司機管理';
    protected static ?int $navigationSort = 2;
    public static function canViewAny(): bool
    {
        // Log::info('canViewAny00--'.auth()->user()->id);
        if(Filament::auth()->user()->id==1){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(Filament::auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', Filament::auth()->user()->vendor_id);
        }
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('姓名')
                    ->searchable(),
                Tables\Columns\TextColumn::make('nick_name')
                    ->label('暱稱')
                    ->searchable(),
                Tables\Columns\TextColumn::make('mobile')
                    ->label('手機號碼')
                    ->searchable(),
                Tables\Columns\TextColumn::make('car_license')
                    ->label('車號')
                    ->searchable(),
                Tables\Columns\TextColumn::make('car_type')
                    ->label('車型')
                    ->searchable(),
                Tables\Columns\TextColumn::make('total_points')
                    ->label('總點數')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('建立時間')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                //
            ])
            ->defaultSort('created_at', 'desc')
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBDrivers::route('/'),
            'create' => Pages\CreateBDriver::route('/create'),
            'edit' => Pages\EditBDriver::route('/{record}/edit'),
        ];
    }
}
