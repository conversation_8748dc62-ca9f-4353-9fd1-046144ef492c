<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Driver;
use App\Models\Vendor;
use GuzzleHttp\Client;
use Filament\Forms\Form;
use Filament\Tables\Table;
use GuzzleHttp\Psr7\Request;
use Filament\Facades\Filament;
use LINE\Constants\HTTPHeader;
use Filament\Resources\Resource;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Group;
use Illuminate\Foundation\Auth\User;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Select;
use Illuminate\Database\Eloquent\Model;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Enums\ActionsPosition;
use Illuminate\Support\Facades\Notification;
use LINE\Clients\MessagingApi\Configuration;
use App\Filament\Resources\DriverResource\Pages;
use LINE\Clients\MessagingApi\Model\TextMessage;
use LINE\Clients\MessagingApi\Api\MessagingApiApi;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\DriverResource\RelationManagers;

class DriverResource extends Resource
{
    protected static ?string $model = Driver::class;
    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    protected static ?string $navigationLabel = '駕駛管理';

    public static function canViewAny(): bool
    {
        // Log::info('canViewAny00--'.auth()->user()->id);
        if(Filament::auth()->user()->id==10 || Filament::auth()->user()->id==11 || Filament::auth()->user()->id==15){
            return false;
        }
        return true;
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make()->schema([
                    Forms\Components\Fieldset::make('基本資料')
                        ->schema([

                            Forms\Components\TextInput::make('name')
                                ->label('姓名')
                                ->disabled()
                                ->required()
                                ->maxLength(255),
                            Forms\Components\Radio::make('sex')
                                ->label('性別')
                                ->disabled()
                                ->options([
                                    '1' => '男',
                                    '0' => '女',
                                ])
                                ->inline(),
                            Forms\Components\TextInput::make('address')
                                ->label('地址')
                                ->maxLength(255)
                                ->columnSpanFull(),
                            Forms\Components\TextInput::make('nickname')
                                ->label('暱稱')
                                ->disabled()
                                ->maxLength(255),
                            Forms\Components\TextInput::make('mobile')
                                ->label('手機')
                                ->disabled()
                                ->required()
                                ->maxLength(255),
                            Forms\Components\TextInput::make("person_id")
                                ->disabled()
                                ->label('職業駕照號碼'),
                            Forms\Components\TextInput::make("dealer_name")
                                ->disabled()
                                ->label('車行'),
                            Forms\Components\Select::make('is_active')
                                ->label('狀態')
                                ->placeholder('請選擇')
                                ->options([
                                    '0' => '未啟用',
                                    '1' => '審核通過',
                                    '2' => '審核未通過',
                                ])
                                ->afterStateUpdated(function ($record, Select $component, $state) {
                                    // dd($record);
                                    $user_line_id = $record->line_id;
                                    $channelToken = config('line.channel_access_token');
                                    $config = new Configuration();
                                    $config->setAccessToken($channelToken);
                                    $bot = new MessagingApiApi(new Client(), $config);

                                    if($state==1){
                                        Log::info('司機 line id '.json_encode($user_line_id));
                                        $driver = Driver::where('line_id', $user_line_id)->first();
                                        $vendor = Vendor::where('id', $driver->vendor_id)->first();
                                        Log::info('司機 DATA '.json_encode($driver));
                                        // $bot->linkRichMenuIdToUser($user_line_id, 'richmenu-b434072b7e79aa7d8912ad02cd130ff3');
                                        $bot->linkRichMenuIdToUser($user_line_id, $vendor->rm_id);
                                        $message = '審核通過，請開始使用電子派車單!';
                                        static::pushMsg($message, $user_line_id, $channelToken);
                                        // Log::info(print_r($response, true));
                                    }else if($state==0 || $state==2){
                                        Log::info('司機 解除綁定:: '.$user_line_id);
                                        // $message = '解除綁定，如有疑問，請與車行聯繫';
                                        // static::pushMsg($message, $user_line_id, $channelToken);
                                        $bot->unlinkRichMenuIdFromUser($user_line_id);
                                    }
                                }),
                                // ->hiddenOn('create')
                        ])
                ]),
                Group::make()->schema([
                    Forms\Components\Fieldset::make('車輛資訊')
                        ->relationship('car')
                        ->schema([
                            Forms\Components\TextInput::make("car_license")
                                // ->disabled()
                                ->label('車牌號碼'),
                            Forms\Components\TextInput::make("car_type")
                                // ->disabled()
                                ->label('廠牌型式'),

                        ]),
                ]),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        if(Filament::auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', Filament::auth()->user()->vendor_id);
        }
    }
    public static function table(Table $table): Table
    {
        $filters = [
            Filter::make('啟用')
                ->query(fn (Builder $query): Builder => $query->where('is_active', 1)),
            Filter::make('未啟用')
                ->query(fn (Builder $query): Builder => $query->where('is_active', 0))

        ];
        if(Filament::auth()->user()->role=='admin'){
            $filters[] = SelectFilter::make('vendor_id')
                ->label('廠商')
                ->options(Vendor::all()->pluck('title', 'id'));
        }
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('姓名')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('nickname')
                    ->label('暱稱')
                    ->searchable(),
                Tables\Columns\TextColumn::make('sex')
                    ->label('性別')
                    ->sortable()
                    ->getStateUsing( function (Model $record){
                        if($record['sex'] == 1){
                            return '男';
                        }else{
                            return '女';
                        }
                    }),
                Tables\Columns\TextColumn::make('mobile')
                    ->label('手機')
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('狀態')
                    // ->sortable()
                    ->icon(fn (string $state): string => match ($state) {
                        '0' => 'heroicon-s-x-mark',
                        '1' => 'heroicon-c-check',
                        '2' => 'heroicon-s-x-mark',
                    })
                    ->color(fn (string $state): string => match ($state) {
                        '0' => 'danger',
                        '1' => 'success',
                        '2' => 'danger',
                    })
                    // ->getStateUsing( function (Model $record){
                        //     // dd($record);
                        //     if($record['is_active'] == 1){
                            //         return '審核通過';
                            //     }else if($record['is_active'] == 2){
                                //         return '審核未通過';
                                //     }else{
                                    //         return '未啟用';
                                    //     }
                                    // })
                                    ,
                Tables\Columns\TextColumn::make('vendor.title')
                    ->label('車行')
                    ->sortable()
                    ->searchable()
                    ->formatStateUsing(fn($state) => $state ? mb_substr($state, 0, 2) : ''),
                Tables\Columns\TextColumn::make('car.car_license')
                    ->label('車牌')
                    // ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('建立時間')
                    // ->sortable()
                    ->searchable(),
            ])
            ->defaultSort('created_at', 'desc')
            ->striped()
            ->defaultPaginationPageOption(25)
            ->filters($filters)
            ->actions([
                Tables\Actions\EditAction::make()
                    ->color('danger')
                    ->label('編輯'),
            ], position: ActionsPosition::BeforeColumns)
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDrivers::route('/'),
            // 'create' => Pages\CreateDriver::route('/create'),
            'edit' => Pages\EditDriver::route('/{record}/edit'),
        ];
    }
    public static function canCreate(): bool
    {
        return false;
    }
    public static function pushMsg($message, $uid, $ChannelAccessToken):void
    {
        $Payload = [
            'to' => $uid,
            'messages' => [
                [
                    'type' => 'text',
                    'text' => $message
                ]
            ]
        ];

        // 傳送訊息
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.line.me/v2/bot/message/push');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($Payload));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $ChannelAccessToken
        ]);
        $Result = curl_exec($ch);
        curl_close($ch);
        // return $Result;
    }
}
