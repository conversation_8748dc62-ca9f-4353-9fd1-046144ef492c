<?php

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\City;
use Filament\Tables;
use App\Models\Price;
use App\Models\Vendor;
use App\Models\Cartype;
use Filament\Forms\Form;
use App\Models\Rentcarfee;
use Filament\Tables\Table;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Filament\Forms\Components\Grid;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Illuminate\Database\Eloquent\Model;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\PriceResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\PriceResource\RelationManagers;
use Filament\Forms\Get; // <--- 引入 Get
use Filament\Forms\Set; // <--- 引入 Set

class PriceResource extends Resource
{
    protected static ?string $model = Price::class;
    protected static ?string $navigationLabel = '價格管理';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    public static function canViewAny(): bool
    {
        // Log::info('canViewAny00--'.Filament::auth()->user()->id);
        if(Filament::auth()->user()->id==1 || Filament::auth()->user()->id==9 || Filament::auth()->user()->id==10 || Filament::auth()->user()->id==11 || Filament::auth()->user()->id==15){
            return true;
        }
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
        ->schema([
            Grid::make(2)->schema([
                Forms\Components\Select::make('vendor_id')
                    ->label('廠商')
                    ->options(function () {
                        // Fetch vendors - assuming 'title' is the display name and 'id' is the value
                        return Vendor::all()->pluck('title', 'id')->toArray();
                    })
                    ->required()
                    ->searchable() // Good for many vendors
                    ->placeholder('請選擇廠商')
                    ->visible(Filament::auth()->user()->id==1),
                Forms\Components\Hidden::make('vendor_id')
                    ->default(Filament::auth()->user()->vendor_id),
                Forms\Components\Radio::make('order_type')
                    ->label('類別')
                    ->options([
                        0 => '機場接送',
                        // 1 => '兩地接送', // 暫時只保留機場接送
                    ])
                    // ->afterStateUpdated(...) // 如果 order_type 固定為0，這個可能不需要了
                    ->default(0)
                    // ->reactive() // 如果只有一個選項，也不需要 reactive
                    ->required()
                    ->inline(),

                // --- 車型：新增頁面用 CheckboxList ---
                Forms\Components\CheckboxList::make('car_type')
                    ->label('選擇車型 (可複選)')
                    ->visibleOn('create') // 只在新增頁面顯示
                    ->options(function(){
                        // return Cartype::all()->pluck('name', 'code')->toArray();
                        return static::getCarTypeOptions(); // 使用你已有的方法
                    })
                    ->required()
                    ->columns(3)
                    ->bulkToggleable(), // 增加全選功能

                // --- 車型：編輯頁面用 Select (假設一次編輯一筆) ---
                Forms\Components\Select::make('car_type')
                    ->label('車型')
                    ->visibleOn('edit') // 只在編輯頁面顯示
                    ->options(function(){
                        return static::getCarTypeOptions();
                    })
                    ->required(),

            ]),

            // --- 出發地 (機場)：新增頁面用 CheckboxList ---
            Grid::make(1)->schema([ // 改成1欄，讓 CheckboxList 佔滿
                Forms\Components\CheckboxList::make('from_district_id')
                    ->label('選擇機場 (可複選)')
                    ->visibleOn('create') // 只在新增頁面顯示
                    ->options(
                        // 直接寫死四個機場選項
                        City::whereIn('district_code', [110900, 110903, 120901, 130901])
                            ->whereNull('area_code')
                            ->whereNull('city_code')
                            ->pluck('city', 'district_code')
                            ->toArray()
                    )
                    ->required()
                    ->columns(4) // 讓選項並排
                    ->bulkToggleable(), // 增加全選功能
            ]),

            // --- 出發地 (機場)：編輯頁面用 Select ---
             Grid::make(3)->schema([
                Forms\Components\Select::make('from_district_id')
                    ->label('機場')
                    ->visibleOn('edit') // 只在編輯頁面顯示
                    ->options(
                        City::whereIn('district_code', [110900, 110903, 120901, 130901])
                            ->whereNull('area_code')
                            ->whereNull('city_code')
                            ->pluck('city', 'district_code')
                            ->toArray()
                    )
                    ->afterStateUpdated(function ($state, Set $set) { // 使用 Set
                        $selectedDistict = City::where('district_code', $state)
                                ->whereNull('city_code')
                                ->whereNull('area_code')
                                ->first();
                        $set('from_district_name', $selectedDistict?->city);
                    })
                    ->required(),
                Forms\Components\Hidden::make('from_district_name'), // 編輯時需要
             ])->visibleOn('edit'), // 整個 Grid 只在編輯時顯示


            // --- 目的地選擇 ---
            Grid::make(3)->schema([
                // 區域 (Area) - 保持 Select
                Forms\Components\Select::make('to_area_id')
                    ->options(
                        City::whereIn('area_code', [110000, 120000, 130000, 140000])
                            ->whereNull('city_code')
                            ->whereNull('district_code')
                            ->pluck('city', 'area_code')
                            ->toArray()
                    )
                    ->label('目的地 - 區域')
                    ->reactive() // 需要 reactive 以更新城市選項
                    ->afterStateUpdated(function ($state, Set $set) { // 使用 Set
                        $set('to_city_id', null); // 清空城市選擇
                        $set('to_district_id', null); // 清空鄉鎮區選擇
                        $selectedArea = City::where('area_code', $state)
                                        ->whereNull('city_code')
                                        ->whereNull('district_code')
                                        ->first();
                        $set('to_area_name', $selectedArea?->city); // 儲存區域名稱
                    })
                    ->required(),
                Forms\Components\Hidden::make('to_area_name'), // 儲存名稱供後端使用

                // 縣市 (City) - 保持 Select
                Forms\Components\Select::make('to_city_id')
                    ->options(function (Get $get) { // 使用 Get
                        $areaId = $get('to_area_id');
                        if (!$areaId) return [];
                        return City::where('area_code', $areaId)
                            ->whereNotNull('city_code')
                            ->whereNull('district_code')
                            ->pluck('city', 'city_code')
                            ->toArray();
                    })
                    ->label('目的地 - 縣市')
                    ->reactive() // 需要 reactive 以更新鄉鎮區選項
                    ->afterStateUpdated(function ($state, Set $set) { // 使用 Set
                        $set('to_district_id', null); // 清空鄉鎮區選擇
                        $selectedCity = City::where('city_code', $state)
                                        ->whereNull('district_code')
                                        ->first();
                        $set('to_city_name', $selectedCity?->city); // 儲存城市名稱
                    })
                    ->searchable()
                    ->required(fn (Get $get) => $get('to_area_id') !== null), // 選擇區域後必填

                Forms\Components\Hidden::make('to_city_name'), // 儲存名稱供後端使用

                // --- 鄉鎮區 (District)：新增頁面用 CheckboxList ---
                // Forms\Components\CheckboxList::make('to_district_id')
                //     ->label('選擇鄉鎮區 (可複選)')
                //     ->visibleOn('create') // 只在新增頁面顯示
                //     ->options(function (Get $get) { // 使用 Get
                //         $areaId = $get('to_area_id');
                //         $cityId = $get('to_city_id');
                //         if (!$areaId || !$cityId) return [];
                //         return City::where('area_code', $areaId)
                //             ->where('city_code', $cityId)
                //             ->whereNotNull('district_code')
                //             ->pluck('city', 'district_code')
                //             ->toArray();
                //     })
                //     ->required(fn (Get $get) => $get('to_city_id') !== null) // 選擇城市後必填
                //     ->columns(3)
                //     // ->bulkToggleable() // 增加全選功能
                //     ->columnSpanFull(), // CheckboxList 佔滿整行
                Forms\Components\Select::make('to_district_id')
                    ->label('選擇鄉鎮區 (可複選)')
                    ->visibleOn('create') // 只在新增頁面顯示
                    ->options(function (Get $get) { // 使用 Get
                        $areaId = $get('to_area_id');
                        $cityId = $get('to_city_id');
                        if (!$areaId || !$cityId) return [];
                        return City::where('area_code', $areaId)
                            ->where('city_code', $cityId)
                            ->whereNotNull('district_code')
                            ->pluck('city', 'district_code')
                            ->toArray();
                    })
                    ->multiple() // <--- 啟用多選
                    ->searchable() // 建議加上 searchable 方便查找
                    ->required(fn (Get $get) => $get('to_city_id') !== null) // 選擇城市後必填
                    // ->columnSpanFull()
                    , // 佔滿整行

                // --- 鄉鎮區 (District)：編輯頁面用 Select ---
                Forms\Components\Select::make('to_district_id')
                    ->label('鄉鎮區')
                    ->visibleOn('edit') // 只在編輯頁面顯示
                    ->options(function (Get $get) {
                        $areaId = $get('to_area_id');
                        $cityId = $get('to_city_id');
                        if (!$areaId || !$cityId) return [];
                        return City::where('area_code', $areaId)
                            ->where('city_code', $cityId)
                            ->whereNotNull('district_code')
                            ->pluck('city', 'district_code')
                            ->toArray();
                    })
                    ->afterStateUpdated(function ($state, Set $set) {
                        $selectedDistrict = City::where('district_code', $state)->first();
                        $set('to_district_name', $selectedDistrict?->city);
                    })
                    ->searchable()
                    ->required(fn (Get $get) => $get('to_city_id') !== null),

                Forms\Components\Hidden::make('to_district_name'), // 編輯時需要
            ]),

            // --- 價格輸入 ---
            Forms\Components\TextInput::make('price')
                ->label('統一設定價格') // 提示這是批量設定的價格
                ->numeric() // 確保是數字
                ->required()
                ->columnSpanFull(), // 佔滿整行
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_type')
                    ->label('類別')
                    ->toggleable()
                    ->sortable()
                    ->searchable()
                    ->getStateUsing( function (Model $record){
                        if($record['order_type'] == 0){
                            return '機場接送';
                        }else{
                            return '兩地接送';
                        }
                    })
                    ->extraAttributes(['class' => 'hide-on-mobile']),
                Tables\Columns\TextColumn::make('car_type')
                    ->label('車型')
                    ->sortable()
                    ->searchable()
                    ->getStateUsing( function (Model $record){
                        if ($record['vendor_id'] == 17) {
                            if ($record['car_type'] == 5) {
                                return '舒適小車';
                            } elseif ($record['car_type'] == 9) {
                                return '舒適大車';
                            } elseif ($record['car_type'] == 91) {
                                return '尊榮大車';
                            }
                            // 如果 vendor_id == 2 但 car_type 不是 5, 9, 或 91, 則繼續執行下面的通用邏輯
                        }
                        if($record['car_type'] == 5){
                            return '五人座';
                        }else if($record['car_type'] == 51){
                            return '五人休旅';
                        }else if($record['car_type'] == 9){
                            return '九人座';
                        }else if($record['car_type'] == 91){
                            return 'VITO';
                        }else if($record['car_type'] == 10){
                            return 'Alphard';
                        }else{
                            return '五人座';
                        }
                    }),
                Tables\Columns\TextColumn::make('from_district_name')
                    ->label('A 點')
                    ->sortable()
                    ->searchable(['from_city_name', 'from_district_name'])
                    ->formatStateUsing(function ($record) {
                        $city = $record->from_city_name ?? ''; // 如果為空，默認為空字串
                        $district = $record->from_district_name ?? ''; // 如果為空，默認為空字串
                        // $other_name = $record->from_other_name ?? ''; // 如果為空，默認為空字串
                        return trim("{$city} {$district}");
                    }),
                Tables\Columns\TextColumn::make('to_city_name')
                    ->label('B 點')
                    ->sortable()
                    ->searchable(['to_city_name', 'to_district_name'])
                    ->formatStateUsing(function ($record) {
                        $city = $record->to_city_name ?? ''; // 如果為空，默認為空字串
                        $district = $record->to_district_name ?? ''; // 如果為空，默認為空字串
                        return trim("{$city} {$district}");
                    }),
                // Tables\Columns\TextColumn::make('to_city_name')
                //     ->label('B 點')
                //     ->sortable()
                //     ->searchable(),
                // Tables\Columns\TextColumn::make('to_district_name')
                //     ->label('B 點')
                //     ->sortable()
                //     ->searchable(),
                Tables\Columns\TextInputColumn::make('price')
                    ->label('價格')
                    ->width(100)
                    ->sortable()
                    ->searchable(),
            ])
            ->defaultPaginationPageOption('100')
            ->striped()
            ->filters([
                // Tables\Filters\TrashedFilter::make(),
                Filter::make('order_type')
                    ->label('接送類別')
                    ->default(0)
                    ->query(function ($query, $data) {
                        if ($data['order_type']) {
                            $query->where('order_type', $data['order_type']);
                        }
                    })
                    ->form([
                        Radio::make('order_type')
                            ->label('車輛類別')
                            ->inline()
                            ->default(0)
                            ->live()
                            ->options([
                                0 => '機場接送',
                                // 1 => '兩地接送',
                            ])
                    ]),
                Filter::make('car_type')
                    ->query(function ($query, $data) {
                        if ($data['car_type']) {
                            $query->where('car_type', $data['car_type']);
                        }
                    })
                    ->form([
                        Radio::make('car_type')
                            ->label('車輛類別')
                            ->inline()
                            ->default(5)
                            ->options(function(){
                                return static::getCarTypeOptions();
                            }),
                    ]),
                SelectFilter::make('from_district_id')
                    ->label('機場') // Changed label
                    // ->default(110903) // Remove default if not needed
                    ->options(
                        City::whereIn('district_code', [110900, 110903, 120901, 130901])
                            ->whereNull('area_code')
                            ->whereNull('city_code')
                            ->pluck('city', 'district_code')
                            ->toArray()
                    ),
                     // --- NEW: Destination Area Filter ---
                SelectFilter::make('to_area_id')
                    ->label('目的地 - 區域')
                    ->options(function () {
                        return City::whereNotNull('area_code')
                                ->whereNull('city_code')
                                ->whereNull('district_code')
                                ->pluck('city', 'area_code') // Assuming 'city' column holds the name
                                ->toArray();
                    }),

                // --- NEW: Destination City Filter ---
                // SelectFilter::make('to_city_id')
                //     ->label('目的地 - 縣市')
                //     ->options(function () {
                //         // Fetch all distinct cities available in the prices table or from City model
                //         // Option 1: From City model (simpler, shows all possible cities)
                //         return City::whereNotNull('city_code')
                //                 ->whereNull('district_code') // Ensure it's a city-level entry
                //                 ->distinct()
                //                 ->pluck('city', 'city_code')
                //                 ->toArray();
                //         // Option 2: From Price model (more complex, only shows cities with existing prices)
                //         // return Price::select('to_city_id', 'to_city_name')
                //         //            ->whereNotNull('to_city_id')
                //         //            ->distinct()
                //         //            ->pluck('to_city_name', 'to_city_id')
                //         //            ->toArray();
                //     })
                //     ->searchable(), // Add searchable if there are many cities

            // --- Existing Vendor Filter ---
                SelectFilter::make('vendor_id')
                    ->label('廠商')
                    ->visible(fn(Forms\Get $get)=>Filament::auth()->user()->id==1)
                    ->options(function(){
                            return Vendor::all()->pluck('title', 'id')->toArray();
                        })
                        ->query(function (Builder $query, array $data) {
                            $query->when($data['value'], fn (Builder $query, $value): Builder => $query->where('vendor_id', $value));
                        }),

                // SelectFilter::make('to_area_id')
                //     ->label('城市')
                //     ->options(function(){
                //         return City::where('area_code', '!=', NULL)
                //             ->where('city_code', '!=', NULL)
                //             ->where('district_code', '=', NULL)
                //             ->pluck('city', 'city_code')->toArray();
                //     })
                //     ->query(function (Builder $query, array $data) {
                //         if ($data['value']) {
                //             $query->where('to_city_id', $data['value'])
                //                 ->orWhere('from_city_id', $data['value']);
                //         }
                //     })
                    // ,
            ], layout: FiltersLayout::AboveContent)
            ->actions([
                // Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->label('删除'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                    // Tables\Actions\ForceDeleteBulkAction::make(),
                    // Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPrices::route('/'),
            'create' => Pages\CreatePrice::route('/create'),
            // 'edit' => Pages\EditPrice::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        if(Filament::auth()->user()->vendor_id==0){
            return parent::getEloquentQuery()
                ->where('status', 0)
                ->where('deleted_at', null)
                ->withoutGlobalScopes([
                    SoftDeletingScope::class,
                ]);
        }else{
            return parent::getEloquentQuery()
                ->where('status', 0)
                ->where('deleted_at', null)
                ->where('vendor_id', Filament::auth()->user()->vendor_id)
                ->withoutGlobalScopes([
                    SoftDeletingScope::class,
                ]);;
        }
    }
    protected static function getCarTypeOptions(): array
    {
        $user = Filament::auth()->user();
        if ($user) {
            $userVendorId = $user->vendor_id;
            // Log::info('userVendorId--'.$userVendorId);
            if ($userVendorId == 0) {
                return [
                    5 => '五人座',
                    51 => '五人休旅',
                    7 => '七人座',
                    9 => '九人座',
                    91 => 'VITO',
                    10 => 'Alphard',
                ];
            } elseif ($userVendorId == 10) {
                return [
                    5 => '五人座',
                    51 => '五人休旅',
                    9 => '九人座',
                ];
            } elseif ($userVendorId == 7) {
                return [
                    5 => '五人座',
                    7 => '七人座',
                    9 => '九人座',
                ];
            } elseif ($userVendorId == 12) {
                return [
                    5 => '五人座',
                    51 => '五人休旅',
                    9 => '九人座',
                    91 => 'VITO',
                    10 => 'Alphard',
                ];
            } elseif ($userVendorId == 13) {
                return [
                    5 => '五人座',
                    51 => '五人休旅',
                    9 => '九人座',
                ];
            } elseif ($userVendorId == 17) {
                return [
                    5 => '舒適小車',
                    9 => '舒適大車',
                    91 => '尊榮大車',
                ];
            }
        }
        return []; // 如果使用者未登入，回傳空陣列
    }
}
