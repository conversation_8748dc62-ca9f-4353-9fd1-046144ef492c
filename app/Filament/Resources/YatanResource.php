<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Dispatch;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Columns\Summarizers\Sum;
use App\Filament\Resources\YatanResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\YatanResource\RelationManagers;

class YatanResource extends Resource
{
    protected static ?string $model = Dispatch::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = '雅潭管理';
    public static $vendorId = 7;
    public static $customerName = '佳能';

    public static function canView(Model $record): bool
    {
        return auth()->user()->is_admin;
    }

    public static function canViewAny(): bool
    {
        // Log::info('canViewAny'.auth()->user()->id);
        if(auth()->user()->is_admin || auth()->user()->id==2){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        if(auth()->user()->vendor_id==0){
            return parent::getEloquentQuery()->where('vendor_id', static::$vendorId);
        }else{
            return parent::getEloquentQuery()->where('vendor_id', static::$vendorId);
        }
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('start_time')
                                ->label('搭車日期'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('dispatch_id')
                    ->label('派車單號')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('start_time')
                        ->label('日期')
                        ->searchable()
                        ->formatStateUsing(fn ($state) => \Carbon\Carbon::parse($state)->format('Y-m-d')),
                Tables\Columns\TextColumn::make('customer_name')
                    ->label('客戶名稱')
                    ->searchable(),
                Tables\Columns\TextColumn::make('customer_address')
                    ->label('部門')
                    ->searchable(),
                Tables\Columns\TextColumn::make('route')
                    ->label('路線')
                    ->searchable(),
                Tables\Columns\TextColumn::make('rental_cost')
                    ->label('車資')
                    ->searchable(),
                Tables\Columns\TextColumn::make('driver.name')
                    ->label('司機')
                    ->searchable(),
                Tables\Columns\TextColumn::make('rental_cost')
                    ->summarize(Sum::make()->label('Total'))
            ])
            ->defaultGroup('customer_address')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListYatans::route('/'),
            'create' => Pages\CreateYatan::route('/create'),
            'edit' => Pages\EditYatan::route('/{record}/edit'),
        ];
    }

}
