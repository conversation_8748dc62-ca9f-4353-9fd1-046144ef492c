<?php

namespace App\Filament\Resources\BTopupResource\Pages;

use App\Filament\Resources\BTopupResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBTopup extends EditRecord
{
    protected static string $resource = BTopupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
