<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CityResource\Pages;
use App\Filament\Resources\CityResource\RelationManagers;
use App\Models\City;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CityResource extends Resource
{
    protected static ?string $model = City::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    public static function canViewAny(): bool
    {
        // Log::info('canViewAny00--'.auth()->user()->id);
        if(auth()->user()->id==1){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(auth()->user()->vendor_id==0){
            return parent::getEloquentQuery()->where('status', 0);
        }else{
            return parent::getEloquentQuery()
                ->where('status', 0)
                ->where('vendor_id', auth()->user()->vendor_id);
        }
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('area_code')
                                ->label('區域編號'),
                Forms\Components\TextInput::make('city_code')
                                ->label('城市編號'),
                Forms\Components\TextInput::make('district_code')
                                ->label('鄉鎮編號'),
                Forms\Components\TextInput::make('city')
                                ->label('名稱'),
                Forms\Components\TextInput::make('zip')
                                ->label('郵遞區號'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('area_code')
                    ->label('區域編號')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('city_code')
                    ->label('城市編號')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('district_code')
                    ->label('鄉鎮編號')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('city')
                    ->label('城市')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('zip')
                    ->label('郵遞區號')
                    ->sortable()
                    ->searchable(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCities::route('/'),
            'create' => Pages\CreateCity::route('/create'),
            'edit' => Pages\EditCity::route('/{record}/edit'),
        ];
    }
}
