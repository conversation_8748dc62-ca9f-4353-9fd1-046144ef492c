<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Driver;
use App\Models\Vendor;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\VendorResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\VendorResource\RelationManagers;
use App\Filament\Resources\VendorResource\RelationManagers\ContractRelationManager;

class VendorResource extends Resource
{
    protected static ?string $model = Vendor::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?int $navigationSort = 2;

    protected static ?string $navigationLabel = '廠商管理';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('contact')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('mobile')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('address')
                    ->maxLength(255),
                Forms\Components\TextInput::make('rm_id')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('join_code')
                    ->maxLength(255),
                Forms\Components\Select::make('status')
                    ->label('狀態')
                    ->options([
                        '0' => '停用',
                        '1' => '啟用',
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label('廠商名稱')
                    ->searchable(),
                Tables\Columns\TextColumn::make('contact')
                    ->searchable()
                    ->label('聯絡人'),
                Tables\Columns\TextColumn::make('mobile')
                    ->searchable()
                    ->label('聯絡人電話'),
                Tables\Columns\TextColumn::make('join_code')
                    ->label('邀請碼'),
                Tables\Columns\TextColumn::make('driver_count')
                    // ->counts('drivers')
                    ->getStateUsing(function ($record) {
                        return $record->drivers()->count(); // 手動計算 driver 的數量
                    })
                    ->label('司機數'),
                Tables\Columns\IconColumn::make('status')
                    ->label('狀態')
                    ->boolean()
                    ->color(fn (string $state): string => match ($state) {
                        '0' => 'danger',
                        '1' => 'success',
                        default => 'danger',
                    }),
                Tables\Columns\TextColumn::make('created_at')->date(),
            ])
            ->striped()
            ->defaultPaginationPageOption(25)
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ContractRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVendors::route('/'),
            'create' => Pages\CreateVendor::route('/create'),
            'edit' => Pages\EditVendor::route('/{record}/edit'),
        ];
    }
}
