<?php

namespace App\Filament\Resources\RentcarfeeResource\Pages;

use App\Filament\Resources\RentcarfeeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListRentcarfees extends ListRecords
{
    protected static string $resource = RentcarfeeResource::class;
    protected static ?string $title = '租賃車資列表';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('新增租賃車資'),
        ];
    }
}
