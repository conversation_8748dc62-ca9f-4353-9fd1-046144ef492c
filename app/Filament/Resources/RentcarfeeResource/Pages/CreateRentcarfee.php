<?php

namespace App\Filament\Resources\RentcarfeeResource\Pages;

use App\Filament\Resources\RentcarfeeResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateRentcarfee extends CreateRecord
{
    protected static string $resource = RentcarfeeResource::class;
    protected static ?string $title = '建立資料';
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
