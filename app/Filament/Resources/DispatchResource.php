<?php

namespace App\Filament\Resources;

use Closure;
use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Get;
use Pages\ViewCustomer;
use App\Models\Dispatch;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Facades\Filament;
use Barryvdh\DomPDF\Facade\Pdf;
use Filament\Resources\Resource;
use Tables\Columns\ToggleColumn;
use Forms\Components\ImageColumn;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use App\Notifications\PdfGenerated;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Facades\Log;
use Tables\Columns\TextInputColumn;
use Filament\Forms\Components\Group;
use Illuminate\Support\Facades\Auth;
use Tables\Forms\Components\TextInput;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\ViewField;
use Filament\Notifications\Notification;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Exports\DispatchExporter;
use Filament\Forms\Components\Placeholder;
use Filament\Notifications\Actions\Action;
use Filament\Tables\Enums\ActionsPosition;
use Illuminate\Database\Eloquent\Collection;
use Filament\Infolists\Components\ImageEntry;
use App\Filament\Resources\DispatchResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\DispatchResource\RelationManagers;

class DispatchResource extends Resource
{
    protected static ?string $model = Dispatch::class;

    protected static ?string $navigationIcon = 'heroicon-c-user-group';
    protected static ?string $navigationLabel = '派車單管理';
    public static function canViewAny(): bool
    {
        // Log::info('canViewAny00--'.auth()->user()->id);
        if(Filament::auth()->user()->id==10 || Filament::auth()->user()->id==11  || Filament::auth()->user()->id==15){
            return false;
        }
        return true;
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make()->schema([
                    Forms\Components\Fieldset::make('派車資訊')
                        ->schema([
                            Forms\Components\TextInput::make('dispatch_id')
                                ->label('派車單號')
                                ->disabled(),
                            Forms\Components\TextInput::make('customer_name')
                                ->label('客戶名稱')
                                ->disabled(),
                            Forms\Components\TextInput::make('customer_sex')
                                ->label('客戶性別')
                                ->disabled()
                                ->formatStateUsing(fn (string $state): string => $state == 1 ? '男生' : '女生'),
                            Forms\Components\TextInput::make('customer_mobile')
                                ->label('客戶手機')
                                ->disabled(),
                            Forms\Components\TextInput::make('start_time')
                                ->label('租賃時間起')
                                // ->hidden(function () {
                                //     return auth()->user()->vendor_id == 7;
                                // })
                                ,
                            // Forms\Components\DatePicker::make('start_time')
                            //     ->label('租賃時間起')
                            //     ->hidden(function () {
                            //         return auth()->user()->vendor_id != 7;
                            //     }),
                            Forms\Components\TextInput::make('end_time')
                                ->label('租賃時間迄')
                                ->disabled(),
                            Forms\Components\TextInput::make('route')
                                ->label('行駛路線'),
                            Forms\Components\TextInput::make('rental_cost')
                                ->label('租車費用'),
                            Forms\Components\TextInput::make('customer_address')
                                ->label('客戶地址'),
                            Forms\Components\Toggle::make('regular')
                                ->label('固定班次'),
                        ]),
                ]),
                Group::make()->schema([
                    Forms\Components\Fieldset::make('駕駛資訊')
                        ->relationship('driver')
                        ->schema([
                            Forms\Components\TextInput::make('name')
                                ->label('駕駛姓名')
                                ->disabled(),
                            Forms\Components\TextInput::make('mobile')
                                ->label('駕駛手機')
                                ->disabled(),
                        ]),
                    Forms\Components\Fieldset::make('車輛資訊')
                        ->relationship('car')
                        ->schema([
                            Forms\Components\TextInput::make('car_license')
                                ->label('車牌號碼')
                                ->disabled(),
                            Forms\Components\TextInput::make('car_type')
                                ->label('廠牌型式')
                                ->disabled(),
                        ])
                ]),
                Group::make()->schema([
                    Forms\Components\Fieldset::make('派車單圖檔')
                    ->schema([
                        Placeholder::make('Image')
                            ->content(function ($record): HtmlString {
                                return new HtmlString("<img src= '" . url("/images/".$record->dispatch_id.".jpg?".time()."") . "')>");
                            })
                        ])
                ])->columnSpanFull(),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(Filament::auth()->user()->vendor_id==0){
            return parent::getEloquentQuery()->where('status', 0);
        }else{
            return parent::getEloquentQuery()
                ->where('status', 0)
                ->where('vendor_id', Filament::auth()->user()->vendor_id);
        }
    }
    public static function table(Table $table): Table
    {
        // $table = new CustomTable();
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('start_time')
                    ->label('派車日期')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('customer_name')
                    ->label('客戶名稱')
                    ->searchable(),
                Tables\Columns\TextColumn::make('customer_sex')
                    ->getStateUsing( function (Model $record){
                        if($record['customer_sex'] == 1){
                            return '男';
                        }else{
                            return '女';
                        }
                    })
                    ->label('客戶性別')
                    ->searchable(),
                Tables\Columns\TextColumn::make('customer_mobile')
                    ->label('客戶手機')
                    ->searchable(),
                Tables\Columns\TextColumn::make('driver.name')
                    ->label('司機')
                    ->searchable(),
                Tables\Columns\TextColumn::make('driver.mobile')
                    ->label('司機電話')
                    ->searchable(),
                Tables\Columns\TextColumn::make('driver.car.car_license')
                    ->label('車牌號碼')
                    ->searchable(),
                Tables\Columns\TextColumn::make('rental_cost')
                    ->label('車資')
                    ->sortable(),
                Tables\Columns\ToggleColumn::make('regular')
                    ->label('固定')
                    ->visible(function () {
                        return Filament::auth()->user()->role == 'admin';
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('route')
                    ->label('路程')
                    ->searchable()
                    ->sortable(),
                    // ImageEntry::make('image_path')->label('派車單圖檔'),
                // Tables\Columns\ImageColumn::make('image_path')
                    // ->defaultImageUrl(url('/images/placeholder.png'))
                    // ->getStateUsing(function (Dispatch $record): string {
                    //     // dd($record->image_path);
                    //     return "<img src='" . $record->id.".jpg" . "/'/>";
                    // })
                    // ->extraImgAttributes([
                    //     'img' => 'src'
                    // ]),
                Tables\Columns\TextColumn::make('vendor.title')
                    ->label('車行')
                    ->searchable()
                    ->visible(fn () => Filament::auth()->user()->id == 1)
                    ->formatStateUsing(fn($state) => $state ? mb_substr($state, 0, 2) : ''),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('建立時間')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable()
                    ->searchable(),
            ])
            ->defaultSort('start_time', 'desc')
            ->striped()
            ->defaultPaginationPageOption(50)
            ->filters([
                Filter::make('created_at')
                    ->form([
                        DatePicker::make('start_date')
                            ->label('Start Date')
                            ->placeholder('Select start date'),
                        DatePicker::make('end_date')
                            ->label('End Date')
                            ->placeholder('Select end date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when($data['start_date'], fn ($query, $date) => $query->whereDate('created_at', '>=', $date))
                            ->when($data['end_date'], fn ($query, $date) => $query->whereDate('created_at', '<=', $date));
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($data['start_date']) {
                            $indicators['start_date'] = 'Start Date: ' . $data['start_date'];
                        }

                        if ($data['end_date']) {
                            $indicators['end_date'] = 'End Date: ' . $data['end_date'];
                        }

                        return $indicators;
                    }),
                SelectFilter::make('regular')
                    ->label('狀態')
                    ->searchable()
                    ->options([
                        '0' => '非固定',
                        '1' => '固定',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('編輯')
                    ->hidden(function ($record) {
                        return Filament::auth()->user()->role != 'admin';
                    }),
                Tables\Actions\ViewAction::make()
                    ->color('primary')
                    ->label('檢視'),
            ], position: ActionsPosition::BeforeColumns)
            ->recordUrl(null)
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                    BulkAction::make('updatePrice')
                        ->label('批次建立報表')
                        ->action(function ($records, $data, $livewire): void {
                            $filters = $livewire->tableFilters;
                            $search = $livewire->tableSearch;
                            $fileName = self::generatePdf('all',$records, $filters);
                            $downloadUrl = url('storage/pdf_outputs/'.$fileName);
                            $user = Filament::auth()->user();
                            Log::info('Sending notification to user: ' . $user->id);
                            Log::info($user);
                            Notification::make()
                                ->title('新的文件已建立')
                                ->body("新的文件已建立，檔案路徑: ({$downloadUrl})")
                                ->success()
                                ->actions([
                                    Action::make('Open')
                                        ->button()
                                        ->url($downloadUrl, shouldOpenInNewTab: true),
                                ])
                                ->sendToDatabase($user);
                        })
                        ->visible(function () {
                            return Filament::auth()->user()->role == 'admin';
                        })
                        ->requiresConfirmation()
                        ->modalHeading('確認產生報表')
                        ->modalSubheading('選取派車單，建立報表!')
                        ->modalButton('產生報表'),
                    BulkAction::make('updatePrice2')
                        ->label('批次建立報表排除固定')
                        ->action(function ($records, $data, $livewire): void {
                            $filters = $livewire->tableFilters;
                            if($filters['created_at']['start_date'] == null || $filters['created_at']['end_date'] == null){
                                // dd($filters);
                            }else{
                                // die('no empty');
                            }
                            $fileName = self::generatePdf('route', $records, $filters);
                            $downloadUrl = url('storage/pdf_outputs/'.$fileName);
                            $user = Filament::auth()->user();
                            Notification::make()
                                ->title('新的文件已建立')
                                ->body("新的文件已建立，檔案路徑: ({$downloadUrl})")
                                ->success()
                                ->actions([
                                    Action::make('Open')
                                        ->button()
                                        ->url($downloadUrl, shouldOpenInNewTab: true),
                                ])
                                ->sendToDatabase($user);
                        })
                        ->visible(function () {
                            return Filament::auth()->user()->role == 'admin';
                        })
                        ->requiresConfirmation()
                        ->modalHeading('確認產生報表(排除固定4位)')
                        ->modalSubheading('選取派車單，建立報表!')
                        ->modalButton('產生報表'),
                    BulkAction::make('updateReport')
                        ->label('批次建立報表七和')
                        ->action(function ($records, $data, $livewire): void {
                            $filters = $livewire->tableFilters;
                            $fileName = self::generatePdf('seven', $records, $filters);
                            $downloadUrl = url('storage/pdf_outputs/'.$fileName);
                            $user = Filament::auth()->user();
                                Notification::make()
                                ->title('新的七和報表已建立')
                                ->body("新的七和報表已建立，檔案路徑: ({$downloadUrl})")
                                ->success()
                                ->actions([
                                    Action::make('Open')
                                        ->button()
                                        ->url($downloadUrl, shouldOpenInNewTab: true),
                                ])
                                ->sendToDatabase($user);
                        })
                        ->visible(function () {
                            return Filament::auth()->user()->role == 'admin';
                        })
                        ->requiresConfirmation()
                        ->modalHeading('確認產生報表(七和)')
                        ->modalSubheading('選取派車單，建立報表!')
                        ->modalButton('產生報表'),
                    BulkAction::make('updateReport1')
                        ->label('建立報表七和LIST')
                        ->action(function ($records, $data, $livewire): void {
                            $filters = $livewire->tableFilters;
                            $fileName = self::generatePdf('seven2', $records, $filters);
                            $downloadUrl = url('storage/pdf_outputs/'.$fileName);
                            $user = Auth::user();
                                Notification::make()
                                ->title('新的七和報表LIST已建立')
                                ->body("新的七和報表已建立，檔案路徑: ({$downloadUrl})")
                                ->success()
                                ->actions([
                                    Action::make('Open')
                                        ->button()
                                        ->url($downloadUrl, shouldOpenInNewTab: true),
                                ])
                                ->sendToDatabase($user);
                        })
                        ->visible(function () {
                            return Filament::auth()->user()->role == 'admin';
                        })
                        ->requiresConfirmation()
                        ->modalHeading('確認產生報表(七和)')
                        ->modalSubheading('選取派車單，建立報表!')
                        ->modalButton('產生報表'),
                    BulkAction::make('updateReport2')
                        ->label('批次建立報表京三')
                        ->action(function ($records, $data, $livewire): void {
                            $filters = $livewire->tableFilters;
                            $fileName = self::generatePdf('three', $records,$filters);
                            $downloadUrl = url('storage/pdf_outputs/'.$fileName);
                            $user = Auth::user();
                                Notification::make()
                                ->title('新的京三報表已建立')
                                ->body("新的京三報表已建立，檔案路徑: ({$downloadUrl})")
                                ->success()
                                ->actions([
                                    Action::make('Open')
                                        ->button()
                                        ->url($downloadUrl, shouldOpenInNewTab: true),
                                ])
                                ->sendToDatabase($user);
                        })
                        ->visible(function () {
                            return Filament::auth()->user()->role == 'admin';
                        })
                        ->requiresConfirmation()
                        ->modalHeading('確認產生報表(京三)')
                        ->modalSubheading('選取派車單，建立報表!')
                        ->modalButton('產生報表'),
                    BulkAction::make('updateReport3')
                        ->label('批次建立報表村田')
                        ->action(function ($records, $data, $livewire): void {
                            $filters = $livewire->tableFilters;
                            $fileName = self::generatePdf('mm', $records, $filters);
                            $downloadUrl = url('storage/pdf_outputs/'.$fileName);
                            $user = Auth::user();
                                Notification::make()
                                ->title('新的村田報表已建立')
                                ->body("新的村田報表已建立，檔案路徑: ({$downloadUrl})")
                                ->success()
                                ->actions([
                                    Action::make('Open')
                                        ->button()
                                        ->url($downloadUrl, shouldOpenInNewTab: true),
                                ])
                                ->sendToDatabase($user);
                        })
                        ->visible(function () {
                            return Filament::auth()->user()->role == 'admin';
                        })
                        ->requiresConfirmation()
                        ->modalHeading('確認產生報表')
                        ->modalSubheading('選取派車單，建立報表!')
                        ->modalButton('產生報表'),
                    Tables\Actions\ExportBulkAction::make()
                        ->exporter(DispatchExporter::class)
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDispatches::route('/'),
            'create' => Pages\CreateDispatch::route('/create'),
            'edit' => Pages\EditDispatch::route('/{record}/edit'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }

    protected function showImage($record)
    {
        // dd($record);
        // 這裡的邏輯可以用來獲取圖片 URL 或其他相關數據
        $imageUrl = $record->image_path; // 假設你的模型有 image_url 欄位
        $this->emit('showImageModal', $imageUrl);
    }

    protected static function generatePdf($type, Collection $records, $filters = null)
    {
        if($type=='all'){
            $total = 0;
            // 获取数据
            // $products = $records->map(function ($record) {
            //     return [
            //         'id' => $record->id,
            //         'dispatch_id' => $record->dispatch_id,
            //         'customer_name' => $record->customer_name,
            //         'price' => $record->rental_cost,
            //         'image' => $record->image_path, // 假设图片 URL 存储在 `image_url` 字段
            //     ];
            // });
            // foreach ($products as $item) {
            //     $total += $item['price'];
            // }
            // $data = [
            //     'products' => $products,
            //     'total' => $total
            // ];
            $data = DB::select("select customer_address, COUNT(id) AS COUNT, SUM(rental_cost) AS sum FROM dispatches WHERE customer_name LIKE '%佳能%' GROUP BY customer_address");
            foreach ($data as $key => $value) {
                $data[$key]->aa = Dispatch::where('customer_address', $value->customer_address)
                                        ->where('customer_name', 'like', '%佳能%')
                                        ->get();
            }
            $data[0]->all = Dispatch::where('customer_name', 'like', '%佳能%')
                                    ->whereBetween('start_time', ['2024-09-21 00:00:00', '2024-10-19 23:59:59'])
                                    ->get();
            // Log::info('DATA: ' . $data);
            // 生成 PDF
            $pdf = Pdf::loadView('myview', ['data' => $data])->set_option('isFontSubsettingEnabled', true)->setPaper('a4', 'portrait');
            // 保存 PDF 到本地
            $fileName = 'CANON_' . date('Ymd_His') . '_' . uniqid() . '.pdf';
            $filePath = storage_path('app/public/pdf_outputs/' . $fileName);
            $pdf->save($filePath);
            return $fileName;
        }else if($type=='route'){
            // dd($filters);
            $startDate = $filters['created_at']['start_date'].' 00:00:00';
            $endDate = $filters['created_at']['end_date'].' 23:59:59';
            $data = Dispatch::where('customer_name', 'like', '%佳能%')
                                    ->where('regular', 0)
                                    ->where('status', 0)
                                    ->whereBetween('start_time', [$startDate, $endDate])
                                    // ->whereNotIn('driver_id', [944, 948, 957, 946])
                                    ->get();
            $pdf = Pdf::loadView('myrouterview', ['data' => $data])
                        ->set_option('isFontSubsettingEnabled', true)
                        ->setPaper('a4', 'portrait');
            $fileName = '佳能非固定列表_2_' . date('Ymd_His') . '_' . uniqid() . '.pdf';
            $filePath = storage_path('app/public/pdf_outputs/' . $fileName);
            $pdf->save($filePath);
            return $fileName;
        }else if($type=='seven'){
            // 七和
            $startDate = $filters['created_at']['start_date'].' 00:00:00';
            $endDate = $filters['created_at']['end_date'].' 23:59:59';
            $data = Dispatch::select('start_time', 'customer_name', 'signature_file', 'driver_id')
                                ->where('customer_name', 'like', '%七和%')
                                    ->whereBetween('start_time', [$startDate, $endDate])
                                    ->where('status', 0)
                                    ->orderBy('start_time', 'asc')
                                    ->get()
                                    ->groupBy('start_time')
                                    ->map(function ($group) {
                                        // dd($group[1]);
                                        return [
                                            'start_time' => $group->first()->start_time,
                                            'signature_file' => $group->pluck('signature_file')->implode(','), // 或使用其他方式合併 price
                                            'signature_file1' => $group->pluck('signature_file')->implode(','), // 根據需求增加欄位
                                        ];
                                    });
            $pdf = Pdf::loadView('myview_seven', ['data' => $data])
                        ->set_option('isFontSubsettingEnabled', true)
                        ->setPaper('a4', 'portrait');
            $fileName = '七和_3_' . date('Ymd_His') . '_' . uniqid() . '.pdf';
            $filePath = storage_path('app/public/pdf_outputs/' . $fileName);
            $pdf->save($filePath);
            return $fileName;
        }else if($type=='seven2'){
            // 七和 LIST
            $startDate = $filters['created_at']['start_date'].' 00:00:00';
            $endDate = $filters['created_at']['end_date'].' 23:59:59';
            $data = Dispatch::select('start_time', 'customer_name', 'route', 'rental_cost', 'driver_id', 'signature_file')
                                ->where('customer_name', 'like', '%七和%')
                                ->where('status', 1)
                                ->whereBetween('start_time', [$startDate, $endDate])
                                // ->distinct()
                                ->orderBy('start_time', 'asc')
                                ->get();
            $pdf = Pdf::loadView('myrouterview', ['data' => $data])
                        ->set_option('isFontSubsettingEnabled', true)
                        ->setPaper('a4', 'portrait');
            $fileName = '七和列表_3_' . date('Ymd_His') . '_' . uniqid() . '.pdf';
            $filePath = storage_path('app/public/pdf_outputs/' . $fileName);
            $pdf->save($filePath);
            return $fileName;
        }else if($type=='three'){
            // 京三
            $startDate = $filters['created_at']['start_date'].' 00:00:00';
            $endDate = $filters['created_at']['end_date'].' 23:59:59';
            $data = Dispatch::select('start_time', 'customer_name', 'route', 'rental_cost', 'signature_file', 'driver_id')
                                ->where('customer_name', 'like', '%京三%')
                                    ->whereBetween('start_time', [$startDate, $endDate])
                                    ->where('status', 0)
                                    ->orderBy('start_time', 'asc')
                                    ->get();
                                    // dd($data);
            $pdf = Pdf::loadView('myrouterview', ['data' => $data])
                        ->set_option('isFontSubsettingEnabled', true)
                        ->setPaper('a4', 'portrait');
            $fileName = '京三_4_' . date('Ymd_His') . '_' . uniqid() . '.pdf';
            $filePath = storage_path('app/public/pdf_outputs/' . $fileName);
            $pdf->save($filePath);
            return $fileName;
        }else if($type=='mm'){
            // 村田

            $startDate = $filters['created_at']['start_date'].' 00:00:00';
            $endDate = $filters['created_at']['end_date'].' 23:59:59';
            $regular = $filters['regular'];
            // dd([$startDate, $endDate, $regular]);
            $query = Dispatch::select('start_time', 'customer_name', 'route', 'rental_cost', 'driver_id', 'signature_file')
                                ->where('customer_name', 'like', '%村田%')
                                ->where('status', 0)
                                ->whereBetween('start_time', [$startDate, $endDate]);
                                    // ->distinct()
            if (!is_null($regular['value'])) {
                $query->where('regular', $regular['value']);
            }

            // 最終執行查詢
            $data = $query->orderBy('start_time', 'asc')
                            ->get();
            $pdf = Pdf::loadView('myrouterview', ['data' => $data])
                        ->set_option('isFontSubsettingEnabled', true)
                        ->setPaper('a4', 'portrait');
            $fileName = '村田_4_' . date('Ymd_His') . '_' . uniqid() . '.pdf';
            $filePath = storage_path('app/public/pdf_outputs/' . $fileName);
            $pdf->save($filePath);
            return $fileName;
        }
    }
}
