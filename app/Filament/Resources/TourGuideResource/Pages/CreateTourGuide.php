<?php

namespace App\Filament\Resources\TourGuideResource\Pages;

use App\Filament\Resources\TourGuideResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateTourGuide extends CreateRecord
{
    protected static string $resource = TourGuideResource::class;
    protected static ?string $title = '建立導遊';
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
