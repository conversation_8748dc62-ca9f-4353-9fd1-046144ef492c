<?php

namespace App\Filament\Resources\ADispatchResource\Pages;

use App\Filament\Resources\ADispatchResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditADispatch extends EditRecord
{
    protected static string $resource = ADispatchResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Call the resource's hook first (if needed)
        $data = ADispatchResource::mutateFormDataBeforeSave($data);

        // Add any page-specific mutation logic here if necessary
        // $data['some_other_field'] = 'modified_in_edit_page';

        return $data;
    }
}
