<?php

namespace App\Filament\Resources\ADispatchResource\Pages;

use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Set;
use Illuminate\Support\Str;
use Illuminate\Support\HtmlString;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Radio;
use Filament\Resources\Components\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder; // 引入 ADispatchController
use Illuminate\Contracts\Support\Htmlable;         // <--- 確認這個 use 存在
use App\Filament\Resources\ADispatchResource; // <--- 加入這個 use
use App\Http\Controllers\ADispatchController;

class ListADispatches extends ListRecords
{
    protected static string $resource = ADispatchResource::class;

    // 添加JavaScript事件監聽器
    protected function getHeaderActions(): array
    {
        // --- 定義兩種樣板文字 ---
        $template1 = "--- 樣板一 (原始格式) ---\n" .
                     "預約日期: YYYY-MM-DD HH:MM\n" .
                     "服務類別: 送機 | 接機\n" .
                     "從: [上車地點]\n" .
                     "前往: [下車地點]\n" .
                     "乘車貴賓：[乘客姓名]\n" .
                     "貴賓電話：[乘客電話]";

        // --- 更新樣板二的格式 ---
        $template2 = "--- 樣板二 (新格式) ---\n" .
                     "預約日期: YYYY-MM-DD HH:MM\n" .
                     "服務類別: 送機 | 接機 | 包車\n" . // <-- 修改/新增
                     "上車: [上車地點內容]\n" .         // <-- 修改
                     "下車: [下車地點內容]\n" .         // <-- 修改
                     "乘車貴賓: [乘客姓名]\n" .         // <-- 修改
                     "貴賓電話: [乘客電話]\n" .         // <-- 新增
                     "金額: [費用數字]\n" .             // <-- 新增
                     "備註: [備註內容]\n";

        return [
            // Actions\CreateAction::make(),
            Actions\Action::make('addDispatch')
                ->label('快速新增派車單 (文字)')
                ->modalHeading('快速新增派車單 (文字)')
                ->modalDescription('請選擇一個樣板，然後貼上或編輯派車單內容。') // 更新描述
                ->form([
                    // --- 新增 Radio 選項 ---
                    Radio::make('template_type')
                        ->label('選擇樣板')
                        ->options([
                            'template1' => '樣板一 (原始)',
                            'template2' => '樣板二 (新)',
                        ])
                        ->default('template1') // 預設選中樣板一
                        ->inline()
                        ->reactive() // <--- 設為 reactive
                        ->afterStateUpdated(function (Set $set, ?string $state) use ($template1, $template2) {
                            // --- 根據選擇更新 Textarea ---
                            if ($state === 'template1') {
                                $set('text', $template1);
                            } elseif ($state === 'template2') {
                                $set('text', $template2);
                            } else {
                                $set('text', ''); // 清空或其他預設
                            }
                        })
                        ->columnSpanFull(), // 讓 Radio 佔滿一行

                    // --- Textarea ---
                    Textarea::make('text')
                        ->label('派車單內容')
                        ->required()
                        ->rows(10) // 可以稍微減少行數
                        ->columnSpanFull()
                        ->default($template1), // <--- 預設內容為樣板一
                ])
                ->action(function (array $data, Actions\Action $action): void {
                    $controller = new ADispatchController();
                    // 注意：$data 現在會包含 'template_type' 和 'text'
                    // parseAndStoreDispatchForFilament 只需要 'text'
                    $response = $controller->parseAndStoreDispatchForFilament(['text' => $data['text']]); // 只傳遞 text

                    if ($response['success']) {
                        Notification::make()
                            ->title('派車單新增成功')
                            ->success()
                            ->send();
                        $this->redirect(ADispatchResource::getUrl('index'));
                    } else {
                        Notification::make()
                            ->title('派車單新增失敗')
                            ->body($response['message'] ?? '發生未知錯誤')
                            ->danger()
                            ->send();
                    }
                })
                ->modalSubmitActionLabel('儲存')
                ->modalCancelActionLabel('取消')
                ->modalWidth('lg'),
        ];
    }

    // 使用Filament的renderHook方法來添加JavaScript代碼
    protected function getFooterWidgets(): array
    {
        // 直接在頁面底部添加一個script標籤，引用外部JavaScript文件
        echo '<script src="/js/adispatch-sound-notifier.js"></script>';

        return [];
    }

    // --- 加入 getTabs 方法 ---
    public function getTabs(): array
    {
        return [
            'all' => Tab::make('全部')
                ->badge(static::getModel()::query()->count()) // 顯示總數
                ->modifyQueryUsing(fn (Builder $query) => $query), // 不修改查詢

            'today' => Tab::make('今日')
                ->badge(static::getModel()::query()->whereDate('start_date', today())->count()) // 顯示今日數量
                ->modifyQueryUsing(fn (Builder $query) => $query->whereDate('start_date', today())), // 篩選 start_date 為今天

            'tomorrow' => Tab::make('明日')
                ->badge(static::getModel()::query()->whereDate('start_date', today()->addDay())->count()) // 顯示明日數量
                ->modifyQueryUsing(fn (Builder $query) => $query->whereDate('start_date', today()->addDay())), // 篩選 start_date 為明天

            'this_week' => Tab::make('本周')
                 // Carbon 的 startOfWeek 預設是星期日，endOfWeek 是星期六。如果需要週一到週日，需要調整。
                 // 這裡使用 Carbon 預設的本週範圍。
                ->badge(static::getModel()::query()->whereBetween('start_date', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])->count()) // 顯示本周數量
                ->modifyQueryUsing(fn (Builder $query) => $query->whereBetween('start_date', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])), // 篩選 start_date 在本周內

            'this_month' => Tab::make('本月')
                ->badge(static::getModel()::query()->whereBetween('start_date', [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()])->count()) // 顯示本月數量
                ->modifyQueryUsing(fn (Builder $query) => $query->whereBetween('start_date', [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()])), // 篩選 start_date 在本月內
        ];
    }
    // --- getTabs 方法結束 ---
}
