<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Models\User;
use Filament\Actions;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Log;
use App\Filament\Resources\UserResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function handleRecordCreation(array $data): User
    {
        // 创建用户并返回用户实例
        $user = User::create($data);
        Log::info($user);
        // 获取当前登录的管理员用户
        $admin = Filament::auth()->user();
        Log::info('Add user');
        Log::info('About to send notification', ['admin' => $admin->id]);
        // 发送通知
        Notification::make()
            ->title('New User Created')
            ->body("A new user has been created: {$user->name}")
            ->success()
            ->sendToDatabase($admin);
        Log::info('Notification sent');
        return $user;
    }
}
