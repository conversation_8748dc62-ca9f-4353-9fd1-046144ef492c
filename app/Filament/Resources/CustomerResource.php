<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Customer;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Filament\Forms\Components\Group;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Section;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\CustomerResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\CustomerResource\RelationManagers;

class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = '旅遊管理';
    protected static ?string $navigationLabel = '客戶管理';
    protected static ?int $navigationSort = 2;

    public static function canViewAny(): bool
    {
        // Log::info('canViewAny00--'.auth()->user()->id);
        if(Filament::auth()->user()->id==1 || Filament::auth()->user()->id==2 || Filament::auth()->user()->id==3){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(Filament::auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', Filament::auth()->user()->vendor_id);
        }
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    Section::make('客戶基本資料')
                        ->schema([
                            Forms\Components\Hidden::make('id')
                                ->hidden(),
                            Forms\Components\TextInput::make('vendor_id')
                                ->hidden(),
                            Forms\Components\TextInput::make('name')
                                ->label('客戶名稱')
                                ->required(),
                            Forms\Components\TextInput::make('mobile')
                                ->label('電話'),
                            Forms\Components\TextInput::make('address')
                                ->label('地址')
                                ->columnSpanFull(),
                            Forms\Components\TextInput::make('uncode')
                                ->label('統一編號'),
                            Forms\Components\TextInput::make('email')
                                ->label('Email'),

                        ])
                        ->columns(2),
                ]),
                Group::make([
                    Section::make('客戶基本資料')
                    ->schema([
                        Forms\Components\Toggle::make('status')
                            ->label('客戶狀態'),
                        Forms\Components\MarkdownEditor::make('note')
                            ->label('備註')
                            ->columnSpan(2),
                    ])
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                    Tables\Columns\TextColumn::make('name')
                        ->searchable()
                        ->label('客戶名稱'),
                    Tables\Columns\TextColumn::make('mobile')
                        ->searchable()
                        ->label('公司電話'),
                    // Tables\Columns\TextColumn::make('contact')
                    //     ->searchable()
                    //     ->label('聯絡人'),
                    Tables\Columns\TextColumn::make('uncode')
                        ->searchable()
                        ->label('統一編號'),
                    Tables\Columns\IconColumn::make('status')
                        ->label('狀態')
                        ->boolean(),
                    Tables\Columns\TextColumn::make('created_at')
            ])
            ->reorderable('sequence')
            ->defaultSort('sequence')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            'create' => Pages\CreateCustomer::route('/create'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
        ];
    }
}
