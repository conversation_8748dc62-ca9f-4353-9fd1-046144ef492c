<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\BlankDispatch;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\BDispatchResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\BDispatchResource\RelationManagers;
use Illuminate\Support\HtmlString;

class BDispatchResource extends Resource
{
    protected static ?string $model = BlankDispatch::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = '空白簽單管理';
    protected static ?string $navigationLabel = '簽單管理';
    protected static ?int $navigationSort = 1;

    public static function canViewAny(): bool
    {
        // Log::info('canViewAny00--'.auth()->user()->id);
        if(Filament::auth()->user()->id==1){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(Filament::auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery()->where('vendor_id', Filament::auth()->user()->vendor_id);
        }
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('customer')
                    ->label('客戶名稱')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('passenger')
                    ->label('乘客姓名')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('mobile')
                    ->label('乘客手機')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('driver_id')
                    ->label('司機姓名')
                    ->sortable()
                    ->searchable()
                    ->getStateUsing(function ($record) {
                        $driver = \App\Models\BlankDriver::find($record->driver_id);
                        return $driver ? $driver->name : '';
                    }),
                Tables\Columns\TextColumn::make('cost')
                    ->label('費用')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('start_date')
                    ->label('預約日期')
                    ->sortable()
                    ->searchable(),
            ])
            ->defaultSort('start_date', 'desc')
            ->striped()
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('view-dispatch-image')
                    ->label('檢視派車單')
                    ->icon('heroicon-o-eye')
                    ->action(function ($record, $livewire) {
                        // 這裡不做實際動作，僅用於開啟modal
                    })
                    ->modalHeading('派車單圖片預覽')
                    ->modalContent(function ($record) {
                        if ($record->image_path) {
                            $url = asset($record->image_path);
                            return new HtmlString("<img src='{$url}' style='max-width:100%;border:1px solid #ccc' />");
                        } else {
                            return new HtmlString('<span style=\"color:red\">無派車單圖片</span>');
                        }
                    })
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('關閉'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBDispatches::route('/'),
            'create' => Pages\CreateBDispatch::route('/create'),
            'edit' => Pages\EditBDispatch::route('/{record}/edit'),
        ];
    }
}
