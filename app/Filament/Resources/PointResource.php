<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use App\Models\BlankPoint;
use Filament\Tables\Table;
use App\Models\BlankDriver;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Filament\Actions\CreateAction;
use Illuminate\Support\Facades\Log;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Columns\Summarizers\Sum;
use App\Filament\Resources\PointResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\PointResource\RelationManagers;

class PointResource extends Resource
{
    protected static ?string $model = BlankPoint::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = '空白簽單管理';
    protected static ?string $navigationLabel = '點數管理';
    protected static ?int $navigationSort = 3;
    public static function canViewAny(): bool
    {
        // Log::info('canViewAny00--'.auth()->user()->id);
        if(Filament::auth()->user()->id==1){
            return true;
        }
        return false;
    }
    public static function getEloquentQuery(): Builder
    {
        // dd(auth()->user()->vendor_id);
        if(Filament::auth()->user()->vendor_id==0){
            return parent::getEloquentQuery();
        }else{
            return parent::getEloquentQuery();
        }
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('line_id')
                    ->label('司機 Line ID')
                    ->options(\App\Models\BlankDriver::pluck('name', 'line_id'))
                    ->searchable()
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function (
                        $state, $set
                    ) {
                        $driver = \App\Models\BlankDriver::where('line_id', $state)->first();
                        $set('name', $driver?->name ?? '');
                    }),
                Forms\Components\TextInput::make('name')
                    ->label('司機姓名')
                    ->required()
                    ->disabled()
                    ->dehydrated()
                    ->reactive()
                    ->afterStateUpdated(function (
                        $state, $set
                    ) {
                        $driver = \App\Models\BlankDriver::where('line_id', $state)->first();
                        $set('name', $driver?->name ?? '');
                    })
                    ->afterStateHydrated(function (
                        $state, $set, $record
                    ) {
                        if ($record && $record->line_id) {
                            $driver = \App\Models\BlankDriver::where('line_id', $record->line_id)->first();
                            $set('name', $driver?->name ?? '');
                        }
                    }),
                Forms\Components\Select::make('type')
                    ->label('類型')
                    ->options([
                        1 => '註冊',
                        2 => '建立表單',
                        3 => '編輯表單',
                        4 => '加值',
                    ])
                    ->default(1)
                    ->required(),
                Forms\Components\TextInput::make('point')
                    ->label('點數')
                    ->maxLength(255)
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('reason')
                    ->label('原因')
                    ->maxLength(255)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('blankdriver.name')
                    ->label('姓名')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->label('類型')
                    ->sortable()
                    ->searchable()
                    ->formatStateUsing(function ($state) {
                        return match ($state) {
                            1 => '註冊',
                            2 => '建立表單',
                            3 => '編輯表單',
                            4 => '加值',
                            default => $state,
                        };
                    }),
                Tables\Columns\TextColumn::make('point')
                    ->label('點數')
                    ->searchable()
                    ->summarize(Sum::make()
                        ->label('點數總和')
                        ->numeric(
                            decimalPlaces: 0,
                            decimalSeparator: '.',
                            thousandsSeparator: ',',
                        )),
                Tables\Columns\TextColumn::make('reason')
                    ->label('原因')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->label('建立日期')
                    ->searchable(),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('line_id')
                    ->label('Line ID')
                    ->options(BlankPoint::all()->pluck('line_id', 'line_id')->unique()->toArray())
                    ->searchable(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPoints::route('/'),
            'create' => Pages\CreatePoint::route('/create'),
            'edit' => Pages\EditPoint::route('/{record}/edit'),
        ];
    }

}
