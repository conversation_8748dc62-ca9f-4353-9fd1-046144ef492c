<?php

namespace App\Filament\Resources\DriverResource\Pages;

use Filament\Actions;
use Filament\Pages\Actions\ButtonAction;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\DriverResource;

class EditDriver extends EditRecord
{
    protected static string $resource = DriverResource::class;
    protected static ?string $title = '駕駛編輯';

    public function getFormActions(): array
    {
        return [
            $this->getSaveFormAction()->label('儲存'), // 自定义保存按钮的文本内容
            $this->getCancelFormAction()->label('取消'), // 自定义取消按钮的文本内容
            // $this->getDeleteFormAction(), // 保留默认的删除按钮
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
        ];
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    public function getBreadcrumbs(): array
    {
        return [
            // url('/') => '首頁',
            url('/admin/dispatches') => '駕駛',
            url('/admin/dispatches#') => '駕駛編輯',
        ];
    }
}
