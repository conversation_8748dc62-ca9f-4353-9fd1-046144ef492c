<?php

namespace App\Filament\Resources\DriverResource\Pages;

use App\Filament\Resources\DriverResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDrivers extends ListRecords
{
    protected static string $resource = DriverResource::class;
    protected static ?string $title = '駕駛列表';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return
        [
            // url('/') => '首頁',
            url('/admin/dispatches') => '駕駛',
            url('/admin/dispatches#') => '駕駛列表',
        ];
    }
}
