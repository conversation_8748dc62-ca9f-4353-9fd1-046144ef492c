<?php

namespace App\Filament\Resources\DriverResource\Widgets;

use App\Models\Driver;
use App\Models\Dispatch;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

class DriverStatsWidget extends BaseWidget
{
    public ?Driver $record;
    protected function getStats(): array
    {
        if(auth()->user()->vendor_id==0){
            $totalDriver =Driver::query()->count();
            $successActive = Driver::where('is_active', '=', 1)->count();
            $totalDispatch = Dispatch::query()->count();
        }else{
            $totalDriver =Driver::where('vendor_id', '=', auth()->user()->vendor_id)->count();
            $successActive = Driver::where('is_active', '=', 1)
                ->where('vendor_id', '=', auth()->user()->vendor_id)->count();
            $totalDispatch = Dispatch::where('vendor_id', '=', auth()->user()->vendor_id)->count();
        }
        return [
            Stat::make('駕駛註冊人數', $totalDriver)
                ->icon('heroicon-o-users')
                ->color('primary')
                ->chart([1,2,3,4,5,6,7])
                ->description('駕駛註冊總人數'),
            Stat::make('成功審核人數', $successActive)
                ->icon('heroicon-o-users')
                ->color('success')
                ->chart([1,2,3,4,5,6,7])
                ->description('審核成功駕駛總數'),
            Stat::make('派車單總數', $totalDispatch)
                ->icon('heroicon-o-truck')
                ->color('info')
                ->chart([1,5,2,1,2,6,7])
                ->description('所有派車單總數'),
        ];
    }
}
