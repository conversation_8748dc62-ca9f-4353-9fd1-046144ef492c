<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\BlankTopup;
use App\Models\BlankDriver;
use App\Models\User;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\BTopupResource\Pages;

class BTopupResource extends Resource
{
    protected static ?string $model = BlankTopup::class;
    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';
    protected static ?string $navigationGroup = '空白簽單管理';
    protected static ?string $navigationLabel = '儲值管理';
    protected static ?int $navigationSort = 3;

    public static function canViewAny(): bool
    {
        // Log::info('canViewAny00--'.auth()->user()->id);
        if(Filament::auth()->user()->id==1){
            return true;
        }
        return false;
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('line_id')
                    ->label('司機')
                    ->options(BlankDriver::all()->pluck('name', 'line_id'))
                    ->searchable()
                    ->required(),
                Forms\Components\TextInput::make('amount')
                    ->label('儲值金額')
                    ->numeric()
                    ->required(),
                Forms\Components\TextInput::make('point')
                    ->label('點數')
                    ->numeric()
                    ->required(),
                Forms\Components\Select::make('status')
                    ->label('狀態')
                    ->options([
                        'pending' => '待審核',
                        'approved' => '已通過',
                        'rejected' => '已拒絕',
                    ])
                    ->default('pending')
                    ->required()
                    ->live()
                    ->afterStateUpdated(function ($state, $set, $get, $record) {
                        if ($state === 'approved') {
                            $set('reviewed_at', now());
                            $set('reviewed_by', auth()->id());

                            // 確保只在狀態首次變更為 approved 時寫入
                            if ($record && $record->status !== 'approved') {
                                \App\Models\BlankPoint::create([
                                    'line_id' => $record->line_id,
                                    'name' => $record->driver?->name ?? '',
                                    'type' => 4,
                                    'point' => $record->point,
                                    'reason' => '儲值通過'
                                ]);
                                 // 更新司機總點數
                                $driver = BlankDriver::where('line_id', $record->line_id)->first();
                                if ($driver) {
                                    $driver->total_points = $driver->total_points + $record->point;
                                    $driver->save();
                                }
                            }
                        }
                    }),
                Forms\Components\DateTimePicker::make('reviewed_at')
                    ->label('審核日期')
                    ->nullable(),
                Forms\Components\Select::make('reviewed_by')
                    ->label('審核人員')
                    ->options(User::all()->pluck('name', 'id'))
                    ->searchable()
                    ->nullable(),
                Forms\Components\Textarea::make('note')
                    ->label('備註')
                    ->nullable(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('driver.name')->label('司機'),
                Tables\Columns\TextColumn::make('amount')->label('儲值金額'),
                Tables\Columns\TextColumn::make('point')->label('點數'),
                Tables\Columns\TextColumn::make('status')->label('狀態'),
                Tables\Columns\TextColumn::make('created_at')->label('建立日期')->dateTime(),
                Tables\Columns\TextColumn::make('reviewed_at')->label('審核日期')->dateTime(),
                Tables\Columns\TextColumn::make('reviewed_by')->label('審核人員')->getStateUsing(function ($record) {
                    $user = User::find($record->reviewed_by);
                    return $user ? $user->name : '';
                }),
                Tables\Columns\TextColumn::make('note')->label('備註')->limit(20),
            ])
            ->defaultSort('created_at', 'desc')
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBTopups::route('/'),
            'create' => Pages\CreateBTopup::route('/create'),
            'edit' => Pages\EditBTopup::route('/{record}/edit'),
        ];
    }
}
