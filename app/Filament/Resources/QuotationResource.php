<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Driver;
use App\Models\Dispatch;
use Filament\Forms\Form;
use App\Models\Quotation;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use App\Traits\CreateDispatch;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Filament\Forms\Components\Grid;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;

use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Enums\FiltersLayout;
use Illuminate\Database\Eloquent\Builder;
// use Filament\Actions\Modal\Actions\Action;
use Illuminate\Support\Facades\Validator;
use App\Filament\Resources\QuotationResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;


class QuotationResource extends Resource
{
    use CreateDispatch;
    protected static ?string $model = Quotation::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel = '詢價/預約管理';
    public static function canViewAny(): bool
    {
        // Log::info('canViewAny00--'.auth()->user()->id);
        if(Filament::auth()->user()->id==1 || Filament::auth()->user()->id==9 || Filament::auth()->user()->id==15){
            return true;
        }
        return false;
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->label('ID')
                    ->toggleable()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('status')->label('狀態')
                    ->toggleable()
                    ->badge()
                    ->getStateUsing(function ($record) {
                        if($record->status==0){
                            return trim("詢價");
                        }else{
                            return trim(string: "預約");
                        }
                    })
                    ->color(fn (string $state): string => match ($state) {
                        "詢價" => 'success',
                        "預約" => 'danger',
                    }),
                TextColumn::make('order_type')->label('類別')
                    ->toggleable()
                    ->getStateUsing( function (Model $record){
                        if($record['order_type'] == 0){
                            return '接機';
                        }else if($record['order_type'] == 1){
                            return '送機';
                        }else if($record['order_type'] == 2){
                            return '包車';
                        } else {
                            return '未知'; // Or return an empty string: '';
                        }
                    })
                    ->color(fn (string $state): string => match ($state) {
                        '接機' => 'success',
                        '送機' => 'primary',
                        '包車' => 'danger',
                        default => 'gray', // Add a default for the color match
                    }),
                TextColumn::make( 'car_type')->label('車型')
                    ->getStateUsing( function (Model $record){
                        if ($record['vendor_id'] == 17) {
                            if ($record['car_type'] == 5) {
                                return '舒適小車';
                            } elseif ($record['car_type'] == 9) {
                                return '舒適大車';
                            } elseif ($record['car_type'] == 91) {
                                return '尊榮大車';
                            }
                            // 如果 vendor_id == 2 但 car_type 不是 5, 9, 或 91, 則繼續執行下面的通用邏輯
                        }

                        // 一般情況或 vendor_id == 2 但 car_type 不匹配特殊情況
                        if($record['car_type'] == 5){
                            return '五人轎車';
                        }else if($record['car_type'] == 51){
                            return '五人休旅車';
                        }else if($record['car_type'] == 9){
                            return '九人座';
                        }else{
                            return '五人轎車'; // 預設
                        }
                    }),
                TextColumn::make('nickname')->label('暱稱')
                        ->toggleable()
                        ->searchable()
                        ->sortable(),
                TextColumn::make('contact_info')
                    ->label('聯絡人/電話')
                    ->getStateUsing(function ($record) {
                        return $record->passenger_name . "\n" . $record->passenger_mobile;
                    })
                    ->searchable(query: function ($query, $search) {
                        $query->where(function($q) use ($search) {
                            $q->where('passenger_name', 'like', "%{$search}%")
                                ->orWhere('passenger_mobile', 'like', "%{$search}%");
                        });
                    })
                    ->wrap()
                    ->formatStateUsing(fn($state) => str_replace("\n", "<br>", $state))
                    ->html(),
                TextColumn::make('location_from_name')->label('上車地點')
                    ->formatStateUsing(function ($record) {
                        $city = $record->location_from_name ?? '';
                        $city2 = $record->location_city_name ?? '';
                        $district = $record->location_district_name ?? '';
                        $address = $record->passenger_address ?? '';
                        if($record->status == 1){
                            if($record->order_type == 0){
                                return trim("{$city}");
                            }else if($record->order_type == 1 || $record->order_type == 2){
                                return trim("{$city2} {$district}" . (!empty($address) ? "<br>{$address}" : ''));
                            }
                        }else{
                            if($record->order_type == 0){
                                return trim("{$city}");
                            }else if($record->order_type == 1 || $record->order_type == 2){
                                return trim("{$city2} {$district}");
                            }
                        }
                        return ''; // 新增：確保總是有字串返回
                    })
                    ->wrap()
                    ->html(),
                TextColumn::make('location_district_name')->label('下車地點')
                    ->formatStateUsing(function ($record) {
                        $city = $record->location_city_name ?? '';
                        $district = $record->location_district_name ?? '';
                        $from = $record->location_from_name ?? '';
                        $address = $record->passenger_address ?? '';
                        if($record->status == 1){
                            if($record->order_type == 0){
                                // 接機：下車地點顯示完整（縣市+區+換行+地址）
                                $full = trim("{$city} {$district}");
                                return !empty($address) ? $full . "<br>" . $address : $full;
                            }else if($record->order_type == 1){
                                // 送機：下車地點只顯示機場
                                return trim("{$from}");
                            }
                        }else{
                            if($record->order_type == 0){
                                return trim("{$city} {$district}");
                            }else if($record->order_type == 1){
                                return trim("{$from}");
                            }
                        }
                        return ''; // 新增：確保總是有字串返回
                    })
                    ->wrap()
                    ->html(),
                TextColumn::make('appointment_date')->label('預約日期/時間')
                    ->getStateUsing(function ($record) {
                        if(empty($record->appointment_date)) return '';
                        $dt = explode(' ', $record->appointment_date);
                        return count($dt) == 2 ? $dt[0] . "\n" . $dt[1] : $record->appointment_date;
                    })
                    ->wrap()
                    ->formatStateUsing(fn($state) => str_replace("\n", "<br>", $state))
                    ->html(),
                TextColumn::make('num_of_people')->label('乘車人數')
                    ->toggleable(),
                TextColumn::make('num_of_bags')->label('行李數')
                    ->toggleable(),
                TextColumn::make('flightno')->label('航班編號'),
                TextColumn::make('total')->label('金額')
                    ->toggleable()
                    ->searchable(),
                // 添加备注列，使用图标按钮显示
                // 使用IconColumn显示图标，点击后显示popover
                Tables\Columns\IconColumn::make('note')
                    ->toggleable()
                    ->label('備註')
                    ->icon(fn ($record): string => !empty($record->note) ? 'heroicon-s-information-circle' : 'heroicon-o-minus-circle')
                    ->color(fn ($record): string => !empty($record->note) ? 'danger' : 'gray')
                    ->size('md')
                    ->tooltip(fn ($record): ?string => !empty($record->note) ? '點擊查看備註' : '無備註')
                    ->action(function ($record, $column) {
                        if (empty($record->note)) {
                            // 如果没有备注，显示提示
                            $column->getLivewire()->js('$tooltip("此記錄無備註內容", { timeout: 1500 });');
                            return;
                        }

                        // 使用Filament的通知系统显示备注内容，设置为红色背景
                        Notification::make()
                            ->title('備註詳情')
                            ->body($record->note)
                            ->persistent()
                            ->color('danger') // 设置为红色
                            ->icon('heroicon-s-information-circle') // 添加图标
                            ->iconColor('danger') // 图标也设置为红色
                            ->actions([
                                \Filament\Notifications\Actions\Action::make('close')
                                    ->label('關閉')
                                    ->color('white')
                                    ->close(),
                            ])
                            ->send();
                    }),
            ])
            ->defaultSort('id', 'desc')
            ->striped()
            ->filters([
                // Tables\Filters\TrashedFilter::make(),
                Filter::make('status')
                    ->label('狀態')
                    ->default(0)
                    ->query(function ($query, $data) {
                        if ($data['status']==0 || $data['status']==1 || $data['status']==2) {
                            // dd($data['status']);
                            $query->where('status', $data['status']);
                        }
                    })
                    ->form([
                        Radio::make('status')
                            ->label('狀態')
                            ->inline()
                            ->default(-1) // 設置預設為 "全部"
                            ->live()
                            ->options(function () {
                                // 根據用戶ID決定是否顯示"已建派車單"選項
                                if (Filament::auth()->user()->id == 1) {
                                    return [
                                        -1 => '全部', // 新增 "全部" 選項
                                        0 => '詢價',
                                        1 => '預約',
                                        2 => '已建派車單',
                                    ];
                                } else {
                                    return [
                                        -1 => '全部', // 新增 "全部" 選項
                                        0 => '詢價',
                                        1 => '預約',
                                    ];
                                }
                            }),
                    ]),
                Filter::make('order_type')
                    ->label('類別')
                    // ->default(0) // 移除或註解此行，讓 Radio 的 default 生效
                    ->query(function ($query, $data) {
                        $orderType = $data['order_type'] ?? null;
                        // 只有當選擇的不是 "全部" (-1) 時，才加入 order_type 的篩選條件
                        if (isset($orderType) && $orderType != -1) {
                            $query->where('order_type', $data['order_type']);
                        }
                    })
                    ->form([
                        Radio::make('order_type')
                            ->label('類別')
                            ->inline()
                            ->default(-1) // 設置預設為 "全部"
                            ->live()
                            ->options([
                                -1 => '全部', // 新增 "全部" 選項
                                0 => '接機',
                                1 => '送機',
                                2 => '包車',
                            ])
                        ]),
                Filter::make('appointment_date')
                    ->label('預約日期')
                    ->form([
                        Forms\Components\DatePicker::make('from')->label('起始日'),
                        Forms\Components\DatePicker::make('to')->label('結束日'),
                    ])
                    ->query(function ($query, $data) {
                        if (!empty($data['from'])) {
                            $query->whereDate('appointment_date', '>=', $data['from']);
                        }
                        if (!empty($data['to'])) {
                            $query->whereDate('appointment_date', '<=', $data['to']);
                        }
                    }),
            ], layout: FiltersLayout::AboveContent)
            ->actions([
                Tables\Actions\ActionGroup::make([
                    \Filament\Tables\Actions\Action::make('copy_text')
                        ->label('拷貝預約文字')
                        ->icon('heroicon-o-clipboard')
                        ->action(function ($livewire, $record) {
                            $orderType = $record->order_type == 0 ? '接機' : ($record->order_type == 1 ? '送機' : '包車');
                            if($record->vendor_id == 17){
                                $carType = match($record->car_type) {
                                    5 => '舒適小車',
                                    9 => '舒適大車',
                                    91 => '尊爵大車',
                                    default => '其他'
                                };
                            }else{
                                $carType = match($record->car_type) {
                                    5 => '5人座小客車',
                                    51 => '5人座休旅車',
                                    9 => '九人座車',
                                    91 => '賓士Vito九人座',
                                    10 => 'Alphard尊爵',
                                    default => '其他'
                                };
                            }
                            $textToCopy =
                                "類別：{$orderType}\n"
                                . "乘車人姓名：{$record->passenger_name}\n"
                                . "聯絡手機：{$record->passenger_mobile}\n"
                                . "班機編號：{$record->flightno}\n"
                                . "乘車時間：{$record->appointment_date}\n"
                                . "上車地點：{$record->location_from_name}\n"
                                . "下車地點：{$record->location_city_name} {$record->location_district_name} {$record->passenger_address}\n"
                                . "搭乘人數：{$record->num_of_people} 人\n"
                                . "行李數：" . ($record->num_of_bags ?? 0) . " 件（如有，請告知）\n"
                                . "車輛類型：{$carType}\n"
                                . "價格：NT$ " . number_format($record->total)."元 \n"
                                . "備註： {$record->note}";
                            $tooltipStr = empty($textToCopy) ? '目前無文字內容' : '已拷貝到剪貼簿中!';
                            $livewire->js(
                                'window.navigator.clipboard.writeText(' . json_encode($textToCopy) . ');'
                                . '$tooltip(' . json_encode(__($tooltipStr)) . ', { timeout: 1500 });'
                            );
                        }),
                    \Filament\Tables\Actions\Action::make('created_dispatch')
                        ->label('建立派車單')
                        ->visible(function (Quotation $record){
                            return $record->status==1;
                        })
                        ->form(function (Quotation $record){
                            return [
                                Grid::make(3) // 分 3 列
                                    ->schema([
                                        Hidden::make('quotation_id')
                                            ->default($record->id),
                                        Select::make('driver_id')
                                            ->label('司機')
                                            ->options(Driver::where('vendor_id', Filament::auth()->user()->vendor_id)->pluck('name', 'id')),
                                        TextInput::make('passenger_name')
                                            ->default($record->passenger_name)
                                            ->label('聯絡人'),
                                        TextInput::make('passenger_mobile')
                                            ->default($record->passenger_mobile)
                                            ->label('聯絡電話'),
                                        TextInput::make('passenger_address')
                                            ->default($record->passenger_address)
                                            ->label('地址'),
                                        TextInput::make('total')
                                            ->default($record->total)
                                            ->label('金額'),
                                        TextInput::make('id')
                                            ->default($record->id)
                                            ->label('編號'),
                                    ]),
                            ];
                        })
                        ->action(function (array $data
                        , Quotation $record) {
                            $validator = Validator::make($data, [
                                'driver_id' => 'required',
                                'passenger_name' => 'required',
                                'passenger_mobile' => 'required',
                                'passenger_address' => 'required',
                                'total' => 'required',
                            ]);
                            if ($validator->fails()) {

                                Notification::make()
                                    ->title('表單驗證錯誤')
                                    ->body('請檢查所有必填欄位並重新提交。')
                                    ->danger()
                                    ->send();
                                return;
                            }
                            $validatedData = $validator->validated();
                            // dd($validatedData);
                            $validatedData['status'] = 2;
                            $record->update($validatedData);
                            $quotation_id = $record->id;
                            $driver_id = $validatedData['driver_id'];
                            // $driver = Driver::find($driver_id);
                            $rs = Dispatch::create([
                                'dispatch_id' => 'DP_' . date('YmdHis') . Str::random(3),
                                'driver_id' => $driver_id,
                                // 'driver_name' => $driver->name,
                                // 'driver_mobile' => $driver->mobile,
                                // 'driver_address' => $driver->address,
                                'customer_name' => $validatedData['passenger_name'],
                                'customer_mobile' => $validatedData['passenger_mobile'],
                                'customer_address' => $validatedData['passenger_address'],
                                // 'total' => $validatedData['total'],
                                'start_time' => $record->appointment_date,
                                'end_time' => $record->appointment_date,
                                'route' => $record->location_from_name.'-'.$validatedData['passenger_address'],
                                'rental_cost' => $record->order_type,
                                'flight_no' => $record->flightno,
                                'vendor_id' => Filament::auth()->user()->vendor_id,
                                'quotation_id' => $quotation_id
                            ]);
                            $rsimage = static::createDispatchImage($rs->id);
                            // dd($rsimage);
                            if($rsimage){
                                $dispatch = Dispatch::find($rs->id);
                                $dispatch->update(['image_path' => $rsimage]);
                            }
                            Notification::make()
                                ->title('派車單建立成功')
                                ->body('新的派車單已成功建立！')
                                ->success() // 設定通知類型為成功
                                ->send();
                            return;
                            // $action->success();
                        }),
                    Tables\Actions\DeleteAction::make()
                        ->label('刪除')
                        ->modalHeading('確認刪除此筆資料!')
                        ->modalSubheading('刪除後無法恢復，請確認是否刪除!')
                        ->modalButton('刪除')
                ])
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('刪除選取資料')
                        ->modalHeading('確認刪除此筆資料!')
                        ->modalSubheading('刪除後無法恢復，請確認是否刪除!')
                        ->modalButton('刪除'),
                    // Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\BulkAction::make('export_selected')
                        ->label('匯出選取資料')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->action(function ($records) {
                            $ids = $records->pluck('id')->toArray();
                            $url = route('export.quotations', ['ids' => implode(',', $ids)]);
                            return redirect($url);
                        })
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQuotations::route('/'),
            // 'create' => Pages\CreateQuotation::route('/create'),
            // 'edit' => Pages\EditQuotation::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        // return parent::getEloquentQuery()
        //     ->withoutGlobalScopes([
        //         SoftDeletingScope::class,
        //     ]);
            if(Filament::auth()->user()->vendor_id==0){
                return parent::getEloquentQuery()
                    // ->where('status', 0)
                    ->where('deleted_at', null)
                    ->withoutGlobalScopes([
                        SoftDeletingScope::class,
                    ]);
            }else{
                return parent::getEloquentQuery()
                    // ->where('status', 0)
                    ->where('deleted_at', null)
                    ->where('vendor_id', Filament::auth()->user()->vendor_id)
                    ->withoutGlobalScopes([
                        SoftDeletingScope::class,
                    ]);;
            }
    }
}
