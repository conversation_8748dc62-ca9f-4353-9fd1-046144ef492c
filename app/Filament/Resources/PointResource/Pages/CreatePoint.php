<?php

namespace App\Filament\Resources\PointResource\Pages;

use Filament\Actions;
use App\Models\BlankPoint;
use App\Models\BlankDriver;
use Illuminate\Support\Facades\Log;
use App\Filament\Resources\PointResource;
use Filament\Resources\Pages\CreateRecord;

class CreatePoint extends CreateRecord
{
    protected static string $resource = PointResource::class;
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function afterCreate()
    {
        // dd('afterCreate');

            // 1. 取得 line_id
            $lineId = $this->record->line_id;

            // 2. 根據 line_id 找到 BlankDriver 記錄
            $driver = BlankDriver::where('line_id', $lineId)->first();

            if ($driver) {
                // 3. 計算該 line_id 的總點數
                $totalPoints = BlankPoint::where('line_id', $lineId)->sum('point');
                Log::info('更新 BlankDriver 點數成功: ' . $lineId . ' - 總點數: ' . $totalPoints);

                // 4. 更新 BlankDriver 記錄的 point 欄位
                $driver->total_points = $totalPoints;
                $driver->save();
            } else {
                Log::warning('找不到 BlankDriver 記錄: ' . $lineId);
            }

    }
}
