<?php

namespace App\Filament\Resources\TourDriverResource\Pages;

use App\Filament\Resources\TourDriverResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditTourDriver extends EditRecord
{
    protected static string $resource = TourDriverResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
