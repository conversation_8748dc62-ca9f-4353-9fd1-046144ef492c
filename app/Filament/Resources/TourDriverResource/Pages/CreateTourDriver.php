<?php

namespace App\Filament\Resources\TourDriverResource\Pages;

use App\Filament\Resources\TourDriverResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateTourDriver extends CreateRecord
{
    protected static string $resource = TourDriverResource::class;
    protected static ?string $title = '建立司機';
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
