<?php

namespace App\Filament\Resources\BDriverResource\Pages;

use App\Filament\Resources\BDriverResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBDriver extends EditRecord
{
    protected static string $resource = BDriverResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
