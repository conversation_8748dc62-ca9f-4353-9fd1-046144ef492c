<?php

namespace App\Filament\Resources\BDriverResource\Pages;

use App\Filament\Resources\BDriverResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBDrivers extends ListRecords
{
    protected static string $resource = BDriverResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
