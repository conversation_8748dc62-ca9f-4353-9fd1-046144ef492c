<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Reserve;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\ReserveResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\ReserveResource\RelationManagers;

class ReserveResource extends Resource
{
    protected static ?string $model = Reserve::class;

    protected static ?string $navigationIcon = 'heroicon-c-rectangle-stack';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationLabel = '預約車輛管理';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make()->schema([
                    Section::make('預約資訊')->schema([

                        Forms\Components\TextInput::make('reserve_id')
                            ->label('預約單號')
                            ->disabled()
                            ->required(),
                        Forms\Components\TextInput::make('customer_name')
                            ->label('客戶名稱')
                            ->disabled()
                            ->required(),
                        Forms\Components\TextInput::make('customer_mobile')
                            ->label('連絡電話')
                            ->disabled()
                            ->required(),
                        Forms\Components\Select::make('type')
                            ->placeholder('請選擇')
                            ->label('類別')
                            ->required()
                            ->options([
                                '0' => '機場接機',
                                '1' => '機場送機',
                                '2' => '預約包車',
                            ]),
                        Forms\Components\DatePicker::make('reserve_date')
                            ->label('預約日期')
                            ->required(),
                        Forms\Components\TimePicker::make('reserve_time')
                            ->label('預約時間')
                            ->required(),
                        Forms\Components\TextInput::make('no_of_passengers')
                            ->label('乘客人數')
                            ->required(),
                        Forms\Components\TextInput::make('no_of_bags')
                            ->label('行李數')
                            ->required(),
                        Forms\Components\Select::make('status')
                            ->placeholder('請選擇')
                            ->label('狀態')
                            ->required()
                            ->options([
                                '0' => '未處理',
                                '1' => '派單中',
                                '2' => '已完成',
                            ]),
                    ])->columns(2),
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('reserve_id')
                    ->label('預約單號')
                    ->searchable(),
                Tables\Columns\TextColumn::make('customer_name')
                    ->label('客戶名稱')
                    ->searchable(),
                Tables\Columns\TextColumn::make('customer_mobile')
                    ->label('連絡電話')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->label('類別')
                    ->searchable()
                    ->getStateUsing( function (Model $record){
                        if($record['type'] == 0){
                            return '機場接機';
                        }else if($record['type'] == 1){
                            return '機場送機';
                        }else{
                            return '預約包車';
                        }
                    })
                    ->color(fn (string $state): string => match ($state) {
                        '機場接機' => 'info',
                        '機場送機' => 'success',
                        '預約包車' => 'danger',
                    }),
                Tables\Columns\TextColumn::make('reserve_date')
                    ->label('預約日期')
                    ->searchable(),
                Tables\Columns\TextColumn::make('no_of_passengers')
                    ->label('乘客人數')
                    ->searchable(),
                Tables\Columns\TextColumn::make('no_of_bags')
                    ->label('行李數')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('狀態')
                    ->searchable()
                    ->badge()
                    ->getStateUsing( function (Model $record){
                        if($record['status'] == 0){
                            return '未處理';
                        }else if($record['status'] == 1){
                            return '派單中';
                        }else{
                            return '已完成';
                        }
                    })
                    ->color(fn (string $state): string => match ($state) {
                        '未處理' => 'gray',
                        '派單中' => 'danger',
                        '已完成' => 'success',
                    }),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReserves::route('/'),
            'create' => Pages\CreateReserve::route('/create'),
            'edit' => Pages\EditReserve::route('/{record}/edit'),
        ];
    }
}
