<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\Customer;
use Filament\Forms\Form;
use App\Models\TourGuide;
use App\Models\Rentcarfee;
use App\Models\TourDriver;
use Filament\Tables\Table;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Filament\Forms\Components\Grid;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\RentcarfeeResource\Pages;
use App\Filament\Resources\RentcarfeeResource\RelationManagers;

class RentcarfeeResource extends Resource
{
    protected static ?string $model = Rentcarfee::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = '旅遊管理';
    protected static ?string $navigationLabel = '車輛租賃管理';
    protected static ?int $navigationSort = 0;


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make()->schema([
                    Section::make('客戶資料')->schema([
                        Forms\Components\DatePicker::make('rent_car_date')
                            ->label('租賃日期'),
                        Forms\Components\Select::make('customer_id')
                            ->label('客戶名稱')
                            ->options(Customer::all()->pluck('name', 'id')),
                        Forms\Components\TextInput::make('title')
                            ->label('團號')
                            ->columnSpanFull(),
                    ])->columns(2),
                ]),
                Group::make()->schema([
                    Section::make('車輛資料')->schema([
                        Grid::make(3)->schema([
                            Forms\Components\Select::make('car_type')
                                ->label('車型')
                                ->options([
                                    5 => '5人車',
                                    9 => '9人車',
                                ]),
                            Forms\Components\Select::make('driver_id')
                                ->label('司機')
                                ->options(TourDriver::all()->pluck('name', 'id'))
                                ->searchable(),
                            Forms\Components\Select::make('tour_guide_id')
                                ->label('導遊')
                                ->options(TourGuide::all()->pluck('name', 'id'))
                                ->searchable(),
                        ]),
                        Forms\Components\TextInput::make('journey')
                            ->label('行程')
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('rental_cost')
                            ->label('車資'),
                    ])->columns(2),
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('rent_car_date')
                    ->label('租賃日期')
                    ->sortable()
                    ->searchable()
                    ->date('Y-m-d'),
                // Tables\Columns\TextColumn::make('customer.name')
                //     ->label('客戶名稱')
                //     ->searchable(),
                // Tables\Columns\TextColumn::make('title')
                //     ->label('團號')
                //     ->searchable(),
                Tables\Columns\TextColumn::make('customer.name')
                    ->label('客戶名稱 / 團號')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->customer->name . '<br>' . $record->title;
                    })
                    ->searchable(query: function (Builder $query, string $search) {
                        return $query->where(function ($query) use ($search) {
                            $query->where('title', 'like', "%{$search}%")
                                    ->orWhereHas('customer', function ($q) use ($search) {
                                        $q->where('name', 'like', "%{$search}%");
                                    });
                        });
                    })
                    ->html(),
                Tables\Columns\TextColumn::make('car_type')
                    ->label('車型')
                    ->searchable(),
                Tables\Columns\TextColumn::make('driver.name')
                    ->label('司機')
                    ->searchable(),
                Tables\Columns\TextColumn::make('guide.name')
                    ->label('導遊')
                    ->searchable(),
                Tables\Columns\TextColumn::make('journey')
                    ->label('行程')
                    ->searchable(),
                Tables\Columns\TextColumn::make('rental_cost')
                    ->label('車資')
                    ->searchable(),
                Tables\Columns\TextColumn::make('total')
                    ->label('總計')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('狀態')
                    ->searchable(),
            ])
            ->filters([
                SelectFilter::make('customer_id')
                    ->label('司機')
                    ->options(Customer::where('vendor_id', Filament::auth()->user()->vendor_id)
                        ->pluck('name', 'id')),
                Filter::make('rent_car_date')
                    ->form([
                        DatePicker::make('start_date')
                            ->label('開始日期')
                            ->placeholder('請選擇開始日期'),
                        DatePicker::make('end_date')
                            ->label('結束日期')
                            ->placeholder('請選擇結束日期'),
                    ])
                    ->columns(2)
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when($data['start_date'], fn ($query, $date) => $query->whereDate('rent_car_date', '>=', $date))
                            ->when($data['end_date'], fn ($query, $date) => $query->whereDate('rent_car_date', '<=', $date));
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['start_date']) {
                            $indicators['start_date'] = '開始日期: ' . $data['start_date'];
                        }
                        if ($data['end_date']) {
                            $indicators['end_date'] = '結束日期: ' . $data['end_date'];
                        }
                        return $indicators;
                    }),
            ], layout: FiltersLayout::AboveContent)
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRentcarfees::route('/'),
            'create' => Pages\CreateRentcarfee::route('/create'),
            'edit' => Pages\EditRentcarfee::route('/{record}/edit'),
        ];
    }
}
