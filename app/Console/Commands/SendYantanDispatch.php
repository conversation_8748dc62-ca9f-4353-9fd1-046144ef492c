<?php

namespace App\Console\Commands;

use App\Traits\Traits\ScheduleDispatch;
use Illuminate\Console\Command;
// use App\Traits\CreateYatanDispatch;

class SendYantanDispatch extends Command
{

    use ScheduleDispatch;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:yantan-dispatch';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '雅潭 佳能派車單';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        static::sendScheduleDispatch(7);
    }
}
