<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use App\Notifications\InvoicePaid;
use Illuminate\Support\Facades\Notification;

class SendUserCount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:user-count';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        info('send user count mail sttart');
        $count = User::whereDate('created_at', today())->count();
        info('send user count'.$count);
        $text = '標題: '.$count;
        // $rs = Mail::to('<EMAIL>')->send(new SendUserCountMail($count));
        Notification::route('slack', config('notifications.slack.invoice_paid'))
                ->notify(new InvoicePaid($text));
        // var_dump($rs);
        info('send user count mail end');
    }
}
