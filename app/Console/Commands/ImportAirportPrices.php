<?php

namespace App\Console\Commands;

use App\Models\City;
use App\Models\Price;
use League\Csv\Reader;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportAirportPrices extends Command
{
    protected $signature = 'import:airport-prices {csvFile} {vendorId} {orderType} {airportIndex?}';
    protected $description = 'Import airport transfer prices from a CSV file to rent_car_prices table.';

    protected $carTypeMapping = [
        '舒適小車' => 5,  // 請根據您的實際 car_type 定義
        '舒適大車' => 9,
        '尊榮車款' => 91,
    ];

    protected $airportList = [
        ["from_district_id" => "110900", "from_district_name" => "松山機場"],
        ["from_district_id" => "110903", "from_district_name" => "桃園機場"],
        ["from_district_id" => "120901", "from_district_name" => "台中機場"],
    ];

    public function handle()
    {
        // airport.csv 18 0 0, 18-vendor_id, 0-order_type, 0-airportIndex
        $csvFile = $this->argument('csvFile');
        $vendorId = $this->argument('vendorId');
        $orderType = $this->argument('orderType');
        $airportIndex = (int)($this->argument('airportIndex') ?? 0); // 新增參數
        $airport = $this->airportList[$airportIndex] ?? $this->airportList[0];
        // $fromDistrictId = $airport['from_district_id'];
        // $fromDistrictName = $airport['from_district_name'];

        if (!file_exists($csvFile)) {
            $this->error("CSV file not found: {$csvFile}");
            return;
        }

        $csv = Reader::createFromPath($csvFile, 'r');
        $csv->setHeaderOffset(0);  // Assuming the first row is the header

        $records = $csv->getRecords();
        $importedCount = 0;

        DB::beginTransaction();
        try {
            foreach ($records as $record) {
                $county = trim($record['縣市']);
                $districtString = trim($record['鄉/鎮/區']);

                // 查詢縣市 ID
                $city = City::where('city', 'like', '%' . $county . '%')->first();

                if ($city) {
                    $fromCityId = $city->city_code;
                    $fromCityName = $city->city;

                    $districts = explode('、', $districtString);

                    foreach ($districts as $districtName) {
                        // 這裡強制使用機場參數，不再查詢 district
                        $fromDistrictName = trim($districtName);
                        $district = City::where('city', $fromDistrictName)
                            ->where('city_code', $fromCityId)
                            ->whereNotNull('district_code')
                            ->first();
                        $fromDistrictId = $district ? $district->district_code : null;

                        // 取得區域代碼與名稱
                        $toArea = City::where('city_code', $fromCityId)
                            ->whereNotNull('area_code')
                            ->first();
                        $toAreaId = $toArea ? $toArea->area_code : null;
                        $toAreaName = null;
                        if ($toAreaId) {
                            $area = City::where('area_code', $toAreaId)
                                ->whereNull('city_code')
                                ->whereNull('district_code')
                                ->first();
                            $toAreaName = $area ? $area->city : null;
                        }

                        // 寫入不同車型的價格
                        foreach ($this->carTypeMapping as $carTypeName => $carType) {
                            $price = trim($record[$carTypeName]);

                            if (is_numeric($price)) {
                                $exists = Price::where('vendor_id', $vendorId)
                                    ->where('order_type', $orderType)
                                    ->where('car_type', $carType)
                                    ->where('from_district_id', $fromDistrictId)
                                    ->where('to_city_id', $fromCityId)
                                    ->where('to_district_id', $fromDistrictId)
                                    ->first();

                                if (!$exists) {
                                    Price::create([
                                        'vendor_id' => $vendorId,
                                        'order_type' => $orderType,
                                        'car_type' => $carType,
                                        'from_district_id' => $airport['from_district_id'],
                                        'from_district_name' => $airport['from_district_name'],
                                        'to_area_id' => $toAreaId,
                                        'to_area_name' => $toAreaName,
                                        'to_city_id' => $fromCityId,
                                        'to_city_name' => $fromCityName,
                                        'to_district_id' => $fromDistrictId,
                                        'to_district_name' => $fromDistrictName,
                                        'price' => $price,
                                        'note' => 'Imported from CSV',
                                        'status' => 0,  // 或您希望設定的預設狀態
                                        'created_at' => now(),
                                        'updated_at' => now(),
                                    ]);
                                    $importedCount++;
                                } else {
                                    $this->warn("Skipping existing record: {$fromCityName} - {$fromDistrictName} - {$carTypeName}");
                                }
                            }
                        }
                    }
                } else {
                    $this->error("City not found: {$county}");
                }
            }

            DB::commit();
            $this->info("Successfully imported {$importedCount} records.");
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("Import failed: {$e->getMessage()}");
        }
    }
}
