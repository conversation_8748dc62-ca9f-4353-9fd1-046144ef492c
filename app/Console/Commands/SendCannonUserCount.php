<?php

namespace App\Console\Commands;

use Log;
use App\Models\Dispatch;
use Illuminate\Console\Command;
use App\Notifications\CannonReport;
use App\Traits\Traits\ScheduleDispatch;
use Illuminate\Support\Facades\Notification;

class SendCannonUserCount extends Command
{
    use ScheduleDispatch;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:cannon-user-count';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cannon 固定班表檢查';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $count = Dispatch::where('customer_name', 'like', '%佳能%')
            ->whereDate('start_time', today())
            ->count();
        $signCount = Dispatch::where('customer_name', 'like', '%佳能%')
            ->whereDate('start_time', today())
            ->whereNotNull('signature_file')->count();
        $priceCount = Dispatch::where('customer_name', 'like', '%佳能%')
            ->whereDate('start_time', today())
            ->where('rental_cost', 400)->count();
        info('send user count' . $count);
        $text = '佳能每日報表 : ' . date('Y-m-d');
        // $signCount = Dispatch::where('customer_name', 'like', '%佳能%');
        // $rs = Mail::to('<EMAIL>')->send(new SendUserCountMail($count));
        Notification::route('slack', config('notifications.slack.invoice_paid'))
            ->notify(new CannonReport($text, $count, $signCount, $priceCount));
    }
}
