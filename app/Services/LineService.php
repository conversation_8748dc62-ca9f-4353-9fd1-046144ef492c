<?php
namespace App\Services;

use App\Models\Driver;
use LINE\Constants\HTTPHeader;
use Illuminate\Support\Facades\Log;
use LINE\Parser\EventRequestParser;
use LINE\Webhook\Model\FollowEvent;
use LINE\Webhook\Model\MessageEvent;
use LINE\Webhook\Model\UnfollowEvent;
use LINE\Webhook\Model\TextMessageContent;
use LINE\Clients\MessagingApi\Configuration;
use LINE\Clients\MessagingApi\Model\TextMessage;
use LINE\Clients\MessagingApi\Api\MessagingApiApi;
use LINE\Parser\Exception\InvalidSignatureException;
use LINE\Parser\Exception\InvalidEventRequestException;
use LINE\Clients\MessagingApi\Model\ReplyMessageRequest;

class LineService
{
     public function webhook($req){
        $channelToken = config('line.channel_access_token');
        $config = new Configuration();
        $config->setAccessToken($channelToken);
        $bot = new MessagingApiApi(new \GuzzleHttp\Client(), $config);
        $signature = $req->header(HTTPHeader::LINE_SIGNATURE);
        if (empty($signature)) {
            return abort(400, 'Bad Request');
        }

        // Check request with signature and parse request
        try {
            $secret = config('line.channel_secret');
            $parsedEvents = EventRequestParser::parseEventRequest($req->getContent(), $secret, $signature);
        } catch (InvalidSignatureException $e) {
            return abort(400, 'Bad signature');
        } catch (InvalidEventRequestException $e) {
            return abort(400, "Invalid event request");
        }
        Log::info('Hello '.$channelToken);
        try {
            $parsedEvents = EventRequestParser::parseEventRequest($req->getContent(), $secret, $signature);
            foreach ($parsedEvents->getEvents() as $event) {
                $line_id = $event->getSource()->getUserId();
                Log::info('Hello '.$line_id);
                if ($event instanceof FollowEvent) { // お友達の登録したとき
                    // $user_id = $event->getUserId();
                    // User::AddDate($user_id);
                    Log::info('follow Event:: 已加入 '.$line_id);
                    $profile = $bot->getProfile($line_id);
                    $display_name = $profile->getDisplayName();
                    $this->followEvent($bot, $event, $line_id, $display_name);

                } else if ($event instanceof UnfollowEvent) { // お友達の登録したとき
                    Log::info('Unfollow Event:: 已退出 ');
                    $this->unfollowEvent($event, $line_id);

                } else if (!($event instanceof MessageEvent)) {
                    continue;
                }else{
                    $message = $event->getMessage();
                    if (!($message instanceof TextMessageContent)) {
                        // $logger->info('Non text message has come');
                        continue;
                    }
                    switch ($message->getText()) {
                        case '#Hello':
                            # code...
                            $replyText = 'Hi World 123!';
                            $message = (new TextMessage(['type' => 'text' ,'text' => $replyText]));

                            $bot->unlinkRichMenuIdFromUser($line_id);
                            $bot->replyMessage(new ReplyMessageRequest([
                                'replyToken' => $event->getReplyToken(),
                                'messages' => [$message],
                            ]));
                            break;
                        default:
                            # code...
                            break;
                    }
                    // $replyText = $message->getText();
                    // $bot->replyMessage(new ReplyMessageRequest([
                    //     'replyToken' => $event->getReplyToken(),
                    //     'messages' => [
                    //         (new TextMessage(['text' => $replyText]))->setType('text'),
                    //     ],
                    // ]));
                    // $line_user_id = $this->getLineUserId($event);
                    // Log::info('line user id: '. $line_user_id);
                    // $this->replyText($bot, $event->getReplyToken(), $replyText);
                }

            }
        } catch (InvalidSignatureException $e) {
            return abort(400, 'Bad signature');
        } catch (InvalidEventRequestException $e) {
            return abort(400, "Invalid event request");
        }
        return response('ok');

        // $res->withStatus(200, 'OK');
        // return $res;
    }

    private function followEvent($bot, FollowEvent $event, $line_id, $display_name) // お友達登になったとき
    {
        // $line_user_id = $event->getUserId();
        $driver = Driver::where('line_id', $line_id)->first();
        if($driver){
            Log::info('follow Event:: 有司機 ');
            Driver::where('line_id', '=', $line_id)
                ->update([
                    'is_active' => 0
                ]);
        }else{
            Log::info('follow Event:: 吳司機 ');
            Driver::create([
                'line_id' => $line_id,
                'nickname' => $display_name,
            ]);
        }
        $reply_token = $event->getReplyToken();
        $text = "歡迎加入!";
        Log::info('follow Event:: 已加入 '.$reply_token.' line id '. $line_id.' display name '. $display_name);
        $this->replyTextMessage($bot, $text, $reply_token);
    }
    private function unfollowEvent(UnfollowEvent $event, $line_id) // お友達登になったとき
    {
        // $reply_token = $event->getReplyToken();
        // $text = "歡迎加入!";
        Driver::where('line_id', '=', $line_id)
            ->update([
                'is_active' => 0
            ]);
        // $this->replyTextMessage($bot, $text, $reply_token);
        Log::info('unfollowEvent:: 已退出'.$line_id);
    }
    private function replyTextMessage($bot, string $text, string $reply_token) // テキストメッセージの返信
    {
        $text_message = new TextMessage(['text' => $text]);

        $bot->replyMessage(new ReplyMessageRequest([
            'replyToken' => $reply_token,
            'messages' => [
                $text_message->setType('text'),
            ],
        ]));
    }
    private function replyText($bot, string $reply_token, string $text)
    {
        $message = new TextMessage([
            'type' => 'text',
            'text' => $text,
        ]);
        $request = new ReplyMessageRequest([
            'replyToken' => $reply_token,
            'messages' => [$message],
        ]);

        $bot->replyMessage($request);
    }

    // private function getLineUserId(MessageEvent $event)
    // {
    //     $source = $event->getSource();

    //     return $source->getUserId();
    // }
}
