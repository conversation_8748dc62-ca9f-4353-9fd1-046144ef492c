<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Dispatch;
use Log;

class YatanPolicy
{
    /**
     * Create a new policy instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine whether the user can view any Yatan's.
     *
     * @param  \App\Models\User  $user
     * @return bool
     */
    public function viewAny(User $user): bool
    {
        Log::info('viewAny');
        $role = $user->role;
        return ($role == 'admin');
    }

    public function view(User $user, Dispatch $dispatch): bool
    {
        Log::info('view');
        $role = $user->role;
        return ($role == 'admin');
    }

    public function create(User $user): bool
    {
        $role = $user->role;
        return ($role == 'admin');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, User $model): bool
    {
        $role = $user->role;
        return ($role == 'admin');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, User $model): bool
    {
        $role = $user->role;
        return ($role == 'admin');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, User $model): bool
    {
        $role = $user->role;
        return ($role == 'admin');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, User $model): bool
    {
        $role = $user->role;
        return ($role == 'admin');
    }
}
