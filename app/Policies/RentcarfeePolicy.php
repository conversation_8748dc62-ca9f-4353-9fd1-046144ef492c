<?php

namespace App\Policies;

use App\Models\Rentcarfee;
use App\Models\User;

class RentcarfeePolicy
{
    /**
     * Create a new policy instance.
     */
    public function __construct()
    {
        //
    }
    public function viewAny(User $user): bool
    {
        $role = $user->role;
        return ($role == 'admin' || $role == 'vip');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Rentcarfee $reserve): bool
    {
        $role = $user->role;
        return ($role == 'admin' || $role == 'vip');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        $role = $user->role;
        return ($role == 'admin' || $role == 'vip');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Rentcarfee $reserve): bool
    {
        $role = $user->role;
        return ($role == 'admin' || $role == 'vip');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Rentcarfee $reserve): bool
    {
        $role = $user->role;
        return ($role == 'admin' || $role == 'vip');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Rentcarfee $reserve): bool
    {
        $role = $user->role;
        return ($role == 'admin' || $role == 'vip');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Rentcarfee $reserve): bool
    {
        $role = $user->role;
        return ($role == 'admin' || $role == 'vip');
    }
}
