<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Reserve extends Model
{
    use HasFactory;
    protected $fillable = [
        'reserve_id',
        'reserve_type',
        'order_id',
        'customer_name',
        'customer_mobile',
        'type',
        'flight_no',
        'no_of_passengers',
        'no_of_bags',
        'vehicle_type',
        'charter_hours',
        'reserve_date',
        'reserve_time',
        'boarding_address',
        'drop_off_address',
        'status',
        'driver_id',
        'car_license',
        'vendor_id',
    ];
}
