<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BlankDriver extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'blank_drivers';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id'; // Assuming 'id' is your primary key

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        // Add the columns of your table here that you want to be mass-assignable
        'line_id',
        'nick_name',
        'name',
        'mobile',
        'total_points',
        'car_license',
        'car_type',
        'note',
        'status',
        // ... other columns
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        // 'form_data' => 'array', // If form_data is stored as JSON
        // ... other casts
        'created_at'  => 'date:Y-m-d',
    ];
    public function blankDispatchs(): HasMany
    {
        return $this->hasMany(BlankDispatch::class, 'driver_id', 'id');
    }
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    // public $timestamps = true; // Set to false if you don't have created_at and updated_at columns
}
