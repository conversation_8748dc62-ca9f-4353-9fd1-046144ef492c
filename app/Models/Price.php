<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Price extends Model
{
    use HasFactory, SoftDeletes;
    protected $table = 'rent_car_prices';
    protected $fillable = [
        'vendor_id',
        'order_type',
        'car_type',
        'from_area_id',
        'from_area_name',
        'from_city_id',
        'from_city_name',
        'from_district_id',
        'from_district_name',
        'to_area_id',
        'to_area_name',
        'to_city_id',
        'to_city_name',
        'to_district_id',
        'to_district_name',
        'price',
        'note',
        'status',
        'deleted_at'
    ];
    protected $casts = [
        'created_at'  => 'date:Y-m-d',
    ];

}
