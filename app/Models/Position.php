<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Position extends Model
{
    use HasFactory, SoftDeletes;
    protected $table = 'positions';
    protected $fillable = [
        'user_id',
        'longitude',
        'latitude'
    ];
    protected $casts = [
        'created_at'  => 'date:Y-m-d',
    ];
}
