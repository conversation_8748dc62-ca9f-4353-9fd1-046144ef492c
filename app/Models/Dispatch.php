<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Dispatch extends Model
{
    use HasFactory;

    protected $fillable = [
        'dispatch_id',
        'customer_name',
        'customer_id',
        'customer_sex',
        'customer_mobile',
        'customer_address',
        'driver_id',
        'start_time',
        'end_time',
        'status',
        'route',
        'rental_cost',
        'vendor_id',
        'signature_file',
        'image_path',
        'flight_no',
        'regular',
        'odometer',
        'quotation_id',
        'passenger_multi'
    ];
    protected $casts = [
        'created_at'  => 'date:Y-m-d',
    ];
    public function driver()
    {
        return $this->belongsTo(Driver::class, 'driver_id', 'id');
    }
    public function car()
    {
        return $this->belongsTo(Car::class, 'driver_id', 'driver_id');
    }
    public function signatures(): HasMany // Add type hint
    {
        return $this->hasMany(DispatchSignature::class);
    }
    public function vendor()
    {
        return $this->belongsTo(\App\Models\Vendor::class, 'vendor_id', 'id');
    }
}
