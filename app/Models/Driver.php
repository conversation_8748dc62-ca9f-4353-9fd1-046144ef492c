<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Driver extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'sex',
        'mobile',
        'is_active',
        'line_id',
        'nickname',
        'vendor_id',
        'dealer_name',
        'person_id',
        'address',
    ];
    protected $casts = [
        'created_at'  => 'date:Y-m-d',
    ];
    public function car()
    {
        return $this->hasOne(Car::class, 'driver_id', 'id');
    }
    public function dispatch()
    {
        return $this->hasMany(Dispatch::class, 'driver_id', 'id');
    }
    public function vendor()
    {
        // 修正關聯: vendor_id 對應 Vendor 的 id
        return $this->belongsTo(Vendor::class, 'vendor_id', 'id');
    }
}
