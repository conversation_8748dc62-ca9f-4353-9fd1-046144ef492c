<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DispatchSignature extends Model
{
    use HasFactory;

    protected $fillable = [
        'dispatch_id',
        'file_path',
    ];

    /**
     * Get the dispatch that owns the signature.
     */
    public function dispatch(): BelongsTo // Add type hint
    {
        return $this->belongsTo(Dispatch::class);
    }
}
