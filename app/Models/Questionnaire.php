<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Questionnaire extends Model
{
    protected $fillable = [
        'line_id',
        'nickname',
        'booker_name',
        'ride_date',
        'customer_service_rating',
        'driver_service_rating',
        'vehicle_condition_rating',
        'vehicle_cleanliness_rating',
        'has_recommended_ansing',
        'likelihood_to_recommend',
        'how_heard_about_ansing',
        'booking_channel_awareness',
        'additional_feedback',
        'vendor_id'
    ];
    protected $casts = [
        'created_at'  => 'date:Y-m-d',
    ];
}
