<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'sex',
        'mobile',
        'address',
        'email',
        'uncode',
        'vandor_id',
        'status',
        'note',
    ];

    protected $casts = [
        'updated_at'  => 'date:Y-m-d',
        'created_at'  => 'date:Y-m-d',
    ];
    public function rentcatfee()
    {
        return $this->hasMany(Rentcarfee::class, 'customer_id', 'id');
    }
}
