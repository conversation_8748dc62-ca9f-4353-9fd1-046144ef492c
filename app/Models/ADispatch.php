<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ADispatch extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'a_dispatchs';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id'; // Assuming 'id' is your primary key. If it's different, change it accordingly.

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true; // Set to true if you have 'created_at' and 'updated_at' columns, otherwise false.

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'dispatch_no',
        'vendor_id',
        'status',
        'service_type',
        'customer',
        'unified',
        'passenger',
        'mobile',
        'start_date',
        'start_location',
        'end_location',
        'cost',
        'driver_id',
        'paytype',
        'note',
        'signature_file',
        'image_file',
        'outset',
        'on_the_car',
        'off_the_car',
        'signature_time',
        'adriver_id',
        'adriver_lineid',
        'adriver_name',
        'adriver_mobile',
        'adriver_car_license',
        'adriver_car_type',
        'passenger_multi',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        // Add any columns you want to hide when the model is serialized.
        // Example:
        // 'password',
        // 'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        // Add any columns that need to be cast to a specific type.
        // Example:
        'created_at'  => 'date:Y-m-d',
        'passenger_multi' => 'array',
        // 'is_active' => 'boolean',
    ];
}
