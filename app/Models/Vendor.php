<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vendor extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'contact',
        'mobile',
        'rm_id',
        'address',
        'status',
        'join_code',
    ];

    public function contract()
    {
        return $this->hasMany(Contract::class);
    }

    public function drivers()
    {
        return $this->hasMany(Driver::class, 'vendor_id', 'id');
    }
}
