<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Rentcarfee extends Model
{
    use HasFactory;

    protected $fillable = [
        'rent_car_date',
        'customer_id',
        'title',
        'car_type',
        'driver_id',
        'tour_guide_id',
        'journey',
        'rental_cost',
        'taxes',
        'total',
        'status',
    ];

    public function driver()
    {
        return $this->belongsTo(TourDriver::class, 'driver_id', 'id');
    }
    public function guide()
    {
        return $this->belongsTo(TourGuide::class, 'tour_guide_id', 'id');
    }
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'id');
    }
}
