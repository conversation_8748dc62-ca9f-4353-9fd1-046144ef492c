<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ADriver extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'a_drivers';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id'; // Assuming 'id' is your primary key. If it's different, change it accordingly.

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true; // Set to true if you have 'created_at' and 'updated_at' columns, otherwise false.

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'mobile',
        'nickname',
        'lineid',
        'car_license',
        'car_type',
        'vendor_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        // Add any columns you want to hide when the model is serialized.
        // Example:
        // 'password',
        // 'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        // Add any columns that need to be cast to a specific type.
        // Example:
        'created_at'  => 'date:Y-m-d',
        // 'email_verified_at' => 'datetime',
        // 'is_active' => 'boolean',
    ];
}
