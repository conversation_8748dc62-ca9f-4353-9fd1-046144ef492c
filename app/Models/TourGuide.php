<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TourGuide extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'sex',
        'address',
        'birthday',
        'mobile',
        'note',
        'status',
        'vandor_id',
    ];

    protected $casts = [
        'updated_at'  => 'date:Y-m-d',
        'created_at'  => 'date:Y-m-d',
    ];
    public function rentcatfee()
    {
        return $this->hasMany(Rentcarfee::class, 'tour_guide_id', 'id');
    }
}
