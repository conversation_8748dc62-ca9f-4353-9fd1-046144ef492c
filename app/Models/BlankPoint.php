<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BlankPoint extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'blank_points';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id'; // Assuming 'id' is your primary key

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        // Add the columns of your table here that you want to be mass-assignable
        'line_id',
        'name',
        'type',
        'point',
        'reason',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        // 'form_data' => 'array', // If form_data is stored as JSON
        // ... other casts
        'created_at'  => 'date:Y-m-d',
    ];
    public function blankdriver(): BelongsTo
    {
        return $this->belongsTo(BlankDriver::class, 'line_id', 'line_id');
    }
}
