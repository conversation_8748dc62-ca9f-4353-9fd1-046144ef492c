<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\View;

class SoundNotifierServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // 創建一個自定義的Blade指令，用於添加聲音通知腳本
        Blade::directive('soundNotifier', function () {
            return "<?php echo '<script src=\"" . asset('js/sound-notifier.js') . "\"></script>'; ?>";
        });

        // 在所有視圖中添加聲音通知腳本
        View::composer('*', function ($view) {
            // 檢查視圖是否已經有內容
            if ($view->getName() != '') {
                // 在視圖中添加一個變量，用於在模板中使用
                $view->with('soundNotifierScript', asset('js/sound-notifier.js'));
            }
        });
    }
}
