<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Notifications\InvoicePaid;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Notification;

class SlackController extends Controller
{
    public function form()
    {
        return view('my-form');
    }
    public function store(Request $request)
    {
        $input = $request->all();
        Log::info('save >> ', $input);
        $validator = Validator::make($input, [
            'token' => 'required|max:255',
            'title' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        if($input['token']!='zaq123'){
            return $this->sendError('Token Error.', $validator->errors());
        }
        $text = '標題: '.$input['title'];
        Notification::route('slack', config('notifications.slack.invoice_paid'))
            ->notify(new InvoicePaid($text));
            dd('success');
    }
}
