<?php

namespace App\Http\Controllers;

use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class LineNotifyController extends Controller
{
    public function index(Request $request)
    {
        // 獲取 code
        $code = $request->input('code');

        // 獲取 access_token
        $responseData = Http::asForm()->post('https://notify-bot.line.me/oauth/token', [
            'code' => $code,
            'grant_type' => 'authorization_code',
            'redirect_uri' => 'https://cars-dev.aerocars.cc/',
            'client_id' => 'TcbzQ94tzOXFxvHUdAaTFA',
            'client_secret' => '96I9MnSqyTYVh2Zr5Oshff3zKngWS8ZH1QO4QdVhbec',
        ])->json();

        $accessToken = Arr::get($responseData, 'access_token');

        // 發送 notify 訊息
        $responseData = Http::asForm()->withHeaders(
            [
                'Authorization' => "Bearer {$accessToken}"
            ]
        )->post(
            'https://notify-api.line.me/api/notify',
            [
                'message' => '你好'
            ]
        )->json();

        $status = Arr::get($responseData, 'status');

        if ($status !== 200) {
            response('連動失敗', $status);
        }

        return response('已經連動成功', 200);
    }
}
