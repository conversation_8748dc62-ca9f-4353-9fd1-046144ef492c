<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\ADispatch;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use App\Models\ADriver; // <--- 加入 ADriver 模型

class ADispatchController extends Controller
{
    public function parseAndStoreDispatch(Request $request)
    {
        Log::info('parseAndStoreDispatch (JSON) 開始執行');
        $startTime = microtime(true);

        // Get the JSON payload from the request
        $input = $request->json()->all(); // Use json() to get decoded JSON data

        // Define validation rules for the expected JSON structure
        $validator = Validator::make($input, [
            '預約日期' => 'required|date_format:Y-m-d H:i', // Validate the date and time format
            '接送類別' => 'required|string|in:送機,接機', // Ensure it's one of the expected types
            '上車地點' => 'required|string|max:255',
            '下車地點' => 'required|string|max:255',
            '乘客名稱' => 'required|string|max:255',
            '乘客電話' => 'required|string|max:20', // Adjust max length if needed
            'note'     => 'nullable|string|max:500', // Optional note field
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            Log::error('JSON 驗證錯誤: ' . json_encode($validator->errors()));
            // Return validation errors
            return $this->sendError('輸入資料驗證失敗', $validator->errors()->toArray());
        }

        Log::info('JSON 驗證通過，準備儲存資料', $input);

        // Prepare data for database insertion, mapping JSON keys to DB columns
        $dataToStore = [
            'dispatch_no'   => 'ADP_' . date("YmdHis") . Str::random(3), // Generate unique dispatch number
            'start_date'    => Carbon::parse($input['預約日期'])->format('Y-m-d H:i:s'), // Parse and format date/time
            'service_type'  => $input['接送類別'],
            'start_location'=> $input['上車地點'],
            'end_location'  => $input['下車地點'],
            'passenger'     => $input['乘客名稱'],
            'mobile'        => $input['乘客電話'],
            'status'        => '未指派', // Default status
            'paytype'       => 1,        // Default paytype (assuming 1 is a valid default)
            'note'          => $input['note'] ?? null, // Use note if provided, otherwise null
            // Add other default values if necessary for your ADispatch model
            // 'adriver_id' => null,
            // 'adriver_name' => null,
            // ... etc.
        ];

        // Store data to the database within a try-catch block
        Log::info('開始儲存資料到資料庫');
        try {
            $aDispatch = ADispatch::create($dataToStore);
            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime);
            Log::info('儲存資料到資料庫完成', ['dispatch_id' => $aDispatch->id, 'execution_time' => $executionTime]);
            // Send success response
            return $this->sendResponse($aDispatch->toArray(), '派車資料已成功儲存。');
        } catch (\Exception $e) {
            Log::error('儲存派車資料時發生錯誤: ' . $e->getMessage(), ['data' => $dataToStore]);
            // Send error response
            return $this->sendError('儲存派車資料時發生錯誤，請聯繫管理員。', ['error' => $e->getMessage()], 500);
        }

    }
    public function parseAndStoreDispatchForFilament(array $data): array
    {
        Log::info('parseAndStoreDispatchForFilament 開始執行');
        $startTime = microtime(true);

        $validator = Validator::make($data, [
            'text' => 'required|string',
        ]);

        if ($validator->fails()) {
            Log::error('驗證錯誤: ' . json_encode($validator->errors()));
            return ['success' => false, 'message' => '驗證錯誤', 'errors' => $validator->errors()];
        }

        $text = $data['text'];
        Log::info('Input Text: ' . $text); // Log the input text for debugging

         // --- 改進格式判斷邏輯 ---
        $formatType = 'unknown';
        // 優先判斷新格式的關鍵字 (使用你輸入的 "上車:", "下車:", "乘客:")
        // 使用 [：:] 匹配全形或半形冒號
        if (preg_match('/上車[：:]/u', $text) && preg_match('/下車[：:]/u', $text) && preg_match('/乘車貴賓[：:]/u', $text)) { // 檢查樣板二的關鍵字
            $formatType = 'template2';
            Log::info('Detected format: template2 (based on 上車:/下車:/乘車貴賓:)');
        // 2. 其次檢查新格式 (new)
        } elseif (preg_match('/上車[：:]/u', $text) && preg_match('/下車[：:]/u', $text) && preg_match('/乘客[：:]/u', $text)) {
            $formatType = 'new';
            Log::info('Detected format: new (based on 上車:/下車:/乘客:)');
        // 3. 最後檢查原始格式 (original)
        } elseif (preg_match('/服務類別[：:]/u', $text) && preg_match('/乘車貴賓[：:]/u', $text)) {
            $formatType = 'original';
            Log::info('Detected format: original (based on 服務類別:/乘車貴賓:)');
        } else {
            // --- 加入更詳細的失敗原因 Log ---
            $missingKeywords = [];
            if (!preg_match('/上車[：:]/u', $text)) $missingKeywords[] = '"上車:"';
            if (!preg_match('/下車[：:]/u', $text)) $missingKeywords[] = '"下車:"';
            if (!preg_match('/乘客[：:]/u', $text)) $missingKeywords[] = '"乘客:"';
            $newFormatMissing = implode(', ', $missingKeywords);

            $missingKeywords = [];
            if (!preg_match('/服務類別[：:]/u', $text)) $missingKeywords[] = '"服務類別:"';
            if (!preg_match('/乘車貴賓[：:]/u', $text)) $missingKeywords[] = '"乘車貴賓:"';
            $originalFormatMissing = implode(', ', $missingKeywords);

            Log::error("無法識別輸入格式。新格式缺少: [{$newFormatMissing}] | 原始格式缺少: [{$originalFormatMissing}]");
            return ['success' => false, 'message' => '無法識別輸入格式。請確保使用了正確的樣板關鍵字 (例如 "上車:", "下車:", "乘客:" 或 "服務類別:", "乘車貴賓:")。'];
        }

        // --- 根據格式定義正規表達式 (更新樣板二的模式) ---
        $dateTimePattern = '/預約日期[：:]\s*(\d{4}-\d{2}-\d{2})\s+(\d{1,2})[：:](\d{2})/mu';

        if ($formatType === 'original') {
            $serviceTypePattern = '/服務類別[：:]\s*(送機|接機)/mu';
            $addressPattern = '/從[：:]\s*([^\r\n]+)/mu'; // 假設原始格式也用冒號
            $destinationPattern = '/(?:接貴賓)?前往[：:]\s*([^\r\n]+)/mu'; // 假設原始格式也用冒號
            $passengerPattern = '/乘車貴賓[：:]\s*([^\r\n]+)/mu';
            $phonePattern = '/貴賓電話[：:]\s*(\d+)/mu';
            $notePattern = null;
        } elseif ($formatType === 'new') {
            // 允許樣板二中可能出現 "服務類別" 或 "聯絡電話" 但不強制提取
            $serviceTypePattern = '/服務類別[：:]\s*([^\r\n]+)/mu'; // 嘗試提取，但不強制
            $addressPattern = '/上車[：:]\s*([^\r\n]+)/mu'; // 使用 "上車:"
            $destinationPattern = '/下車[：:]\s*([^\r\n]+)/mu'; // 使用 "下車:"
            $passengerPattern = '/乘客[：:]\s*([^\r\n]+)/mu';
            $phonePattern = '/聯絡電話[：:]\s*([^\r\n]+)/mu'; // 嘗試提取 "聯絡電話"
            $notePattern = '/備註[：:]\s*([^\r\n]*)/mu'; // 保持備註提取
        } elseif ($formatType === 'template2') {
            $serviceTypePattern = '/服務類別[：:]\s*([^\r\n]+)/mu'; // 提取服務類別後的所有內容
            $addressPattern = '/上車[：:]\s*([^\r\n]+)/mu';
            $destinationPattern = '/下車[：:]\s*([^\r\n]+)/mu';
            $passengerPattern = '/乘車貴賓[：:]\s*([^\r\n]+)/mu';
            $phonePattern = '/貴賓電話[：:]\s*([^\r\n]+)/mu'; // 提取貴賓電話後的所有內容 (更寬鬆)
            $costPattern = '/金額[：:]\s*(\d+)/mu'; // 新增：提取金額數字
            $notePattern = '/備註[：:]\s*([^\r\n]*)/mu';
        }

        // --- 提取資料 ---
        preg_match($dateTimePattern, $text, $dateTimeMatches);
        $serviceTypeMatches = $serviceTypePattern ? (preg_match($serviceTypePattern, $text, $matches) ? $matches : []) : [];
        preg_match($addressPattern, $text, $addressMatches);
        preg_match($destinationPattern, $text, $destinationMatches);
        preg_match($passengerPattern, $text, $passengerMatches);
        $phoneMatches = $phonePattern ? (preg_match($phonePattern, $text, $matches) ? $matches : []) : [];
        $costMatches = $costPattern ? (preg_match($costPattern, $text, $matches) ? $matches : []) : [];
        $noteMatches = $notePattern ? (preg_match($notePattern, $text, $matches) ? $matches : []) : [];


        // --- 增加更詳細的日誌 ---
        // Log::info('日期時間匹配結果 (u): ' . json_encode($dateTimeMatches));
        // Log::info('服務類別匹配結果 (u): ' . json_encode($serviceTypeMatches));
        // Log::info('出發地匹配結果 (u): ' . json_encode($addressMatches));
        // Log::info('目的地匹配結果 (u): ' . json_encode($destinationMatches));
        // Log::info('乘客匹配結果 (u): ' . json_encode($passengerMatches));
        // Log::info('電話匹配結果 (u): ' . json_encode($phoneMatches));
        // --- 日誌結束 ---

        // --- 準備儲存資料 ---
        $dataToStore = [
            'dispatch_no' => 'ADP_' . date("YmdHis") . Str::random(3),
            'start_date' => null,
            'status' => '未指派',
            'paytype' => 1,
            'note' => null,
            // 'cost' => null,
            'service_type' => null, // 初始化
            'mobile' => null, // 初始化
        ];

        // --- 根據格式填充 $dataToStore ---
        if ($formatType === 'original') {
            $dataToStore['service_type'] = isset($serviceTypeMatches[1]) ? trim($serviceTypeMatches[1]) : null;
            $dataToStore['start_location'] = isset($addressMatches[1]) ? trim($addressMatches[1]) : null;
            $dataToStore['end_location'] = isset($destinationMatches[1]) ? trim($destinationMatches[1]) : null;
            $dataToStore['passenger'] = isset($passengerMatches[1]) ? trim($passengerMatches[1]) : null;
            $dataToStore['mobile'] = isset($phoneMatches[1]) ? trim($phoneMatches[1]) : null;
        } elseif ($formatType === 'new') {
            // 如果樣板二中提取到了服務類別，就用它，否則用預設值
            $dataToStore['service_type'] = isset($serviceTypeMatches[1]) ? trim($serviceTypeMatches[1]) : '其他';
            $dataToStore['start_location'] = isset($addressMatches[1]) ? trim($addressMatches[1]) : null;
            $dataToStore['end_location'] = isset($destinationMatches[1]) ? trim($destinationMatches[1]) : null;
            $dataToStore['passenger'] = isset($passengerMatches[1]) ? trim($passengerMatches[1]) : null;
            // 如果樣板二中提取到了聯絡電話，就用它
            $dataToStore['mobile'] = isset($phoneMatches[1]) ? trim($phoneMatches[1]) : null;
            // --- 設定 vendor_id for 樣板二 ---
            $dataToStore['vendor_id'] = 7;
            // --- 設定結束 ---
            if (isset($noteMatches[1])) {
                $extractedNote = trim($noteMatches[1]);
                if (!empty($extractedNote)) {
                    $dataToStore['note'] = $extractedNote;
                }
            }
        } elseif ($formatType === 'template2') {
            $dataToStore['service_type'] = isset($serviceTypeMatches[1]) ? trim($serviceTypeMatches[1]) : '未指定'; // 或其他預設值
            $dataToStore['start_location'] = isset($addressMatches[1]) ? trim($addressMatches[1]) : null;
            $dataToStore['end_location'] = isset($destinationMatches[1]) ? trim($destinationMatches[1]) : null;
            $dataToStore['passenger'] = isset($passengerMatches[1]) ? trim($passengerMatches[1]) : null;
            $dataToStore['mobile'] = isset($phoneMatches[1]) ? trim($phoneMatches[1]) : null;
            $dataToStore['cost'] = isset($costMatches[1]) ? trim($costMatches[1]) : null; // 新增：填充 cost
            $dataToStore['vendor_id'] = 7; // 設定 vendor_id
            if (isset($noteMatches[1])) {
                $extractedNote = trim($noteMatches[1]);
                if (!empty($extractedNote)) {
                    $dataToStore['note'] = $extractedNote;
                }
            }
        }
        // Process date and time (handle missing matches)
        // Log::info('開始處理日期和時間');
        // --- 處理日期和時間 (共用邏輯) ---
        if (!empty($dateTimeMatches) && count($dateTimeMatches) === 4) {
            try {
                $dateParts = explode('-', $dateTimeMatches[1]);
                if (count($dateParts) === 3) {
                    $year = $dateParts[0];
                    $month = $dateParts[1];
                    $day = $dateParts[2];
                    $hour = $dateTimeMatches[2];
                    $minute = $dateTimeMatches[3];

                    // 檢查日期和時間是否有效
                    if (checkdate($month, $day, $year) && $hour >= 0 && $hour <= 23 && $minute >= 0 && $minute <= 59) {
                        $startDateTime = Carbon::create($year, $month, $day, $hour, $minute, 0);
                        $dataToStore['start_date'] = $startDateTime->format('Y-m-d H:i:s');
                        Log::info('日期時間處理成功: ' . $dataToStore['start_date']);
                    } else {
                        Log::error('無效的日期或時間值: ' . json_encode($dateTimeMatches));
                        $dataToStore['start_date'] = null;
                    }
                } else {
                    Log::error('日期格式解析錯誤: ' . $dateParts[1]);
                    $dataToStore['start_date'] = null;
                }
            } catch (\Exception $e) {
                Log::error('日期時間轉換錯誤: ' . $e->getMessage());
                $dataToStore['start_date'] = null;
            }
        } else {
            Log::warning('日期時間模式不匹配或匹配結果數量不足!');
            $dataToStore['start_date'] = null;
        }
        // Log::info('處理日期和時間完成');

        // 在儲存前再次記錄準備好的資料
        // Log::info('最終準備儲存的資料 (u): ' . json_encode($dataToStore));

        // Store data to the database
        // Log::info('開始儲存資料到資料庫');
        // --- 驗證必要欄位 (根據格式調整) ---
        $missingFields = [];
        if ($formatType === 'original') {
            $requiredFields = ['start_date', 'service_type', 'start_location', 'end_location', 'passenger'];
        } elseif ($formatType === 'new') {
            // 新格式中，mobile 不是從文本提取，service_type 是預設的，cost 是必須的
            $requiredFields = ['start_date', 'start_location', 'end_location', 'passenger'];
        }  elseif ($formatType === 'template2') {
            // 根據實際需求決定哪些欄位是必須的，這裡假設金額也是必須的
            $requiredFields = ['start_date', 'start_location', 'end_location', 'passenger', 'cost'];
        } else {
            // 不應該執行到這裡，但以防萬一
            return ['success' => false, 'message' => '未知的格式類型，無法驗證欄位。'];
        }

        foreach ($requiredFields as $field) {
            if (!isset($dataToStore[$field]) || $dataToStore[$field] === null || $dataToStore[$field] === '') {
                // 特殊處理 mobile，如果新格式且未提取到，則不視為缺失
                if ($field === 'mobile' && $formatType === 'new' && !isset($phoneMatches[1])) {
                    continue;
                }
                 // 特殊處理 service_type，如果新格式且未提取到，則不視為缺失 (因為有預設值)
                 if ($field === 'service_type' && $formatType === 'new' && !isset($serviceTypeMatches[1])) {
                    continue;
                }
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            $missingFieldsString = implode('、', $missingFields);
            Log::error("必要欄位缺失 ({$formatType} format)，無法儲存: " . $missingFieldsString . ' | 資料: ' . json_encode($dataToStore));
            // 修改錯誤訊息以包含格式類型
            return ['success' => false, 'message' => "解析失敗 ({$formatType} 格式)，缺少或無法識別以下資訊：" . $missingFieldsString . "。請檢查輸入格式。"];
        }

        // --- 儲存資料 ---
        Log::info("最終準備儲存的資料 ({$formatType} format): " . json_encode($dataToStore));
        try {
            $aDispatch = ADispatch::create($dataToStore);
            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime);
            Log::info("儲存資料到資料庫完成 ({$formatType} format)，執行時間: " . $executionTime . ' 秒');
            return ['success' => true, 'message' => '派車資料已成功解析並儲存。'];
        } catch (\Illuminate\Database\QueryException $qe) {
            Log::error("資料庫儲存錯誤 ({$formatType} format): " . $qe->getMessage() . ' | SQL: ' . $qe->getSql() . ' | Bindings: ' . json_encode($qe->getBindings()), ['data' => $dataToStore]); // <-- 加入資料庫錯誤 Log
            return ['success' => false, 'message' => '儲存派車資料時發生資料庫錯誤，請檢查欄位設定或聯繫管理員。', 'error' => $qe->getMessage()];
        } catch (\Exception $e) {
            Log::error("儲存派車資料時發生一般錯誤 ({$formatType} format): " . $e->getMessage(), ['data' => $dataToStore]); // <-- 加入一般錯誤 Log
            return ['success' => false, 'message' => '儲存派車資料時發生錯誤，請聯繫管理員。', 'error' => $e->getMessage()];
        }
    }
    public function caselist(Request $request)
    {
        $startTime = microtime(true);
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required|max:255',
            'search' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $lineid = $input['line_id'];
        $searchTerm = $input['search'];
        $dispatches = collect(); // 初始化一個空的集合

        try {
            // 1. Find the driver using the provided line_id
            $driver = ADriver::where('lineid', $lineid)->first();

            // 2. If driver not found, return an empty list or an error
            if (!$driver) {
                Log::warning('caselist: Driver not found for line_id', ['line_id' => $lineid]);
                // Return an empty list, indicating no dispatches for this non-existent/unregistered driver
                return $this->sendResponse(collect(), '查無此司機的派車單資料。');
                // Alternatively, you could return an error:
                // return $this->sendError('查無此司機資料。');
            }

            // 3. Get the driver's ID
            $driverId = $driver->id;
            Log::info('caselist: Found driver', ['line_id' => $lineid, 'driver_id' => $driverId]);

            // 4. Start building the ADispatch query, filtering by the found driver's ID
            $query = ADispatch::where('adriver_id', $driverId);

            // 5. Apply date filtering based on the search term
            if ($searchTerm == 'today') {
                Log::info('caselist 查詢條件: 今天', ['driver_id' => $driverId]);
                // Query records where start_date is today
                $query->whereDate('start_date', Carbon::today());
            } elseif ($searchTerm == 'tomorrow') {
                Log::info('caselist 查詢條件: 今明兩天', ['driver_id' => $driverId]);
                // Query records where start_date is today or tomorrow
                $today = Carbon::today()->startOfDay();
                $tomorrow = Carbon::tomorrow()->endOfDay();
                $query->whereBetween('start_date', [$today, $tomorrow]);
                // Alternative using whereDate:
                // $query->where(function ($q) {
                //     $q->whereDate('start_date', Carbon::today())
                //       ->orWhereDate('start_date', Carbon::tomorrow());
                // });
            } elseif ($searchTerm == 'all') {
                 Log::info('caselist 查詢條件: 全部', ['driver_id' => $driverId]);
                // No additional date conditions needed for 'all'
            }

            // 6. Order by start date (descending) and get the results
            $dispatches = $query->orderBy('start_date', 'desc')->get();

            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime);
            Log::info('caselist 查詢完成', ['driver_id' => $driverId, 'search' => $searchTerm, 'count' => $dispatches->count(), 'execution_time' => $executionTime, 'dispatches' => $dispatches->toArray()]);

            return $this->sendResponse($dispatches, '成功取得派車單列表。');

        } catch (\Exception $e) {
            // Log the error with context
            Log::error('caselist 查詢時發生錯誤', [
                'line_id' => $lineid,
                'search' => $searchTerm,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString() // Optional: include stack trace for debugging
            ]);
            return $this->sendError('查詢派車單時發生錯誤，請稍後再試。', [], 500);
        }
    }

    public function updatestart(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'id' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $dispatch_id = $input['id'];

        try {
            $dispatch = ADispatch::where('id', $dispatch_id)->first();
            if (!$dispatch) {
                return $this->sendError('派車單不存在。');
            }
            $dispatch->outset = Carbon::now()->format('Y-m-d H:i:s');
            $dispatch->status = '已出發';
            $dispatch->save();

            return $this->sendResponse($dispatch, '派車單開始時間更新成功。');
        } catch (\Exception $e) {
            return $this->sendError('更新派車單開始時間時發生錯誤。', [], 500);
        }
    }
    public function updatecustomerup(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'id' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $dispatch_id = $input['id'];

        try {
            $dispatch = ADispatch::where('id', $dispatch_id)->first();
            if (!$dispatch) {
                return $this->sendError('派車單不存在。');
            }
            $dispatch->on_the_car = Carbon::now()->format('Y-m-d H:i:s');
            $dispatch->status = '客上';
            $dispatch->save();

            return $this->sendResponse($dispatch, '派車單開始時間更新成功。');
        } catch (\Exception $e) {
            return $this->sendError('更新派車單開始時間時發生錯誤。', [], 500);
        }
    }
    public function updatecustomerdown(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'id' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $dispatch_id = $input['id'];

        try {
            $dispatch = ADispatch::where('id', $dispatch_id)->first();
            if (!$dispatch) {
                return $this->sendError('派車單不存在。');
            }
            $dispatch->off_the_car = Carbon::now()->format('Y-m-d H:i:s');
            $dispatch->status = '已完成';
            $dispatch->save();

            return $this->sendResponse($dispatch, '派車單開始時間更新成功。');
        } catch (\Exception $e) {
            return $this->sendError('更新派車單開始時間時發生錯誤。', [], 500);
        }
    }
    public function updatesignature(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'id' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $dispatch_id = $input['id'];
        $dispatch = ADispatch::where('id', $dispatch_id)->first();
        $signatureData = $input['imgBase64'];
        $signatureData = str_replace('data:image/png;base64,', '', $signatureData);
        $signatureData = str_replace(' ', '+', $signatureData);
        $signature = base64_decode($signatureData);
        // 簽名檔名
        $fileName = 'st_' . $dispatch->dispatch_no . '.png';
        Storage::put('public/signatures/' . $fileName, $signature);
        $signature_path = storage_path('app/public/signatures/' . $fileName);

        try {
            if (!$dispatch) {
                return $this->sendError('派車單不存在。');
            }
            $dispatch->signature_time = Carbon::now()->format('Y-m-d H:i:s');
            $dispatch->status = '簽名';
            $dispatch->signature_file = $signature_path;
            $dispatch->save();

            return $this->sendResponse($dispatch, '派車單開始時間更新成功。');
        } catch (\Exception $e) {
            return $this->sendError('更新派車單開始時間時發生錯誤。', [], 500);
        }
    }
    public function getcarorder(Request $request)
    {
        $input = $request->all();
        Log::info('getcarorder: ', $input);

        // 修改驗證規則
        $validator = Validator::make($input, [
            'oid' => 'required|max:255',
            'lineid' => 'required|max:255',
            'token' => 'required|max:255',
        ]);

        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }

        // Use validated data
        $validatedData = $validator->validated();
        $order_id = $validatedData['oid'];
        $line_id = $validatedData['lineid'];

        try {
            // 1. 先檢查司機是否存在
            $driver = ADriver::where('lineid', $line_id)->first();

            if (!$driver) {
                Log::warning('getcarorder: Driver not found', ['lineid' => $line_id]);
                return $this->sendError('找不到此司機資料。');
            }

            // 2. 查詢訂單
            $dispatch = ADispatch::where('id', $order_id)->first();

            if (!$dispatch) {
                Log::warning('getcarorder: Order not found', ['order_id' => $order_id]);
                return $this->sendError('派車單不存在。');
            }

            // 3. 檢查訂單狀態
            if($dispatch->status != '未指派'){
                Log::warning('getcarorder: Order already assigned', [
                    'order_id' => $order_id,
                    'status' => $dispatch->status
                ]);
                return $this->sendError('派車單已經指派司機!');
            }

            // Add driver details to the response data using the requested keys
            $dispatch['adriver_id'] = $driver->id;
            $dispatch['adriver_name'] = $driver->name;
            $dispatch['adriver_mobile'] = $driver->mobile;
            // Assuming car_license and car_type are direct attributes of ADriver model
            $dispatch['adriver_car_license'] = $driver->car_license;
            $dispatch['adriver_car_type'] = $driver->car_type;
            // --- END: 將司機資訊加入回傳資料 ---
            Log::info('getcarorder: Success', [
                'order_id' => $order_id,
                'driver_id' => $driver->id,
                'status' => $dispatch->status
            ]);

            return $this->sendResponse($dispatch, '成功取得派車單資料。');

        } catch (\Exception $e) {
            Log::error('getcarorder: Error occurred', [
                'error' => $e->getMessage(),
                'order_id' => $order_id,
                'lineid' => $line_id
            ]);
            return $this->sendError('查詢派車單時發生錯誤。', [], 500);
        }
    }
    public function confirmorder(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            // 'vid' => 'required|max:255',
            'oid' => 'required|max:255',
            'driver_name' => 'required|max:255',
            'driver_phone' => 'required|max:255', // 確保電話號碼格式驗證 (可選，但建議)
            'car_no' => 'required|max:255',
            'car_type' => 'required|max:255',
            'token' => 'required|max:255',
            'lineid' => 'required|max:255'
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // $vendor_id = $input['vid']; // 注意：此變數目前未使用，但保留
        $order_id = $input['oid'];
        $driver_phone = $input['driver_phone'];
        $driver_name = $input['driver_name'];
        $lineid = $input['lineid'];

        try {
            $dispatch = ADispatch::where('id', $order_id)->first();
            if (!$dispatch) {
                return $this->sendError('派車單不存在。');
            }
            $driver = ADriver::where('lineid', $lineid)->first();
            if (!$driver) {
                return $this->sendError('司機不存在。');
            }

            // --- 新增/更新司機邏輯 ---
            // $aDriver = ADriver::firstOrCreate(
            //     ['mobile' => $lineid], // 使用手機號碼查找
            //     ['name' => $driver_name, 'mobile' => $driver_phone, 'lineid' => $lineid],      // 如果找不到，則用這些資料建立新司機
            // );
            // // 如果司機已存在但姓名不同，可以選擇更新姓名 (可選)
            // if ($aDriver->name !== $driver_name) {
            //     //  $aDriver->lineid = $lineid;
            //      $aDriver->name = $driver_name;
            //      $aDriver->save();
            //      Log::info('更新現有司機姓名', ['driver_id' => $aDriver->id, 'new_name' => $driver_name]);
            // }
            // --- 司機邏輯結束 ---


            // 更新 ADispatch 的司機和車輛資訊
            $dispatch->adriver_id = $driver->id; // <--- 更新 adriver_id
            $dispatch->adriver_lineid = $driver->lineid; // <--- 更新 adriver_id
            $dispatch->adriver_name = $driver->name; // 仍然保留這些欄位，方便快速顯示
            $dispatch->adriver_mobile = $driver->mobile;
            $dispatch->adriver_car_license = $input['car_no'];
            $dispatch->adriver_car_type = $input['car_type'];
            $dispatch->status = '已指派'; // 更新狀態為已指派

            // 儲存更新
            $dispatch->save();

            // 記錄日誌
            Log::info('派車單已確認並指派', [
                'dispatch_id' => $order_id,
                'adriver_id' => $driver->id, // <--- 加入 adriver_id 到日誌
                'driver_name' => $driver_name,
                'driver_phone' => $driver_phone,
                'car_no' => $input['car_no'],
                'car_type' => $input['car_type'],
                'lineid' => $input['lineid']
            ]);

            return $this->sendResponse($dispatch, '派車單已成功指派給司機。');
        } catch (\Exception $e) {
            Log::error('確認派車單時發生錯誤', ['error' => $e->getMessage()]);
            return $this->sendError('確認派車單時發生錯誤。', [], 500);
        }
    }

    /**
     * 註冊新的 A_Driver
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function registerDriver(Request $request)
    {
        $startTime = microtime(true);
        Log::info('registerDriver API 開始執行');

        $input = $request->all();

        // 1. 驗證輸入資料
        $validator = Validator::make($input, [
            // 假設 a_drivers table 的 line id 欄位名稱是 'lineid' (根據 caselist 推斷)
            // 如果欄位名稱是 'line_id', 請將 'lineid' 改為 'line_id'
            'lineid' => 'required|string|max:255|unique:a_drivers,lineid',
            'name' => 'required|string|max:255',
            'mobile' => 'required|string|max:20|unique:a_drivers,mobile', // 假設手機也需要唯一
            'car_license' => 'required|string|max:20|unique:a_drivers,car_license', // 假設車牌也需要唯一
            'car_type' => 'required|string|max:50',
            // 可以根據需要加入其他欄位的驗證規則
        ], [
            // 自訂錯誤訊息 (可選)
            'lineid.required' => 'Line ID 為必填欄位。',
            'lineid.unique' => '此 Line ID 已被註冊。',
            'name.required' => '司機姓名為必填欄位。',
            'mobile.required' => '手機號碼為必填欄位。',
            'mobile.unique' => '此手機號碼已被註冊。',
            'car_license.required' => '車牌號碼為必填欄位。',
            'car_license.unique' => '此車牌號碼已被註冊。',
            'car_type.required' => '車型為必填欄位。',
        ]);

        if ($validator->fails()) {
            Log::error('registerDriver 驗證失敗', $validator->errors()->toArray());
            // 回傳驗證錯誤，使用 toArray() 取得陣列格式的錯誤訊息
            return $this->sendError('輸入資料驗證失敗', $validator->errors()->toArray());
        }

        Log::info('registerDriver 驗證通過，準備建立司機資料', $input);

        // 2. 準備要儲存的資料
        $driverData = [
            'lineid'      => $input['lineid'],
            'name'        => $input['name'],
            'mobile'      => $input['mobile'],
            'car_license' => $input['car_license'],
            'car_type'    => $input['car_type'],
            // 可以設定其他預設值，例如狀態
            // 'status'      => 'active',
        ];

        // 3. 建立司機記錄
        try {
            $driver = ADriver::create($driverData);

            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime);
            Log::info('registerDriver 司機註冊成功', [
                'driver_id' => $driver->id,
                'lineid' => $driver->lineid,
                'execution_time' => $executionTime
            ]);

            // 回傳成功訊息及建立的司機資料
            return $this->sendResponse($driver->toArray(), '司機註冊成功。');

        } catch (\Illuminate\Database\QueryException $qe) {
            // 資料庫相關錯誤
            Log::error('registerDriver 資料庫儲存錯誤', [
                'input' => $input,
                'error' => $qe->getMessage(),
                'sql' => $qe->getSql(),
                'bindings' => $qe->getBindings()
            ]);
            return $this->sendError('註冊司機時發生資料庫錯誤，請檢查欄位或聯繫管理員。', [], 500);
        } catch (\Exception $e) {
            // 其他一般錯誤
            Log::error('registerDriver 註冊時發生錯誤', [
                'input' => $input,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString() // 可選，用於詳細偵錯
            ]);
            return $this->sendError('註冊司機時發生未知錯誤，請稍後再試。', [], 500);
        }
    }
    /**
     * 更新 A_Driver 資料
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateDriver(Request $request)
    {
        $startTime = microtime(true);
        Log::info('updateDriver API 開始執行');

        $input = $request->all();
        Log::info('updateDriver 收到請求資料', $input);

        // 1. 先驗證 lineid 是否存在
        $lineIdValidator = Validator::make($input, [
            'lineid' => 'required|string|max:255|exists:a_drivers,lineid',
        ], [
            'lineid.required' => 'Line ID 為必填欄位。',
            'lineid.exists' => '找不到此 Line ID 對應的司機。',
        ]);

        if ($lineIdValidator->fails()) {
            Log::error('updateDriver Line ID 驗證失敗', $lineIdValidator->errors()->toArray());
            return $this->sendError('輸入資料驗證失敗', $lineIdValidator->errors()->toArray());
        }

        // 2. 查找司機
        try {
            $driver = ADriver::where('lineid', $input['lineid'])->firstOrFail();
            Log::info('updateDriver 找到司機', ['driver_id' => $driver->id, 'lineid' => $input['lineid']]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            // 雖然 exists 驗證理論上會擋掉，但多一層防護
            Log::error('updateDriver 查找司機失敗 (ModelNotFound)', ['lineid' => $input['lineid']]);
            return $this->sendError('找不到指定的司機。');
        } catch (\Exception $e) {
            Log::error('updateDriver 查找司機時發生錯誤', ['lineid' => $input['lineid'], 'error' => $e->getMessage()]);
            return $this->sendError('查找司機時發生錯誤。', [], 500);
        }

        // 3. 驗證要更新的欄位
        //    使用 'sometimes' 確保只有在請求中包含該欄位時才進行驗證
        //    使用 Rule::unique()->ignore() 來處理唯一性驗證，排除當前司機
        $updateValidator = Validator::make($input, [
            'name' => 'sometimes|required|string|max:255',
            'mobile' => [
                'sometimes',
                'required',
                'string',
                'max:20',
                Rule::unique('a_drivers', 'mobile')->ignore($driver->id), // 忽略當前司機的 mobile
            ],
            'car_license' => [
                'sometimes',
                'required',
                'string',
                'max:20',
                Rule::unique('a_drivers', 'car_license')->ignore($driver->id), // 忽略當前司機的 car_license
            ],
            'car_type' => 'sometimes|required|string|max:50',
        ], [
            // 自訂錯誤訊息
            'name.required' => '司機姓名不能為空。',
            'mobile.required' => '手機號碼不能為空。',
            'mobile.unique' => '此手機號碼已被其他司機使用。',
            'car_license.required' => '車牌號碼不能為空。',
            'car_license.unique' => '此車牌號碼已被其他司機使用。',
            'car_type.required' => '車型不能為空。',
        ]);

        if ($updateValidator->fails()) {
            Log::error('updateDriver 更新欄位驗證失敗', $updateValidator->errors()->toArray());
            return $this->sendError('輸入資料驗證失敗', $updateValidator->errors()->toArray());
        }

        // 獲取通過驗證的、需要更新的資料
        $validatedUpdates = $updateValidator->validated();
        Log::info('updateDriver 更新欄位驗證通過', $validatedUpdates);

        if (empty($validatedUpdates)) {
             Log::info('updateDriver 沒有提供任何需要更新的欄位');
             return $this->sendResponse($driver->toArray(), '沒有提供需要更新的欄位，未做任何變更。');
        }

        // 4. 更新司機資料
        try {
            // 使用 validatedUpdates 更新 driver 模型
            // $driver->fill($validatedUpdates); // fill 方法會更新 $fillable 允許的欄位

            // 或者手動更新，更明確
            if (isset($validatedUpdates['name'])) {
                $driver->name = $validatedUpdates['name'];
            }
            if (isset($validatedUpdates['mobile'])) {
                $driver->mobile = $validatedUpdates['mobile'];
            }
            if (isset($validatedUpdates['car_license'])) {
                $driver->car_license = $validatedUpdates['car_license'];
            }
            if (isset($validatedUpdates['car_type'])) {
                $driver->car_type = $validatedUpdates['car_type'];
            }

            $driver->save(); // 儲存變更

            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime);
            Log::info('updateDriver 司機資料更新成功', [
                'driver_id' => $driver->id,
                'updated_fields' => array_keys($validatedUpdates),
                'execution_time' => $executionTime
            ]);

            // 回傳成功訊息及更新後的司機資料
            return $this->sendResponse($driver->toArray(), '司機資料更新成功。');

        } catch (\Illuminate\Database\QueryException $qe) {
            // 資料庫相關錯誤
            Log::error('updateDriver 資料庫更新錯誤', [
                'driver_id' => $driver->id,
                'updates' => $validatedUpdates,
                'error' => $qe->getMessage(),
                'sql' => $qe->getSql(),
                'bindings' => $qe->getBindings()
            ]);
            return $this->sendError('更新司機資料時發生資料庫錯誤。', [], 500);
        } catch (\Exception $e) {
            // 其他一般錯誤
            Log::error('updateDriver 更新時發生錯誤', [
                'driver_id' => $driver->id,
                'updates' => $validatedUpdates,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->sendError('更新司機資料時發生未知錯誤。', [], 500);
        }
    }
    public function getdriverprofile(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'lineid' => 'required|max:255',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $lineid = $input['lineid'];

        try {
            // 1. 查找司機
            $driver = ADriver::where('lineid', $lineid)->first();

            if (!$driver) {
                return $this->sendError(['exists' => false], '找不到此司機資料。');
            }
            $responseData = $driver->toArray();
            $responseData['exists'] = true; // <--- 新增：加入 exists: true
            return $this->sendResponse($responseData, '司機資料已成功查詢。');

        } catch (\Exception $e) {
            Log::error('deleteDriver 查詢時發生錯誤', ['error' => $e->getMessage()]);
            return $this->sendError('查詢司機資料時發生錯誤。', [], 500);
        }
    }
    public function checkadriver(Request $request)
    {
        $startTime = microtime(true); // Optional: Add timing
        Log::info('checkadriver API 開始執行');

        $input = $request->all();
        $validator = Validator::make($input, [
            'lineid' => 'required|string|max:255', // Use string validation
        ]);

        if ($validator->fails()) {
            Log::error('checkadriver 驗證失敗', $validator->errors()->toArray());
            return $this->sendError('輸入資料驗證失敗', $validator->errors()->toArray());
        }

        // Use validated data
        $validatedData = $validator->validated();
        $lineid = $validatedData['lineid'];

        try {
            // Query using the ADriver model, check for existence
            $exists = ADriver::where('lineid', $lineid)->exists();

            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime);

            if ($exists) {
                // Driver found
                Log::info('checkadriver: 司機存在', ['lineid' => $lineid, 'execution_time' => $executionTime]);
                // Return success response confirming existence
                return $this->sendResponse(['exists' => true], '司機資料存在。');
            } else {
                // Driver not found
                Log::info('checkadriver: 司機不存在', ['lineid' => $lineid, 'execution_time' => $executionTime]);
                // Return success response indicating non-existence (or error as before)
                // Using success response for "not found" might be better for a simple check
                 return $this->sendResponse(['exists' => false], '找不到此司機資料。');
                // Or keep using sendError if that's the preferred pattern for "not found"
                // return $this->sendError('找不到此司機資料。', ['lineid' => $lineid]);
            }

        } catch (\Exception $e) {
            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime);
            Log::error('checkadriver: 查詢時發生錯誤', [
                'lineid' => $lineid,
                'error' => $e->getMessage(),
                'execution_time' => $executionTime
                // 'trace' => $e->getTraceAsString() // Optional for detailed debugging
            ]);
            // Return generic server error
            return $this->sendError('查詢司機資料時發生錯誤。');
        }

    }
}
