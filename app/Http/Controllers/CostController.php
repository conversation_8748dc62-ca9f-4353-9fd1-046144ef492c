<?php

namespace App\Http\Controllers;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;   // <--- 引入 Log
use Illuminate\Support\Facades\Http; // <--- 引入 HTTP Client
use Illuminate\Support\Facades\Validator; // <--- 引入 Validator

class CostController extends Controller // <--- 確保繼承了基礎 Controller 以使用 sendResponse/sendError
{
    /**
     * 估算兩地之間的行車距離和時間
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function costestimate(Request $request)
    {
        // --- 1. 取得 API 金鑰 ---
        $apiKey = config('services.google_maps.api_key');

        if (!$apiKey) {
            Log::error('Google Maps API key not configured in config/services.php or .env');
            return $this->sendError('伺服器設定錯誤，缺少 Google Maps API 金鑰。', [], 500);
        }
        $recaptchaToken = $request->input('g-recaptcha-response');
        $secretKey = config('services.google_recaptcha.secret_key'); // 假設你在 config/services.php 設定了

        if (!$recaptchaToken) {
            // 可以回傳特定的錯誤碼給前端
            return $this->sendError('缺少 reCAPTCHA 驗證。', ['error_code' => 'RECAPTCHA_MISSING'], 400);
        }

        if (!$secretKey) {
             Log::error('Google reCAPTCHA Secret Key not configured.');
             return $this->sendError('伺服器 reCAPTCHA 設定錯誤。', [], 500);
        }

        try {
            $verifyResponse = Http::asForm()->post('https://www.google.com/recaptcha/api/siteverify', [
                'secret'   => $secretKey,
                'response' => $recaptchaToken,
                'remoteip' => $request->ip(),
            ]);

            $verifyData = $verifyResponse->json();

            if (!$verifyResponse->successful() || !isset($verifyData['success']) || $verifyData['success'] !== true) {
                Log::warning('reCAPTCHA verification failed.', [
                    'response_body' => $verifyData,
                    'client_ip' => $request->ip()
                ]);
                // 回傳特定的錯誤碼和訊息
                $errorMessage = 'reCAPTCHA 驗證失敗。';
                if (isset($verifyData['error-codes'])) {
                     // 可以根據 error-codes 提供更詳細的訊息，但通常不需要暴露給用戶
                     Log::warning('reCAPTCHA error codes: ' . implode(', ', $verifyData['error-codes']));
                }
                return $this->sendError($errorMessage, ['error_code' => 'RECAPTCHA_FAILED'], 400); // Bad Request
            }

            // --- reCAPTCHA 驗證成功，繼續執行後面的邏輯 ---
            Log::info('reCAPTCHA verification successful.');

        } catch (\Exception $e) {
             Log::error('Error during reCAPTCHA verification request.', ['exception' => $e->getMessage()]);
             return $this->sendError('reCAPTCHA 驗證時發生錯誤。', [], 500);
        }
        // --- 2. 取得並驗證地址 (從 $request 讀取) ---
        $input = $request->all(); // 取得所有輸入資料 (包含 JSON)

        $validator = Validator::make($input, [
            '上車地點' => 'required|string|max:255', // 驗證 "上車地點"
            '下車地點' => 'required|string|max:255', // 驗證 "下車地點"
        ]);

        if ($validator->fails()) {
            // 回傳更具體的中文錯誤訊息
            return $this->sendError('輸入參數錯誤，請檢查上車地點和下車地點。', $validator->errors()->toArray());
        }

        // *** 新增：從 validator 獲取驗證通過的資料 ***
        $validatedData = $validator->validated();

        // 從驗證過的資料中取得地址
        $origin = $validatedData['上車地點'];
        $destination = $validatedData['下車地點'];
        // *** 修改結束 ***

        // --- 移除暫時寫死的地址 ---
        // $origin = "台北市信義區市府路1號";
        // $destination = "桃園市大園區航站南路9號";

        Log::info('Cost estimate request', ['origin' => $origin, 'destination' => $destination]);

        // --- 3. 呼叫 Google Directions API ---
        $apiUrl = "https://maps.googleapis.com/maps/api/directions/json";

        try {
            $response = Http::get($apiUrl, [
                'origin' => $origin,         // 使用從參數取得的 $origin
                'destination' => $destination, // 使用從參數取得的 $destination
                'key' => $apiKey,
                'language' => 'zh-TW',
                'mode' => 'driving',
            ]);

            // 檢查 HTTP 請求是否成功
            if (!$response->successful()) {
                Log::error('Google Maps API request failed', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return $this->sendError('無法連接 Google Maps 服務', ['api_status' => $response->status()], 503);
            }

            $data = $response->json();

            // 檢查 Google API 回應狀態
            if ($data['status'] !== 'OK') {
                Log::warning('Google Maps API returned status: ' . $data['status'], [
                    'origin' => $origin,
                    'destination' => $destination,
                    'error_message' => $data['error_message'] ?? 'No error message provided'
                ]);
                if ($data['status'] === 'ZERO_RESULTS') {
                    return $this->sendError('在指定的地址之間找不到路線。');
                } elseif ($data['status'] === 'NOT_FOUND') {
                     return $this->sendError('無法解析提供的地址之一或兩者。');
                } elseif ($data['status'] === 'REQUEST_DENIED') {
                     return $this->sendError('Google Maps API 請求被拒絕，請檢查 API 金鑰或設定。');
                }
                return $this->sendError('Google Maps API 錯誤: ' . $data['status']);
            }

            // --- 4. 解析回應並提取距離 ---
            if (isset($data['routes'][0]['legs'][0]['distance']['value'])) {
                $distanceInMeters = $data['routes'][0]['legs'][0]['distance']['value'];
                $distanceInKm = round($distanceInMeters / 1000, 2);
                $durationText = $data['routes'][0]['legs'][0]['duration']['text'] ?? '無法取得';
                // 假設每公里 30 元 (根據原代碼)
                $rawCost = $distanceInKm * 30;
                $roundedCost = (int) (ceil($rawCost / 50) * 50);
                // --- 修改結束 ---
                $resultData = [
                    'origin' => $origin,
                    'destination' => $destination,
                    'distance_meters' => $distanceInMeters,
                    'distance_km' => $distanceInKm,
                    'distance_text' => $data['routes'][0]['legs'][0]['distance']['text'] ?? '無法取得',
                    'duration_text' => $durationText,
                    'cost_text' => $roundedCost, // 假設每公里 10 元
                ];

                Log::info('Distance calculated successfully', $resultData);
                return $this->sendResponse($resultData, '成功計算距離。');

            } else {
                Log::error('無法從 Google Maps API 回應中解析距離', ['response_data' => $data]);
                return $this->sendError('無法從 Google Maps API 回應中解析距離。');
            }

        } catch (\Illuminate\Http\Client\ConnectionException $e) {
            Log::error('無法連接到 Google Maps API', ['exception' => $e->getMessage()]);
            return $this->sendError('網路連線錯誤，無法連接到地圖服務。', [], 504);
        } catch (\Exception $e) {
            Log::error('呼叫 Google Maps API 時發生未知錯誤', ['exception' => $e->getMessage()]);
            return $this->sendError('計算距離時發生未知錯誤。', [], 500);
        }
    }
     /**
     * 估算多點之間的行車距離、時間和費用
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function costEstimateMultiPoint(Request $request)
    {
        // --- 2. 取得並驗證地點陣列 ---
        $input = $request->all();
        $validator = Validator::make($input, [
            'locations' => 'required|array|min:2',
            'locations.*' => 'required|string|max:255',
        ], [
            'locations.required' => '缺少地點陣列 (locations)。',
            'locations.array' => '地點 (locations) 必須是陣列格式。',
            'locations.min' => '地點陣列 (locations) 至少需要包含起點和終點 (2個地點)。',
            'locations.*.required' => '地點陣列中的地址不能為空。',
            'locations.*.string' => '地點陣列中的地址必須是文字。',
        ]);

        if ($validator->fails()) {
            return $this->sendError('輸入參數錯誤，請檢查地點陣列。', $validator->errors()->toArray());
        }

        $validatedData = $validator->validated();
        // --- 1. API Key & reCAPTCHA (No changes needed here) ---
        $apiKey = config('services.google_maps.api_key');
        if (!$apiKey) {
            Log::error('Google Maps API key not configured');
            return $this->sendError('伺服器設定錯誤，缺少 Google Maps API 金鑰。', [], 500);
        }
        // ... (reCAPTCHA validation code remains the same) ...
        $recaptchaToken = $request->input('g-recaptcha-response');
        if($input['site'] == 'yatantaxi888'){
            $secretKey = config('services.google_recaptcha_yatan.secret_key');
        }else{
            $secretKey = config('services.google_recaptcha.secret_key');
        }

        if (!$recaptchaToken) {
            return $this->sendError('缺少 reCAPTCHA 驗證。', ['error_code' => 'RECAPTCHA_MISSING'], 400);
        }
        if (!$secretKey) {
            Log::error('Google reCAPTCHA Secret Key not configured.');
            return $this->sendError('伺服器 reCAPTCHA 設定錯誤。', [], 500);
        }
        try {
            $verifyResponse = Http::asForm()->post('https://www.google.com/recaptcha/api/siteverify', [
                'secret'   => $secretKey,
                'response' => $recaptchaToken,
                'remoteip' => $request->ip(),
            ]);
            $verifyData = $verifyResponse->json();
            if (!$verifyResponse->successful() || !isset($verifyData['success']) || $verifyData['success'] !== true) {
                Log::warning('reCAPTCHA verification failed.', [
                    'response_body' => $verifyData, 'client_ip' => $request->ip()
                ]);
                $errorMessage = 'reCAPTCHA 驗證失敗。';
                if (isset($verifyData['error-codes'])) {
                    Log::warning('reCAPTCHA error codes: ' . implode(', ', $verifyData['error-codes']));
                }
                return $this->sendError($errorMessage, ['error_code' => 'RECAPTCHA_FAILED'], 400);
            }
            Log::info('reCAPTCHA verification successful.');
        } catch (Exception $e) {
            Log::error('Error during reCAPTCHA verification request.', ['exception' => $e->getMessage()]);
            return $this->sendError('reCAPTCHA 驗證時發生錯誤。', [], 500);
        }
        // --- End reCAPTCHA Validation ---



        // *** FIX: Use the original validated array directly ***
        $allLocations = $validatedData['locations'];
        $locationsCount = count($allLocations);

        // Log the locations being processed
        Log::info('Cost estimate request (multi-point)', ['locations' => $allLocations]);

        // --- 3. 呼叫 Google Directions API (Loop through legs) ---
        $apiUrl = "https://maps.googleapis.com/maps/api/directions/json";
        $totalDistanceMeters = 0;
        $totalDurationSeconds = 0;
        $legs = [];

        try {
            // *** FIX: Adjust loop to iterate through pairs of locations ***
            if ($locationsCount >= 2) {
                for ($i = 0; $i < $locationsCount - 1; $i++) { // Loop from 0 to second-to-last index
                    $currentOrigin = $allLocations[$i];
                    $currentDestination = $allLocations[$i + 1]; // The next point is the destination for this leg

                    Log::debug("Processing leg: Origin='{$currentOrigin}', Destination='{$currentDestination}'"); // Add debug log

                    $response = Http::get($apiUrl, [
                        'origin' => $currentOrigin,
                        'destination' => $currentDestination,
                        'key' => $apiKey,
                        'language' => 'zh-TW',
                        'mode' => 'driving',
                    ]);

                    // Check HTTP request success
                    if (!$response->successful()) {
                        Log::error('Google Maps API request failed (multi-point leg)', [
                            'status' => $response->status(),
                            'body' => $response->body(), // Log the body for 400 errors
                            'origin' => $currentOrigin,
                            'destination' => $currentDestination
                        ]);
                        // Provide a more specific error if possible
                        $errorMsg = '無法連接 Google Maps 服務';
                        if ($response->status() == 400) {
                            $errorMsg = 'Google Maps API 請求錯誤 (可能是地址無效或格式錯誤)';
                        }
                        return $this->sendError($errorMsg, ['api_status' => $response->status()], $response->status() >= 500 ? 503 : 400);
                    }

                    $data = $response->json();

                    // Check Google API status within the response
                    if ($data['status'] !== 'OK') {
                        Log::warning('Google Maps API returned status: ' . $data['status'] . ' (multi-point leg)', [
                            'origin' => $currentOrigin,
                            'destination' => $currentDestination,
                            'error_message' => $data['error_message'] ?? 'No error message provided'
                        ]);
                        // Use the helper function for specific Google errors
                        return $this->handleGoogleApiError($data['status']);
                    }

                    // Extract data for the current leg
                    if (isset($data['routes'][0]['legs'][0]['distance']['value'])) {
                        $legDistanceMeters = $data['routes'][0]['legs'][0]['distance']['value'];
                        $legDurationSeconds = $data['routes'][0]['legs'][0]['duration']['value'];

                        $totalDistanceMeters += $legDistanceMeters;
                        $totalDurationSeconds += $legDurationSeconds;

                        $legs[] = [
                            'origin' => $data['routes'][0]['legs'][0]['start_address'],
                            'destination' => $data['routes'][0]['legs'][0]['end_address'],
                            'distance_km' => round($legDistanceMeters / 1000, 2),
                            'distance_text' => $data['routes'][0]['legs'][0]['distance']['text'] ?? '無法取得',
                            'duration_seconds' => $legDurationSeconds,
                            'duration_text' => $data['routes'][0]['legs'][0]['duration']['text'] ?? '無法取得',
                        ];
                    } else {
                        Log::error('無法從 Google Maps API 回應中解析路段距離', [
                            'response_data' => $data,
                            'origin' => $currentOrigin,
                            'destination' => $currentDestination
                        ]);
                        return $this->sendError('無法從 Google Maps API 回應中解析路段距離。');
                    }
                    // No need to update $currentOrigin manually, the loop does it
                }
            } else {
                // This case should technically be caught by validation, but good to have
                Log::error('Less than 2 locations provided for multi-point calculation after validation.');
                return $this->sendError('需要至少兩個地點才能計算路徑。');
            }


            // --- 4. Calculate Totals and Cost (No changes needed here) ---
            $totalDistanceKm = round($totalDistanceMeters / 1000, 2);
            $totalDurationText = $this->formatDuration($totalDurationSeconds);

           // Define cost parameters
           $baseDistanceKm = 20.0;
           $baseCost = 600;
           $extraKmCostStandard = 25; // Standard car (5-seater) rate
           $extraKmCostVan = 30;      // Van (9-seater) rate
           $roundUpUnit = 100;
           $waypointSurchargePerStop = 100; // Cost per intermediate stop

           $calculatedCostStandardBase = 0; // Base cost before surcharge (Standard)
           $calculatedCostVanBase = 0;      // Base cost before surcharge (Van)
           $costMessage = null;             // Shared base message

            // Calculate base cost based on distance for BOTH vehicle types
            if ($totalDistanceKm <= $baseDistanceKm) {
                $calculatedCostStandardBase = $baseCost;
                $calculatedCostVanBase = $baseCost; // Same base cost
                $costMessage = "基本起跳{$baseDistanceKm}公里";
            } else {
                $extraDistance = $totalDistanceKm - $baseDistanceKm;

                // Standard car base cost
                $rawTotalCostStandard = $baseCost + ($extraDistance * $extraKmCostStandard);
                $calculatedCostStandardBase = (int) (ceil($rawTotalCostStandard / $roundUpUnit) * $roundUpUnit);

                // Van base cost
                $rawTotalCostVan = $baseCost + ($extraDistance * $extraKmCostVan);
                $calculatedCostVanBase = (int) (ceil($rawTotalCostVan / $roundUpUnit) * $roundUpUnit);

                // You can refine the message if needed, e.g., mention the standard rate
                $costMessage = "超過{$baseDistanceKm}公里 (標準車每公里加收{$extraKmCostStandard}元, Van每公里加收{$extraKmCostVan}元)";
            }
            // --- 5. Calculate Waypoint Surcharge ---
            $waypointSurcharge = 0;
            $surchargeMessagePart = null; // Specific message part for surcharge

            if ($locationsCount >= 3) {
                $numberOfWaypoints = $locationsCount - 2;
                $waypointSurcharge = $numberOfWaypoints * $waypointSurchargePerStop;
                $surchargeMessagePart = "{$numberOfWaypoints}個停靠點加收 {$waypointSurcharge}元";

                Log::info("Waypoint surcharge calculated", [
                    'locations_count' => $locationsCount,
                    'waypoints' => $numberOfWaypoints,
                    'surcharge_per_stop' => $waypointSurchargePerStop,
                    'total_surcharge' => $waypointSurcharge,
                ]);
            }
            // --- 6. Calculate Final Costs and Final Message ---
            $finalCostStandard = $calculatedCostStandardBase + $waypointSurcharge;
            $finalCostVan = $calculatedCostVanBase + $waypointSurcharge;

            // Construct the final cost message
            if (!is_null($surchargeMessagePart)) {
                if (is_null($costMessage)) {
                    $costMessage = $surchargeMessagePart;
                } else {
                    $costMessage .= " + " . $surchargeMessagePart;
                }
            }

            // --- 7. Prepare Result Data ---
            $resultData = [
                'legs' => $legs,
                'total_distance_meters' => $totalDistanceMeters,
                'total_distance_km' => $totalDistanceKm,
                'total_duration_seconds' => $totalDurationSeconds,
                'total_duration_text' => $totalDurationText,
                'estimated_cost_standard' => $finalCostStandard, // Cost for standard car (5-seater)
                'estimated_cost_van' => $finalCostVan,         // Cost for van (9-seater)
                'cost_message' => $costMessage,                // Combined cost message
            ];

            Log::info('Multi-point distance calculated successfully', $resultData);
            return $this->sendResponse($resultData, '成功計算多點距離與費用。');

        } catch (\Illuminate\Http\Client\ConnectionException $e) {
            Log::error('無法連接到 Google Maps API (multi-point)', ['exception' => $e->getMessage()]);
            return $this->sendError('網路連線錯誤，無法連接到地圖服務。', [], 504);
        } catch (Exception $e) {
            Log::error('呼叫 Google Maps API 時發生未知錯誤 (multi-point)', ['exception' => $e->getMessage()]);
            return $this->sendError('計算多點距離時發生未知錯誤。', [], 500);
        }
    }

    /**
     * 估算多點之間的行車距離和時間 (無需 reCAPTCHA)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMultiPointRouteDetails(Request $request)
    {
        // --- 取得並驗證地點陣列 ---
        $input = $request->all();
        $validator = Validator::make($input, [
            'locations' => 'required|array|min:2',
            'locations.*' => 'required|string|max:255',
        ], [
            'locations.required' => '缺少地點陣列 (locations)。',
            'locations.array' => '地點 (locations) 必須是陣列格式。',
            'locations.min' => '地點陣列 (locations) 至少需要包含起點和終點 (2個地點)。',
            'locations.*.required' => '地點陣列中的地址不能為空。',
            'locations.*.string' => '地點陣列中的地址必須是文字。',
        ]);

        if ($validator->fails()) {
            return $this->sendError('輸入參數錯誤，請檢查地點陣列。', $validator->errors()->toArray());
        }

        $validatedData = $validator->validated();
        
        // --- API Key ---
        $apiKey = config('services.google_maps.api_key');
        if (!$apiKey) {
            Log::error('Google Maps API key not configured');
            return $this->sendError('伺服器設定錯誤，缺少 Google Maps API 金鑰。', [], 500);
        }

        // --- reCAPTCHA Validation REMOVED ---

        $allLocations = $validatedData['locations'];
        $locationsCount = count($allLocations);

        Log::info('Multi-point route details request', ['locations' => $allLocations]);

        // --- 呼叫 Google Directions API (Loop through legs) ---
        $apiUrl = "https://maps.googleapis.com/maps/api/directions/json";
        $totalDistanceMeters = 0;
        $totalDurationSeconds = 0;
        $legs = [];

        try {
            if ($locationsCount >= 2) {
                for ($i = 0; $i < $locationsCount - 1; $i++) {
                    $currentOrigin = $allLocations[$i];
                    $currentDestination = $allLocations[$i + 1];

                    Log::debug("Processing leg: Origin='{$currentOrigin}', Destination='{$currentDestination}'");

                    $response = Http::get($apiUrl, [
                        'origin' => $currentOrigin,
                        'destination' => $currentDestination,
                        'key' => $apiKey,
                        'language' => 'zh-TW',
                        'mode' => 'driving',
                    ]);

                    if (!$response->successful()) {
                        Log::error('Google Maps API request failed (multi-point leg)', [
                            'status' => $response->status(),
                            'body' => $response->body(),
                            'origin' => $currentOrigin,
                            'destination' => $currentDestination
                        ]);
                        $errorMsg = '無法連接 Google Maps 服務';
                        if ($response->status() == 400) {
                            $errorMsg = 'Google Maps API 請求錯誤 (可能是地址無效或格式錯誤)';
                        }
                        return $this->sendError($errorMsg, ['api_status' => $response->status()], $response->status() >= 500 ? 503 : 400);
                    }

                    $data = $response->json();

                    if ($data['status'] !== 'OK') {
                        Log::warning('Google Maps API returned status: ' . $data['status'] . ' (multi-point leg)', [
                            'origin' => $currentOrigin,
                            'destination' => $currentDestination,
                            'error_message' => $data['error_message'] ?? 'No error message provided'
                        ]);
                        return $this->handleGoogleApiError($data['status']);
                    }

                    if (isset($data['routes'][0]['legs'][0]['distance']['value'])) {
                        $legDistanceMeters = $data['routes'][0]['legs'][0]['distance']['value'];
                        $legDurationSeconds = $data['routes'][0]['legs'][0]['duration']['value'];

                        $totalDistanceMeters += $legDistanceMeters;
                        $totalDurationSeconds += $legDurationSeconds;

                        $legs[] = [
                            'origin' => $data['routes'][0]['legs'][0]['start_address'],
                            'destination' => $data['routes'][0]['legs'][0]['end_address'],
                            'distance_km' => round($legDistanceMeters / 1000, 2),
                            'distance_text' => $data['routes'][0]['legs'][0]['distance']['text'] ?? '無法取得',
                            'duration_seconds' => $legDurationSeconds,
                            'duration_text' => $data['routes'][0]['legs'][0]['duration']['text'] ?? '無法取得',
                        ];
                    } else {
                        Log::error('無法從 Google Maps API 回應中解析路段距離', [
                            'response_data' => $data,
                            'origin' => $currentOrigin,
                            'destination' => $currentDestination
                        ]);
                        return $this->sendError('無法從 Google Maps API 回應中解析路段距離。');
                    }
                }
            } else {
                Log::error('Less than 2 locations provided for multi-point calculation after validation.');
                return $this->sendError('需要至少兩個地點才能計算路徑。');
            }

            $totalDistanceKm = round($totalDistanceMeters / 1000, 2);
            $totalDurationText = $this->formatDuration($totalDurationSeconds);

            // --- Cost calculation parameters (copied from costEstimateMultiPoint) ---
            $baseDistanceKm = 20.0;
            $baseCost = 600;
            $extraKmCostStandard = 25;
            $extraKmCostVan = 30;
            $roundUpUnit = 100;
            $waypointSurchargePerStop = 100;

            $calculatedCostStandardBase = 0;
            $calculatedCostVanBase = 0;
            $costMessage = null;

            if ($totalDistanceKm <= $baseDistanceKm) {
                $calculatedCostStandardBase = $baseCost;
                $calculatedCostVanBase = $baseCost;
                $costMessage = "基本起跳{$baseDistanceKm}公里";
            } else {
                $extraDistance = $totalDistanceKm - $baseDistanceKm;
                $rawTotalCostStandard = $baseCost + ($extraDistance * $extraKmCostStandard);
                $calculatedCostStandardBase = (int) (ceil($rawTotalCostStandard / $roundUpUnit) * $roundUpUnit);
                $rawTotalCostVan = $baseCost + ($extraDistance * $extraKmCostVan);
                $calculatedCostVanBase = (int) (ceil($rawTotalCostVan / $roundUpUnit) * $roundUpUnit);
                $costMessage = "超過{$baseDistanceKm}公里 (標準車每公里加收{$extraKmCostStandard}元, Van每公里加收{$extraKmCostVan}元)";
            }
            
            $waypointSurcharge = 0;
            $surchargeMessagePart = null;

            if ($locationsCount >= 3) {
                $numberOfWaypoints = $locationsCount - 2;
                $waypointSurcharge = $numberOfWaypoints * $waypointSurchargePerStop;
                $surchargeMessagePart = "{$numberOfWaypoints}個停靠點加收 {$waypointSurcharge}元";
                Log::info("Waypoint surcharge calculated", [
                    'locations_count' => $locationsCount,
                    'waypoints' => $numberOfWaypoints,
                    'surcharge_per_stop' => $waypointSurchargePerStop,
                    'total_surcharge' => $waypointSurcharge,
                ]);
            }
            
            $finalCostStandard = $calculatedCostStandardBase + $waypointSurcharge;
            $finalCostVan = $calculatedCostVanBase + $waypointSurcharge;

            if (!is_null($surchargeMessagePart)) {
                if (is_null($costMessage)) {
                    $costMessage = $surchargeMessagePart;
                } else {
                    $costMessage .= " + " . $surchargeMessagePart;
                }
            }

            $resultData = [
                'legs' => $legs,
                'total_distance_meters' => $totalDistanceMeters,
                'total_distance_km' => $totalDistanceKm,
                'total_duration_seconds' => $totalDurationSeconds,
                'total_duration_text' => $totalDurationText,
                'estimated_cost_standard' => $finalCostStandard,
                'estimated_cost_van' => $finalCostVan,
                'cost_message' => $costMessage,
            ];

            Log::info('Multi-point route details calculated successfully', $resultData);
            return $this->sendResponse($resultData, '成功計算多點路線詳情與費用。');

        } catch (\Illuminate\Http\Client\ConnectionException $e) {
            Log::error('無法連接到 Google Maps API (multi-point)', ['exception' => $e->getMessage()]);
            return $this->sendError('網路連線錯誤，無法連接到地圖服務。', [], 504);
        } catch (Exception $e) {
            Log::error('呼叫 Google Maps API 時發生未知錯誤 (multi-point)', ['exception' => $e->getMessage()]);
            return $this->sendError('計算多點路線詳情時發生未知錯誤。', [], 500);
        }
    }

    /**
     * Helper function to handle Google API error statuses.
     *
     * @param string $status
     * @return \Illuminate\Http\JsonResponse
     */
    public function handleGoogleApiError(string $status)
    {
        switch ($status) {
            case 'ZERO_RESULTS':
                return $this->sendError('在指定的地址之間找不到路線。');
            case 'NOT_FOUND':
                return $this->sendError('無法解析提供的地址之一或多個。請檢查地址是否正確。');
            case 'REQUEST_DENIED':
                return $this->sendError('Google Maps API 請求被拒絕，請檢查 API 金鑰或設定。');
            case 'OVER_QUERY_LIMIT':
                return $this->sendError('已超出 Google Maps API 的查詢配額。');
            case 'INVALID_REQUEST':
                 return $this->sendError('Google Maps API 請求無效，可能是參數錯誤。');
            default:
                return $this->sendError('Google Maps API 發生未知錯誤: ' . $status);
        }
    }

    /**
     * Helper function to format duration in seconds to a human-readable string.
     *
     * @param int $seconds
     * @return string
     */
    public function formatDuration(int $seconds): string
    {
        if ($seconds < 60) {
            return $seconds . " 秒";
        }
        $minutes = floor($seconds / 60);
        $hours = floor($minutes / 60);
        $remainingMinutes = $minutes % 60;

        $durationString = '';
        if ($hours > 0) {
            $durationString .= $hours . " 小時 ";
        }
        if ($remainingMinutes > 0) {
            $durationString .= $remainingMinutes . " 分鐘";
        }
        // Handle case where duration is exactly on the hour
        if (empty($durationString) && $hours > 0) {
             $durationString = $hours . " 小時";
        }
        // Handle case where duration is less than an hour but more than 0 minutes
        if (empty($durationString) && $minutes > 0) {
             $durationString = $minutes . " 分鐘";
        }
        // Handle edge case of exactly 0 seconds (shouldn't happen with valid routes)
        if (empty($durationString)) {
            return "0 分鐘";
        }

        return trim($durationString);
    }
}
