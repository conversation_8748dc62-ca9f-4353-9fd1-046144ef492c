<?php

namespace App\Http\Controllers;

use App\Exports\QuotationExport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Http\Request;

class ExportController extends Controller
{
    public function exportQuotations(Request $request)
    {
        $filters = $request->only(['status', 'order_type', 'from', 'to']);
        $ids = $request->input('ids', []);
        if (is_string($ids)) {
            $ids = explode(',', $ids);
        }
        return Excel::download(new QuotationExport($filters, $ids), 'quotations.csv');
    }
}
