<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DriverAccommodation;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use GuzzleHttp\Client;

class DriverAccommodationController extends Controller
{
    public function search(Request $request): JsonResponse
    {
        $query = $request->input('query');
        if (!$query) {
            return response()->json(['error' => '請輸入查詢關鍵字'], 400);
        }

        // 支援多關鍵字查詢（AND 條件，所有關鍵字都需出現於任一欄位）
        $keywords = preg_split('/\s+/', $query);
        if (count($keywords) === 1 && mb_strlen($query, 'UTF-8') > 2) {
            $keywords = preg_split('//u', $query, -1, PREG_SPLIT_NO_EMPTY);
        }
        $relevantAccommodations = DriverAccommodation::where(function($q) use ($keywords) {
            foreach ($keywords as $word) {
                $q->where(function($subQ) use ($word) {
                    $subQ->where('name', 'like', "%{$word}%")
                        ->orWhere('address', 'like', "%{$word}%")
                        ->orWhere('description', 'like', "%{$word}%")
                        ->orWhere('location', 'like', "%{$word}%")
                        ->orWhere('tags', 'like', "%{$word}%");
                });
            }
        })
        ->where('suitable_for_drivers', true)
        ->limit(5)
        ->get();

        if ($relevantAccommodations->isEmpty()) {
            return response()->json(['message' => '沒有找到相關的司機住宿資訊'], 200);
        }

        // 組 Gemini Prompt
        $context = "以下是 suitable_for_drivers=1（適合司機住宿與停車）的住宿資訊：\n";
        foreach ($relevantAccommodations as $accommodation) {
            $context .= "- 名稱：{$accommodation->name}\n";
            $context .= "  地址：{$accommodation->address}\n";
            if ($accommodation->price_range) {
                $context .= "  價格：{$accommodation->price_range}\n";
            }
            $context .= "  聯絡電話：" . ($accommodation->contact_phone ?? '無') . "\n";
            if ($accommodation->contact_name) {
                $context .= "  聯絡人：{$accommodation->contact_name}\n";
            }
            if ($accommodation->description) {
                $context .= "  描述：{$accommodation->description}\n";
            }
            // 新增：明確列出地區標籤
            $tags = $accommodation->tags;
            if (is_string($tags)) {
                $tags = json_decode($tags, true);
            }
            if (is_array($tags) && count($tags) > 0) {
                $context .= "  地區標籤：" . implode('、', $tags) . "\n";
            }
            $context .= "\n";
        }
        $prompt = "使用者詢問：{$query}\n\n請根據下方提供的住宿資訊，直接引用每一筆資料中的聯絡電話（contact_phone 欄位），不要自行猜測或省略。每筆住宿的地區標籤（tags）代表該住宿所屬或鄰近的地區，請將這些標籤納入地點判斷。請只列出相關且適合司機的住宿，並提供住宿名稱、地址、聯絡電話（若有 contact_phone 請務必列出），以及其他備註資訊。若沒有相關資訊，請明確告知沒有找到。\n\n{$context}\n\n回答：";

        // 呼叫 Gemini API
        $apiKey = config('services.gemini.api_key') ?? env('GEMINI_API_KEY');
        $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=' . $apiKey;

        try {
            $client = new Client();
            $response = $client->post($url, [
                'json' => [
                    'contents' => [
                        ['parts' => [['text' => $prompt]]]
                    ]
                ]
            ]);
            $data = json_decode($response->getBody(), true);
            $answer = $data['candidates'][0]['content']['parts'][0]['text'] ?? null;

            return response()->json([
                'answer' => $answer,
                'sources' => $relevantAccommodations,
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => '與 Gemini 模型通訊失敗：' . $e->getMessage()], 500);
        }
    }
}
