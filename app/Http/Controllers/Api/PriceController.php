<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Price;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PriceController extends Controller
{
    /**
     * 机场代码映射表
     */
    protected $airportMapping = [
        '松山機場' => '110900',
        'songshan' => '110900',
        'tsa' => '110900',
        '桃園機場' => '110903',
        'taoyuan' => '110903',
        'tpe' => '110903',
        '台中機場' => '120901',
        'taichung' => '120901',
        'rmq' => '120901',
        '高雄小港機場' => '130901',
        'kaohsiung' => '130901',
        'khh' => '130901'
    ];

    /**
     * 将机场代码或名称转换为 from_district_id
     *
     * @param string $airport 机场代码或名称
     * @return string 对应的 from_district_id
     */
    protected function getAirportDistrictId($airport)
    {
        // 将输入转换为小写以进行不区分大小写的匹配
        $airportLower = strtolower($airport);

        // 尝试直接匹配
        if (isset($this->airportMapping[$airport])) {
            return $this->airportMapping[$airport];
        }

        // 尝试小写匹配
        foreach ($this->airportMapping as $key => $value) {
            if (strtolower($key) === $airportLower) {
                return $value;
            }
        }

        // 如果输入已经是有效的 district_id 格式（6位数字），则直接返回
        if (preg_match('/^\d{6}$/', $airport)) {
            return $airport;
        }

        // 默认返回桃园机场的 ID
        return '110903';
    }

    /**
     * 根据条件查询价格
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPrice(Request $request)
    {
        // 验证请求参数
        $validator = Validator::make($request->all(), [
            'vendor_id' => 'required|integer',
            'airport' => 'required|string',
            'to_area_id' => 'required|string',
            'to_city_id' => 'required|string',
            'to_district_id' => 'required|string',
            'car_type' => 'nullable|integer',
            'order_type' => 'nullable|integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        // 获取请求参数
        $vendorId = $request->input('vendor_id');
        $airport = $request->input('airport');  // 接收 airport 参数
        $toAreaId = $request->input('to_area_id');
        $toCityId = $request->input('to_city_id');
        $toDistrictId = $request->input('to_district_id');
        $carType = $request->input('car_type', null);
        $orderType = $request->input('order_type', null);

        // Define vendor-specific surcharges for order_type = 1
        $vendorSurcharges = [
            ['vendor_id' => 2, 'order_type_1_price' => 100],
            ['vendor_id' => 10, 'order_type_1_price' => 100],
            ['vendor_id' => 11, 'order_type_1_price' => 200],
            // Add more vendors and surcharges as needed
        ];

        // 将 airport 转换为 from_district_id
        $fromDistrictId = $this->getAirportDistrictId($airport);

        // 记录转换信息
        Log::info('Airport conversion:', [
            'original_airport' => $airport,
            'converted_to_district_id' => $fromDistrictId
        ]);

        // 构建查询 - order_type is NOT included here
        $query = Price::where('vendor_id', $vendorId)
            ->where('from_district_id', $fromDistrictId)  // 使用转换后的 from_district_id
            ->where('to_area_id', $toAreaId)
            ->where('to_city_id', $toCityId)
            ->where('to_district_id', $toDistrictId)
            ->where('status', 0);  // 假设 status=0 表示有效价格

        // 添加可选条件
        if ($carType !== null) {
            $query->where('car_type', $carType);
        }

        // 记录查询 SQL 以便调试
        Log::info('Price Query SQL:', [
            'sql' => $query->toSql(),
            'bindings' => $query->getBindings()
        ]);

        // 执行查询，只获取第一条记录
        $price = $query->first();

        // 检查是否找到价格
        if (!$price) {
            return response()->json([
                'success' => false,
                'message' => '未找到匹配的价格',
                'data' => null
            ], 404);
        }

        // Apply surcharge if order_type is 1
        // $calculatedPrice = $price->price; // Assuming 'price' is the column name for the price value
        $basePriceValue = $price->price;  // 取得基本價格
        $calculatedPrice = $basePriceValue;  // 初始化計算後價格為基本價格
        $surchargeDescription = '';  // 用於記錄或可能的客戶端回饋
        if ($orderType == 1) {
            $pickupSurcharge = 100;  // 設定預設的接機加價為 100
            $surchargeDescription = "預設接機加價: +{$pickupSurcharge}";

            // 檢查是否有針對此 vendor_id 的特定加價規則，若有則覆寫預設值
            foreach ($vendorSurcharges as $surchargeRule) {
                if ($surchargeRule['vendor_id'] == $vendorId) {
                    $pickupSurcharge = $surchargeRule['order_type_1_price'];  // 使用特定的加價金額
                    $surchargeDescription = "特定廠商 ({$vendorId}) 接機加價: +{$pickupSurcharge}";
                    break;
                }
            }
            $calculatedPrice += $pickupSurcharge;  // 將確定的加價金額加到總價
            Log::info('接機加價已應用.', [
                'vendor_id' => $vendorId,
                'order_type' => $orderType,
                'surcharge_amount' => $pickupSurcharge,
                'description' => $surchargeDescription,
                'base_price' => $basePriceValue,
                'final_price' => $calculatedPrice
            ]);
        }

        // Prepare data for response
        $responseData = $price->toArray();  // Convert model to array
        $responseData['calculated_price'] = $calculatedPrice;  // Add the calculated price
        // If you want to replace the original price, you can do:
        // $responseData['price'] = $calculatedPrice;

        // 返回价格信息
        return response()->json([
            'success' => true,
            'message' => '成功獲取價格訊息',
            // 'data' => $price // Original data
            'data' => $responseData  // Data with calculated price
        ], 200);
    }
}
