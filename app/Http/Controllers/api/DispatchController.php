<?php

namespace App\Http\Controllers\api;

use DateTime;
use App\Models\Car;
use App\Models\Driver;
use App\Models\Customer;
use App\Models\Dispatch;
use App\Models\ADispatch;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\DispatchSignature;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\File\File;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class DispatchController extends Controller
{
    public function store(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'customer_name' => 'required',
            'customer_mobile' => 'required',
            'line_id' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $driver = Driver::where('line_id', $input['line_id'])->first();
        // Log::info('申請派車單司機 '.json_encode($driver));
        $driver_car = Car::where('driver_id', '=',$driver->id)->first();
        // Log::info('申請派車單司機車輛 '.json_encode($driver_car));
        $input['driver_id'] = $driver->id;
        $input['dispatch_id'] = 'DP_' . date("YmdHis") . Str::random(3);
        if(isset($input['start_date']) && isset($input['start_time'])){
            $input['start_time'] = $input['start_date']." ".$input['start_time'];
        }else{
            $input['start_time'] = '00:00:00';
        }
        if(isset($input['end_date']) && isset($input['end_time'])){
            $input['end_time'] = $input['end_date']." ".$input['end_time'];
        }else{
            $input['end_time'] = '00:00:00';
        }
        $input['vendor_id'] = $driver->vendor_id;
        // Log::info('STORE 給的資料 '.json_encode($input));
        $dispatch = Dispatch::create($input);
        // Log::info('派車單ID(新增) '.$dispatch->id);
        // Log::info('派車單 '.json_encode($dispatch));
        // var_dump($driver_car);die;
        $data = array();
        // if($input['car_license']){

        // }
        if($dispatch){
            $data = array(
                'dispatch_id' => $input['dispatch_id'],
                'name' => $input['customer_name'],
                'phone' => $input['customer_mobile'],
                'customer_address' => (empty($input['customer_address']) ? '' : $input['customer_address']),
                'customer_id' => (empty($input['customer_id']) ? '' : $input['customer_id']),
                'sex' => $input['customer_sex'],
                'people' => (empty($input['people']) ? '': $input['people']),
                'driver_name' => $driver->name,
                'driver_mobile' => $driver->mobile,
                'car_license' => $input['car_license'] ?? $driver_car->car_license,
                'car_engine_no' => (empty($driver_car->car_engine_no) ? '' : $driver_car->car_engine_no),
                'car_type' => $driver_car->car_type,
                'start_time' => $dispatch->start_time,
                'end_time' => $dispatch->end_time,
                'vendor_id' => $driver->vendor_id,
                'person_id' => $driver->person_id,
                'address' => (empty($driver->address) ? '' : $driver->address),
                'odometer' => (empty($input['odometer']) ? '' : $input['odometer']),
                'route' => (empty($input['route']) ? '': $input['route']),
                'rental_cost' => (empty($input['rental_cost']) ? '': $input['rental_cost']),
                'flight_no' => (empty($input['flight_no']) ? '': $input['flight_no']),
            );
            Log::info('派車單DATA '.json_encode($data));
            // var_dump($data);die;
            $this->makeImage($input['dispatch_id'], $data, $driver->vendor_id);
        }
        $dispatch['dispatch_id'] = $input['dispatch_id'];
        $dispatch['image_path'] = $input['dispatch_id']. '.jpg';
        $dispatch->save();
        return $this->sendResponse($dispatch, 'Dispatch Created Successfully.');
    }

    public function makeImage($dpid, $data, $vendor_id){
        if($vendor_id==1){
            $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg.jpg'));
        }else if($vendor_id==3){
            $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_03.jpg'));
        }else if($vendor_id==4){
            $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_4.jpg'));
        }else if($vendor_id>=5){
            $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_'.$vendor_id.'.jpg'));
        }else{
            $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_test.jpg'));
        }
        $fontPath = public_path('fonts/jf-openhuninn-2.0.ttf');
        $black = imagecolorallocate($background, 0, 0, 0);
        $fullName = $data['name'];
        $onlyName = $data['name'];
        if(strlen($fullName) > 3){
        }else{
            if($data['sex']==0){
                $fullName = $data['name'].' 小姐/女士';
            }else{
                $fullName = $data['name'].' 先生';
            }
        }
        if($vendor_id==5){
            $this->makeEPaper($background, $dpid, $data, $vendor_id, $black, $fontPath, $fullName, $onlyName);
        }else{
            // Log::info('fullName '.strlen($fullName));
            if(strlen($fullName) > 12){
                $y = 170;
                $maxWidth = 220;
                $lines = $this->wrapText(20, 0, $fontPath, $fullName, $maxWidth);
                foreach ($lines as $line) {
                    imagettftext($background, 18, 0, 330, $y, $black, $fontPath, $line);
                    $y += 25;
                }
            }else{
                imagettftext($background, 24, 0, 340, 182, $black, $fontPath, $fullName);
            }
            // imagettftext($background, 26, 0, 670, 182, $black, $fontPath, $data['phone']);
            if(strlen($data['phone']) > 10){
                imagettftext($background, 20, 0, 660, 182, $black, $fontPath, $data['phone']);
            }else{
                imagettftext($background, 26, 0, 670, 182, $black, $fontPath, $data['phone']);
            }
            // imagettftext($background, 20, 0, 500, 220, $black, $fontPath, $data['dispatch_id']);
            imagettftext($background, 20, 0, 330, 242, $black, $fontPath, $data['customer_address']);
            imagettftext($background, 18, 0, 1070, 242, $black, $fontPath, $data['customer_id']);
            imagettftext($background, 18, 0, 960, 128, $black, $fontPath, $data['dispatch_id']);
            // 自駕司機
            imagettftext($background, 24, 0, 470, 364, $black, $fontPath, $data['driver_name']);
            $y = 350;
            $maxWidth = 200;
            $lines = $this->wrapText(20, 0, $fontPath, $data['address'], $maxWidth);
            foreach ($lines as $line) {
                imagettftext($background, 20, 0, 1070, $y, $black, $fontPath, $line);
                $y += 25;
            }
            // imagettftext($background, 24, 0, 980, 360, $black, $fontPath, $data['address']);
            imagettftext($background, 22, 0, 470, 425, $black, $fontPath, $data['car_license']);
            imagettftext($background, 20, 0, 760, 364, $black, $fontPath, $data['person_id']);
            // imagettftext($background, 14, 0, 755, 425, $black, $fontPath, $data['car_engine_no']);
            $y2 = 412;
            $maxWidth2 = 160;
            $lines2 = $this->wrapText(18, 0, $fontPath, $data['car_engine_no'], $maxWidth2);
            foreach ($lines2 as $line2) {
                imagettftext($background, 18, 0, 755, $y2, $black, $fontPath, $line2);
                $y2 += 25;
            }
            if(strlen($data['car_type']) > 12){
                $y3 = 420;
                $maxWidth3 = 200;
                $lines3 = $this->wrapText(18, 0, $fontPath, $data['car_type'], $maxWidth3);
                foreach ($lines3 as $line3) {
                    imagettftext($background, 16, 0, 1080, $y3, $black, $fontPath, $line3);
                    $y3 += 20;
                }
            }else{
                imagettftext($background, 18, 0, 1074, 425, $black, $fontPath, $data['car_type']);
            }
            if(!empty($data['start_time']) || !empty($data['end_time'])){
                $datestr = $this->convertToChineseDate($data['start_time'], $data['end_time']);
                imagettftext($background, 20, 0, 320, 490, $black, $fontPath, $datestr);
            }
            // if(!empty($data['end_time'])){
            //     imagettftext($background, 28, 0, 765, 490, $black, $fontPath, $data['end_time']);
            // }
            imagettftext($background, 30, 0, 356, 550, $black, $fontPath, $data['rental_cost']);
            imagettftext($background, 20, 0, 760, 550, $black, $fontPath, $data['odometer']);
            imagettftext($background, 18, 0, 1080, 550, $black, $fontPath, $data['flight_no']);
            imagettftext($background, 24, 0, 1160, 920, $black, $fontPath, $data['driver_name']);

            // 派車單
            // imagettftext($background, 20, 0, 248, 1050, $black, $fontPath, $fullName);
            if(strlen($fullName) > 12){
                $y = 1035;
                $maxWidth = 210;
                $lines = $this->wrapText(20, 0, $fontPath, $fullName, $maxWidth);
                foreach ($lines as $line) {
                    imagettftext($background, 18, 0, 245, $y, $black, $fontPath, $line);
                    $y += 25;
                }
            }else{
                imagettftext($background, 20, 0, 248, 1050, $black, $fontPath, $fullName);
            }
            imagettftext($background, 20, 0, 940, 1050, $black, $fontPath, $data['people']);

            if(strlen($data['phone']) > 10){
                imagettftext($background, 12, 0, 1184, 1050, $black, $fontPath, $data['phone']);
            }else{
                imagettftext($background, 16, 0, 1187, 1050, $black, $fontPath, $data['phone']);
            }
            // imagettftext($background, 16, 0, 1187, 1050, $black, $fontPath, $data['phone']);
            imagettftext($background, 24, 0, 248, 1110, $black, $fontPath, $data['car_license']);
            imagettftext($background, 24, 0, 630, 1110, $black, $fontPath, $data['driver_name']);
            imagettftext($background, 16, 0, 898, 1110, $black, $fontPath, $data['person_id']);
            imagettftext($background, 16, 0, 1187, 1110, $black, $fontPath, $data['driver_mobile']);
            // Log::info('start==== date  '.$data['start_time'].'------'.$data['end_time']);
            if(!empty($data['start_time']) || !empty($data['end_time'])){
                $datestr = $this->convertToChineseDate($data['start_time'], $data['end_time']);
                imagettftext($background, 22, 0, 257, 1177, $black, $fontPath, $datestr);
            }
            // if(!empty($data['end_time'])){
            //     imagettftext($background, 28, 0, 680, 1177, $black, $fontPath, $data['end_time']);
            // }

            imagettftext($background, 18, 0, 1190, 1235, $black, $fontPath, $data['flight_no']);
            imagettftext($background, 24, 0, 257, 1235, $black, $fontPath, $data['route']);
            imagettftext($background, 24, 0, 930, 1235, $black, $fontPath, $data['rental_cost']);

            imagettftext($background, 24, 0, 257, 1600, $black, $fontPath, $data['driver_name']);
            imagettftext($background, 24, 0, 700, 1600, $black, $fontPath, $data['driver_name']);
            // imagettftext($background, 24, 0, 1148, 1600, $black, $fontPath, $onlyName);
            if(strlen($onlyName) > 12){
                imagettftext($background, 12, 0, 1140, 1600, $black, $fontPath, $onlyName);
            }else{
                imagettftext($background, 24, 0, 1148, 1600, $black, $fontPath, $onlyName);
            }
            // header('Content-Type: image/jpeg');
            $outputFileName = $dpid . '.jpg';
            $outputFilePath = public_path('images/' . $outputFileName);
            // Log::info('輸出檔案路徑: '.$outputFilePath);
            imagejpeg($background, $outputFilePath);
            imagedestroy($background);
            return 'OK';
        }

    }
    private function makeEPaper($background, $dpid, $data, $vendor_id, $black, $fontPath, $fullName, $onlyName){
        // 天美派車單
        $maxWidth = 300;
        imagettftext($background, 30, 0, 340, 240, $black, $fontPath, $fullName);
        imagettftext($background, 26, 0, 1670, 240, $black, $fontPath, $data['phone']);
        // imagettftext($background, 26, 0, 1280, 240, $black, $fontPath, $data['customer_address']);
        $y0 = 220;
        $lines = $this->wrapText(20, 0, $fontPath, $data['customer_address'], $maxWidth);
        foreach ($lines as $line) {
            imagettftext($background, 20, 0, 1280, $y0, $black, $fontPath, $line);
            $y0 += 40;
        }
        imagettftext($background, 20, 0, 1525, 1350, $black, $fontPath, $data['dispatch_id']);
        // 自駕司機
        imagettftext($background, 30, 0, 508, 480, $black, $fontPath, $data['driver_name']);
        imagettftext($background, 26, 0, 970, 480, $black, $fontPath, $data['person_id']);
        // 司機地址
        // imagettftext($background, 26, 0, 1280, 480, $black, $fontPath, $data['address']);
        $y = 460;
        $lines = $this->wrapText(20, 0, $fontPath, $data['address'], $maxWidth);
        foreach ($lines as $line) {
            imagettftext($background, 20, 0, 1280, $y, $black, $fontPath, $line);
            $y += 40;
        }
        imagettftext($background, 26, 0, 500, 590, $black, $fontPath, $data['car_license']);
        imagettftext($background, 26, 0, 1515, 590, $black, $fontPath, $data['route']);
        imagettftext($background, 26, 0, 970, 590, $black, $fontPath, $data['car_type']);
        if(!empty($data['start_time'])){
            $correctedTime = str_replace(',', '-', substr($data['start_time'], 0, 10)) . ' ' . str_replace(',', ':', substr($data['start_time'], 11));
            $mydate = new DateTime($correctedTime);
            $year = (int)$mydate->format('Y') - 1911;
            $month = $mydate->format('m');
            $day = $mydate->format('d');
            $hours = $mydate->format('H');
            $min = $mydate->format('i');
            imagettftext($background, 26, 0, 480, 685, $black, $fontPath, $year);
            imagettftext($background, 26, 0, 600, 685, $black, $fontPath, $month);
            imagettftext($background, 26, 0, 690, 685, $black, $fontPath, $day);
            imagettftext($background, 26, 0, 795, 685, $black, $fontPath, $hours);
            imagettftext($background, 26, 0, 910, 685, $black, $fontPath, $min);
        }
        if(!empty($data['end_time'])){
            $correctedTime = str_replace(',', '-', substr($data['end_time'], 0, 10)) . ' ' . str_replace(',', ':', substr($data['end_time'], 11));
            $mydate_e = new DateTime($correctedTime);
            $year_e = (int)$mydate_e->format('Y') - 1911;
            $month_e = $mydate_e->format('m');
            $day_e = $mydate_e->format('d');
            $hours_e = $mydate_e->format('H');
            $min_e = $mydate_e->format('i');
            imagettftext($background, 26, 0, 1220, 685, $black, $fontPath, $year_e);
            imagettftext($background, 26, 0, 1355, 685, $black, $fontPath, $month_e);
            imagettftext($background, 26, 0, 1460, 685, $black, $fontPath, $day_e);
            imagettftext($background, 26, 0, 1560, 685, $black, $fontPath, $hours_e);
            imagettftext($background, 26, 0, 1676, 685, $black, $fontPath, $min_e);
            // imagettftext($background, 30, 0, 800, 700, $black, $fontPath, $data['end_time']);
        }
        imagettftext($background, 26, 0, 1670, 480, $black, $fontPath, $data['driver_mobile']);
        imagettftext($background, 30, 0, 215, 810, $black, $fontPath, $data['rental_cost']);
        imagettftext($background, 30, 0, 610, 810, $black, $fontPath, $data['odometer']);
        imagettftext($background, 40, 0, 1100, 1275, $black, $fontPath, $data['driver_name']);
        imagettftext($background, 40, 0, 418, 1275, $black, $fontPath, $onlyName);
        $mydate_created = new DateTime();
        $year_c = (int)$mydate_created->format('Y') - 1911;
        $month_c = $mydate_created->format('m');
        $day_c = $mydate_created->format('d');
        imagettftext($background, 30, 0, 1555, 138, $black, $fontPath, $year_c);
        imagettftext($background, 30, 0, 1686, 138, $black, $fontPath, $month_c);
        imagettftext($background, 30, 0, 1783, 138, $black, $fontPath, $day_c);
        $outputFileName = $dpid . '.jpg';
        $outputFilePath = public_path('images/' . $outputFileName);
        imagejpeg($background, $outputFilePath);
        imagedestroy($background);
        return 'OK';

    }
    private function wrapText($fontSize, $angle, $fontPath, $text, $maxWidth)
    {
        $lines = [];
        $line = "";
        for ($i = 0; $i < mb_strlen($text); $i++) {
            $char = mb_substr($text, $i, 1);
            $testString = $line . $char;
            $size = imagettfbbox($fontSize, $angle, $fontPath, $testString);
            if ($size[2] > $maxWidth && !empty($line)) {
                $lines[] = $line;
                $line = $char;
            }else{
                $line = $testString;
            }
        }
        $lines[] = $line;
        return $lines;
    }
    public function editdispatch(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            // 'dispatch_id' => 'required',
            'line_id' => 'required',
            'customer_name' => 'required',
            'customer_mobile' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // var_dump($input);die;
        $driver = Driver::where('line_id', $input['line_id'])->first();
        if(!empty($driver)){
            $dispatch = Dispatch::where('id', $input['id'])
                ->update([
                    'customer_name' => $input['customer_name'],
                    'customer_mobile' => $input['customer_mobile'],
                    'customer_address' => $input['customer_address'],
                    'customer_id' => $input['customer_id'],
                    'people' => $input['people'],
                    'start_time' => $input['start_time'],
                    'end_time' => $input['end_time'],
                    'route' => $input['route'],
                    'rental_cost' => $input['rental_cost'],
                    'odometer' => $input['odometer'],
                    'flight_no' => $input['flight_no'],
                ]);
                if($dispatch){
                    $driver_car = Car::where('driver_id', '=',$driver->id)->first();
                    $dispatchData = Dispatch::where('id', $input['id'])->first();
                    $data = array(
                        'dispatch_id' => $dispatchData['dispatch_id'],
                        'name' => $dispatchData['customer_name'],
                        'phone' => $dispatchData['customer_mobile'],
                        'customer_address' => (empty($dispatchData['customer_address']) ? '' : $dispatchData['customer_address']),
                        'customer_id' => (empty($dispatchData['customer_id']) ? '' : $dispatchData['customer_id']),
                        'sex' => $dispatchData['customer_sex'],
                        'people' => (empty($dispatchData['people']) ? '': $dispatchData['people']),
                        'driver_name' => $driver->name,
                        'driver_mobile' => $driver->mobile,
                        'car_license' => $driver_car->car_license,
                        'car_engine_no' => (empty($driver_car->car_engine_no) ? '' : $driver_car->car_engine_no),
                        'car_type' => $driver_car->car_type,
                        'start_time' => $input['start_time'],
                        'end_time' => $input['end_time'],
                        'vendor_id' => $driver->vendor_id,
                        'person_id' => $driver->person_id,
                        'address' => (empty($driver->address) ? '' : $driver->address),
                        'odometer' => (empty($dispatchData['odometer']) ? '' : $dispatchData['odometer']),
                        'route' => (empty($dispatchData['route']) ? '': $dispatchData['route']),
                        'rental_cost' => (empty($dispatchData['rental_cost']) ? '': $dispatchData['rental_cost']),
                        'flight_no' => (empty($dispatchData['flight_no']) ? '': $dispatchData['flight_no']),
                    );
                    Log::info('派車單DATA API '.json_encode($data));
                    // var_dump($data);die;
                    $rs = $this->makeImage($dispatchData['dispatch_id'], $data, $driver->vendor_id);
                    return $this->sendResponse($dispatch, '派車單更新成功 '.$rs);
                }
                return $this->sendError('派車單更新失敗');
        }
        // Log::info('申請派車單司機 '.json_encode($driver));
        // $driver_car = Car::where('driver_id', '=',$driver->id)->first();
        // Log::info('申請派車單司機車輛 '.json_encode($driver_car));
    }
    public function storev2(Request $request)
    {
        // 建立雅潭派車單
        $input = $request->all();
        $validator = Validator::make($input, [
            'customer_name' => 'required',
            // 'customer_mobile' => 'required',
            'line_user_id' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        Log::info('申請派車單司機 V2 '.json_encode($input));
        $input['dispatch_id'] = 'DP_' . date("YmdHis") . Str::random(3);
        $driver = Driver::where('line_id', $input['line_user_id'])->first();
        $car = Car::where('driver_id', $driver->id)->first();
        $signatureData = $request->input('imgBase64');
        $input['customer_address'] = (empty($input['customer_department']) ? '' : $input['customer_department']);
        $input['driver_id'] = $driver->id;
        $input['vendor_id'] = $driver->vendor_id;
        $input['route'] = (empty($input['route_start']) ? '' : $input['route_start']).(empty($input['route_end']) ? '' : '  至  ' . $input['route_end']) ;
        $mydate = $input['start_time'];
        $hours = substr($mydate, 0, 2);
        $minutes = substr($mydate, 2, 2);
        $myTimeStr = $input['start_time'];
        $input['start_time'] = empty($input['start_date']) ? date("Y-m-d") : (string)$input['start_date'] . ' ' . $hours.':'.$minutes;
        if(empty($signatureData)){
            $dispatch = Dispatch::create($input);
            $signature_path = '';
            // return  $this->sendResponse('message', 'add successfully.');
        }else{
            $dispatch = Dispatch::create($input);
            $signatureData = str_replace('data:image/png;base64,', '', $signatureData);
            $signatureData = str_replace(' ', '+', $signatureData);
            $signature = base64_decode($signatureData);
            // 簽名檔名
            $fileName = 'st_' . $input['dispatch_id'] . '.png';
            Storage::put('public/signatures/' . $fileName, $signature);
            $signature_path = storage_path('app/public/signatures/' . $fileName);
        }

        $data = [
            'dispatch_id' => $input['dispatch_id'],
            'customer_name' => $input['customer_name'],
            'customer_mobile' => (empty($input['customer_mobile'])) ? '-' : $input['customer_mobile'],
            'customer_department' => (empty($input['customer_department'])) ? '-' : $input['customer_department'],
            'driver_name' => $driver->name,
            'driver_mobile' => $driver->mobile,
            'car_license' => $car->car_license,
            'driver_id' => $driver->id,
            'route' => (empty($input['route'])) ? '-' : $input['route'],
            'route_start' => (empty($input['route_start'])) ? '-' : $input['route_start'],
            'route_end' => (empty($input['route_end'])) ? '-' : $input['route_end'],
            'rental_cost' => (empty($input['rental_cost'])) ? '-' : $input['rental_cost'],
            'odometer' => 0,
            'signature' => $signature_path,
            'start_date' => $input['start_date'],
            'start_time' => ($myTimeStr == '0600') ? '日 DAY  06:00~23:00' : '夜 NIGHT  23:00~06:00',
        ];
        $makeImageResult = $this->makeYatanImage($data);
        if($makeImageResult=='error'){
            return  $this->sendError('message', 'Signature File failed.');
        }
        if(empty($signatureData)){
            Dispatch::where('id', $dispatch->id)
                ->update([
                    'image_path' => 'images/' . $data['dispatch_id'] . '.jpg',
                ]);
        }else{
            Dispatch::where('id', $dispatch->id)
                ->update([
                    'signature_file' => 'signatures/' . $fileName,
                    'image_path' => 'images/' . $data['dispatch_id'] . '.jpg',
                ]);
        }
        $rsDispatch = Dispatch::where('id', $dispatch->id)->first();
        return  $this->sendResponse($rsDispatch, 'File uploaded successfully.');
    }
    public function makeYatanImage($data)
    {
        // 台中雅潭派車單
        $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_7.jpg'));
        if(!empty($data['signature'])){
            $signature = imagecreatefrompng($data['signature']);
            $signature_width = imagesx($signature);
            $signature_height = imagesy($signature);
            $new_width = $signature_width / 2;
            $new_height = $signature_height / 2;
            $resized_signature = imagecreatetruecolor($new_width, $new_height);
            imagealphablending($resized_signature, false);
            imagesavealpha($resized_signature, true);
            imagecopyresampled(
                $resized_signature,  // 目标图像资源
                $signature,          // 源图像资源
                0, 0,                // 目标图像的 x, y 坐标
                0, 0,                // 源图像的 x, y 坐标
                $new_width, $new_height,  // 目标图像的宽度和高度
                $signature_width, $signature_height  // 源图像的宽度和高度
            );
        }

        $fontPath = public_path('fonts/jf-openhuninn-2.0.ttf');
        $black = imagecolorallocate($background, 0, 0, 0);
        $dispatch_id_dest_x = 1500; // 合并时在背景图上的x坐标
        $dispatch_id_dest_y = 140; // 合并时在背景图上的y坐标
        $customer_name_dest_x = 1325; // 合并时在背景图上的x坐标
        $customer_name_dest_y = 410; // 合并时在背景图上的y坐标
        $customer_department_x = 1325; // 合并时在背景图上的x坐标
        $customer_department_y = 573; // 合并时在背景图上的y坐标
        $start_date_dest_x = 539; // 合并时在背景图上的x坐标
        $start_date_dest_y = 260; // 合并时在背景图上的y坐标
        $start_time_dest_x = 355; // 合并时在背景图上的y坐标
        $start_time_dest_y = 355; // 合并时在背景图上的y坐标
        $rental_cost_dest_x = 760; // 合并时在背景图上的y坐标
        $rental_cost_dest_y = 410; // 合并时在背景图上的y坐标
        $driver_name_dest_x = 780; // 合并时在背景图上的y坐标
        $driver_name_dest_y = 760; // 合并时在背景图上的y坐标
        $car_license_dest_x = 500; // 合并时在背景图上的y坐标
        $car_license_dest_y = 760; // 合并时在背景图上的y坐标
        $start_time_dest_x = 1330; // 合并时在背景图上的y坐标
        $start_time_dest_y = 260; // 合并时在背景图上的y坐标
        $route_start_dest_x = 530; // 合并时在背景图上的y坐标
        $route_start_dest_y = 550; // 合并时在背景图上的y坐标
        // $route_end_dest_x = 800; // 合并时在背景图上的y坐标
        // $route_end_dest_y = 550; // 合并时在背景图上的y坐标
        imagettftext($background, 24, 0, $dispatch_id_dest_x, $dispatch_id_dest_y, $black, $fontPath, $data['dispatch_id']);
        imagettftext($background, 32, 0, $customer_name_dest_x, $customer_name_dest_y, $black, $fontPath, $data['customer_name']);
        imagettftext($background, 40, 0, $customer_department_x, $customer_department_y, $black, $fontPath, $data['customer_department']);
        imagettftext($background, 40, 0, $start_date_dest_x, $start_date_dest_y, $black, $fontPath, $data['start_date']);
        imagettftext($background, 36, 0, $rental_cost_dest_x, $rental_cost_dest_y, $black, $fontPath, $data['rental_cost']);
        imagettftext($background, 40, 0, $driver_name_dest_x, $driver_name_dest_y, $black, $fontPath, $data['driver_name']);
        imagettftext($background, 40, 0, $car_license_dest_x, $car_license_dest_y, $black, $fontPath, $data['car_license']);
        imagettftext($background, 30, 0, $start_time_dest_x, $start_time_dest_y, $black, $fontPath, $data['start_time']);
        if(strlen($data['route']) > 36){
            $y = $route_start_dest_y-30;
            $maxWidth = 400;
            $lines = $this->wrapText(20, 0, $fontPath, $data['route'], $maxWidth);
            foreach ($lines as $line) {
                imagettftext($background, 24, 0, $route_start_dest_x, $y, $black, $fontPath, $line);
                $y += 40;
            }
        }else{
            imagettftext($background, 30, 0, $route_start_dest_x, $route_start_dest_y, $black, $fontPath, $data['route']);
        }
        // imagettftext($background, 30, 0, $route_end_dest_x, $route_end_dest_y, $black, $fontPath, $data['route_end']);
        $outputFileName = $data['dispatch_id'] . '.jpg';
        $outputFilePath = public_path('images/' . $outputFileName);
        if(!empty($data['signature'])){
            imagecopy($background, $resized_signature, 1325, 633, 0, 0, $new_width,$new_height);
        }
        if(imagejpeg($background, $outputFilePath)){
            $result =  'images/' . $outputFileName;
        }else{
            $result = 'error';
        }
        imagedestroy($background);
        if(!empty($data['signature'])){
            imagedestroy($signature);
        }
        // imagedestroy($resized_signature);
        return $result;
    }

    public function uploadsignature(Request $request) {
        $input = $request->all();
        $validator = Validator::make($input, [
            'dispatch_id' => 'required',
            'line_id' => 'required',
            'imgBase64' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $signatureData = $request->input('imgBase64');
        if(empty($signatureData)){
            $signature_path = '';
            // return  $this->sendResponse('message', 'add successfully.');
        }else{
            $signatureData = str_replace('data:image/png;base64,', '', $signatureData);
            $signatureData = str_replace(' ', '+', $signatureData);
            $signature = base64_decode($signatureData);
            // 簽名檔名
            $fileName = 'st_' . $input['dispatch_id'] . '.png';
            Storage::put('public/signatures/' . $fileName, $signature);
            $signature_path = storage_path('app/public/signatures/' . $fileName);
            $updateData = Dispatch::where('dispatch_id', '=', $input['dispatch_id'])->update([
                'signature_file' => 'signatures/' .$fileName,
            ]);
            if ($updateData > 0) {
                // 更新成功
                $selectData =Dispatch::where('dispatch_id', '=', $input['dispatch_id'])->get()->first();
                $selectDriverData =Driver::where('id', $selectData->driver_id)->first();
                $selectCarData =Car::where('driver_id', '=', $selectData->driver_id)->first();
                $mydatetime = $selectData['start_time'];

                if($selectData->vendor_id == 7){
                    // 雅潭
                    $mydatetimeArray = explode(" ", $mydatetime);
                    $data = [
                        'dispatch_id' => $selectData['dispatch_id'],
                        'customer_name' => $selectData['customer_name'],
                        'customer_mobile' => $selectData['customer_mobile'],
                        'customer_department' => $selectData['customer_address'],
                        'driver_name' => $selectDriverData->name,
                        'driver_mobile' => $selectDriverData->name,
                        'car_license' => $selectCarData->car_license,
                        'driver_id' => $selectDriverData->id,
                        'route' => (empty($selectData['route'])) ? '-' : $selectData['route'],
                        'route_start' => (empty($selectData['route'])) ? '-' : $selectData['route'],
                        'rental_cost' => (empty($selectData['rental_cost'])) ? '-' : $selectData['rental_cost'],
                        'odometer' => 0,
                        'signature' => $signature_path,
                        'start_date' => $mydatetimeArray[0],
                        'start_time' => ($mydatetimeArray[1] == '06:00:00') ? '日 DAY  06:00~23:00' : '夜 NIGHT  23:00~06:00',
                    ];
                    $makeImageResult = $this->makeYatanImage($data);

                }else {
                    // 派車單加手寫簽名
                    $data = [
                        'dispatch_id' => $selectData['dispatch_id'],
                        'name' => $selectData['customer_name'],
                        'phone' => $selectData['customer_mobile'],
                        'customer_address' => (empty($selectData['customer_address']) ? '' : $selectData['customer_address']),
                        'sex' => $selectData['customer_sex'],
                        'people' => (empty($selectData['people']) ? '': $selectData['people']),
                        'driver_name' => $selectDriverData->name,
                        'driver_mobile' => $selectDriverData->mobile,
                        'car_license' => $selectCarData->car_license,
                        'car_engine_no' => $selectCarData->car_engine_no,
                        'car_type' => $selectCarData->car_type,
                        'start_time' => $selectData->start_time,
                        'end_time' => $selectData->end_time,
                        'vendor_id' => $selectDriverData->vendor_id,
                        'person_id' => $selectDriverData->person_id,
                        'address' => (empty($selectDriverData->address) ? '' : $selectDriverData->address),
                        'odometer' => (empty($selectData['odometer']) ? '' : $selectData['odometer']),
                        'route' => (empty($selectData['route']) ? '': $selectData['route']),
                        'rental_cost' => (empty($selectData['rental_cost']) ? '': $selectData['rental_cost']),
                        'flight_no' => (empty($selectData['flight_no']) ? '': $selectData['flight_no']),
                        'signature' => $signature_path,
                    ];
                    $makeImageResult = $this->makePublicTemplate($data, $selectData->vendor_id);
                }
                if($makeImageResult=='error'){
                    return  $this->sendError('message', 'Signature File failed.');
                }
                Dispatch::where('dispatch_id', $selectData['dispatch_id'])
                    ->update([
                        'image_path' => $makeImageResult,
                    ]);
                return  $this->sendResponse('message', 'File uploaded successfully.');

            } else {
                // 更新失败或没有任何变化
                return  $this->sendError('message', 'Signature File failed.');
            }
        }
    }
    public function uploadMultiSignatures(Request $request)
    {
        // Log::info('uploadMultiSignatures request received', $request->all());
        $startTime = microtime(true);

        // 1. Validate the incoming request structure
        $validator = Validator::make($request->all(), [
            // Assuming dispatch_id is the primary key 'id' for ADispatch
            'id' => 'required|integer|exists:a_dispatchs,id', // Check against a_dispatchs table
            'line_id' => 'required|string|exists:a_drivers,lineid', // Validate line_id exists in a_drivers table
            'signatures' => 'required|array', // Ensure 'signatures' is an array
            'signatures.*.passenger_id' => 'required|string|max:255', // Validate passenger_id within each object
            'signatures.*.name' => 'required|string|max:255',         // Validate name within each object
            'signatures.*.signature' => 'required|string',           // Validate signature (base64) within each object
        ], [
            // Custom error messages
            'id.required' => '派車單 ID 為必填。',
            'id.integer' => '派車單 ID 必須是數字。',
            'id.exists' => '找不到指定的派車單。', // Simplified message
            'line_id.required' => 'Line ID 為必填。',
            'line_id.exists' => '找不到指定的司機 Line ID。',
            'signatures.required' => '簽名資料陣列為必填。',
            'signatures.array' => '簽名資料必須是陣列格式。',
            'signatures.*.passenger_id.required' => '簽名物件中缺少 passenger_id。',
            'signatures.*.name.required' => '簽名物件中缺少 name。',
            'signatures.*.signature.required' => '簽名物件中缺少 signature (Base64)。',
            'signatures.*.signature.string' => '簽名物件中的 signature 必須是字串。',
        ]);

        if ($validator->fails()) {
            Log::error('uploadMultiSignatures validation failed', $validator->errors()->toArray());
            return $this->sendError('輸入資料驗證失敗', $validator->errors()->toArray());
        }

        $validatedData = $validator->validated();
        $dispatchId = $validatedData['id']; // This is the primary key 'id'
        $lineId = $validatedData['line_id'];
        $signaturesInfo = $validatedData['signatures']; // Array of signature objects

        try {
            // 2. Find the dispatch record using the primary key 'id'
            $dispatch = ADispatch::findOrFail($dispatchId);
            Log::info('Dispatch record found', ['dispatch_id' => $dispatch->id, 'dispatch_no' => $dispatch->dispatch_id]);

            // 3. Handle existing passenger_multi data (which might already be an array due to casting)
            $passengerMultiRaw = $dispatch->passenger_multi;
            $passengerDataArray = []; // Initialize as empty array

            if (is_array($passengerMultiRaw)) {
                // Already an array (likely due to model casting)
                $passengerDataArray = $passengerMultiRaw;
                Log::info('passenger_multi is already an array, using directly.', ['dispatch_id' => $dispatch->id]);
            } elseif (is_string($passengerMultiRaw) && !empty($passengerMultiRaw)) {
                // It's a string, try to decode it
                $decoded = json_decode($passengerMultiRaw, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    $passengerDataArray = $decoded;
                    Log::info('Successfully decoded passenger_multi string.', ['dispatch_id' => $dispatch->id]);
                } else {
                    Log::warning('Failed to decode passenger_multi string or result is not an array.', [
                        'dispatch_id' => $dispatch->id,
                        'json_error' => json_last_error_msg(),
                        'passenger_multi_raw' => $passengerMultiRaw
                    ]);
                    // Keep $passengerDataArray as empty array, or handle error differently
                    // return $this->sendError('派車單的乘客資料格式錯誤 (無法解析 JSON)。');
                }
            } elseif (is_null($passengerMultiRaw) || empty($passengerMultiRaw)) {
                Log::info('passenger_multi is null or empty, initializing as empty array.', ['dispatch_id' => $dispatch->id]);
                // Keep $passengerDataArray as empty array
            } else {
                // Unexpected type
                Log::error('Unexpected type for passenger_multi.', [
                    'dispatch_id' => $dispatch->id,
                    'type' => gettype($passengerMultiRaw)
                ]);
                // Keep $passengerDataArray as empty array or return error
                // return $this->sendError('派車單的乘客資料格式錯誤 (Unexpected Type)。');
            }

            // **** START FIX ****
            // 3b. Build a map for quick passenger index lookup
            $passengerMap = [];
            foreach ($passengerDataArray as $index => $passenger) {
                // Assuming the key in passenger_multi is 'id' or 'passenger_id'
                // Adjust 'id' if your key is different (e.g., 'passenger_id')
                $pId = $passenger['id'] ?? ($passenger['passenger_id'] ?? null);
                if ($pId !== null) {
                    $passengerMap[(string)$pId] = $index; // Cast key to string for reliable lookup
                } else {
                     Log::warning('Passenger entry in passenger_multi missing ID', ['dispatch_id' => $dispatch->id, 'index' => $index, 'passenger_data' => $passenger]);
                }
            }
            Log::debug('Passenger Map created', ['dispatch_id' => $dispatch->id, 'map' => $passengerMap]);

            // 3c. Initialize the processed signatures counter
            $signaturesProcessedCount = 0;
            // **** END FIX ****


            // 4. Process each signature object
            foreach ($signaturesInfo as $index => $signatureInfo) {
                $passengerId = (string)$signatureInfo['passenger_id']; // Cast to string for lookup
                $signatureBase64 = $signatureInfo['signature'];

                if (empty($signatureBase64)) {
                    Log::warning('Empty signature data found in array', ['dispatch_id' => $dispatch->id, 'index' => $index, 'passenger_id' => $passengerId]);
                    continue; // Skip empty entries
                }

                // 4a. Decode Base64
                if (strpos($signatureBase64, 'data:image/png;base64,') === 0) {
                    $signatureBase64 = str_replace('data:image/png;base64,', '', $signatureBase64);
                }
                $signatureBase64 = str_replace(' ', '+', $signatureBase64);
                $signatureBinary = base64_decode($signatureBase64, true);

                if ($signatureBinary === false) {
                    Log::error('Failed to decode base64 signature data', ['dispatch_id' => $dispatch->id, 'index' => $index, 'passenger_id' => $passengerId]);
                    continue; // Skip this invalid signature
                }

                // 4b. Generate Filename (Using dispatch_no for clarity)
                $timestamp = now()->format('YmdHisu');
                // Use passenger_id in filename for better identification
                $fileName = 'st_' . $dispatch->dispatch_no . '_' . $passengerId . '_' . $timestamp . '.png';
                $storageDir = 'public/signatures/' . $dispatch->dispatch_id; // Store in subfolder per dispatch
                $storagePath = $storageDir . '/' . $fileName; // Path for Storage facade
                $relativePath = 'signatures/' . $dispatch->dispatch_id . '/' . $fileName; // Relative path for DB

                // 4c. Save File
                $saveSuccess = Storage::put($storagePath, $signatureBinary);

                if (!$saveSuccess) {
                     Log::error('Failed to save signature file to storage', ['dispatch_id' => $dispatch->id, 'index' => $index, 'passenger_id' => $passengerId, 'path' => $storagePath]);
                     continue; // Skip saving this signature
                }

                Log::info('Signature file saved successfully', ['dispatch_id' => $dispatch->id, 'index' => $index, 'passenger_id' => $passengerId, 'relative_path' => $relativePath]);

                // 4d. Update the passengerDataArray
                // Find the passenger in the array using the map
                if (isset($passengerMap[$passengerId])) { // <--- Line 781 (Now $passengerMap is defined)
                    $passengerIndex = $passengerMap[$passengerId];
                    $passengerDataArray[$passengerIndex]['signature_path'] = $relativePath; // Add/Update signature path
                    $signaturesProcessedCount++; // <--- Line 783 (Now $signaturesProcessedCount is defined)
                    Log::info('Updated signature path in passenger_multi array', ['dispatch_id' => $dispatch->id, 'passenger_id' => $passengerId, 'index_in_array' => $passengerIndex]);
                } else {
                    Log::warning('Passenger ID from signature not found in existing passenger_multi', [
                        'dispatch_id' => $dispatch->id,
                        'passenger_id' => $passengerId,
                        'passenger_name' => $signatureInfo['name']
                    ]);
                    // Optional: Add the passenger if not found? Decide based on requirements.
                    // $passengerDataArray[] = [
                    //     'id' => $passengerId, // Or use 'passenger_id' key
                    //     'name' => $signatureInfo['name'],
                    //     'signature_path' => $relativePath
                    // ];
                    // $signaturesProcessedCount++;
                }

            } // End foreach loop

            // 5. Update the dispatch record with the modified passenger_multi
            if ($signaturesProcessedCount > 0) {
                // Use json_encode directly as Eloquent handles the casting back on save
                $dispatch->passenger_multi = $passengerDataArray;
                // Optionally clear the old single signature field if it exists and is no longer needed
                // $dispatch->signature_file = null;
                $dispatch->save();
                Log::info('Dispatch record updated with new passenger_multi', ['dispatch_id' => $dispatch->id]);
            } else {
                 Log::warning('No signatures were successfully processed to update passenger_multi', ['dispatch_id' => $dispatch->id]);
            }

            // 6. Image Regeneration (Still Skipped/Logged)
            Log::warning('uploadMultiSignatures: Image regeneration with multiple signatures is NOT implemented yet.', ['dispatch_id' => $dispatch->id]);
            // If you need to regenerate the image, you'll need to modify makePublicTemplate/makeYatanImage
            // to handle the array of paths in $passengerDataArray.

            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime);
            Log::info('uploadMultiSignatures completed', [
                'dispatch_id' => $dispatchId,
                'signatures_processed' => $signaturesProcessedCount,
                'execution_time' => $executionTime
            ]);

            if ($signaturesProcessedCount === 0 && count($signaturesInfo) > 0) {
                 return $this->sendError('沒有成功處理任何簽名檔，請檢查簽名資料或伺服器日誌。');
            }

            // 7. Return success response
            // Return the updated dispatch record which now includes the modified passenger_multi
            return $this->sendResponse($dispatch->toArray(), $signaturesProcessedCount . ' 個簽名檔已成功處理並更新至乘客資料。');

        } catch (ModelNotFoundException $e) {
            Log::error('uploadMultiSignatures: Dispatch not found by ID', ['dispatch_id' => $dispatchId ?? 'N/A']);
            return $this->sendError('找不到指定的派車單。'); // Consistent error message
        } catch (\Exception $e) {
            Log::error('Error during uploadMultiSignatures process', [
                'dispatch_id' => $dispatchId ?? 'N/A',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString() // Log trace for debugging
            ]);
            return $this->sendError('處理簽名上傳時發生錯誤，請稍後再試。', [], 500);
        }
    }
    public function makePublicTemplate($data, $vendor_id)
    {
        // Log::info(print_r($data));
        // 加簽名的派車單
        $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_'.$vendor_id.'.jpg'));
        if(!empty($data['signature'])){
            $signature = imagecreatefrompng($data['signature']);
            $signature_width = imagesx($signature);
            $signature_height = imagesy($signature);
            $new_width = $signature_width / 5;
            $new_height = $signature_height / 5;
            $resized_signature = imagecreatetruecolor($new_width, $new_height);
            imagealphablending($resized_signature, false);
            imagesavealpha($resized_signature, true);
            imagecopyresampled(
                $resized_signature,  // 目标图像资源
                $signature,          // 源图像资源
                0, 0,                // 目标图像的 x, y 坐标
                0, 0,                // 源图像的 x, y 坐标
                $new_width, $new_height,  // 目标图像的宽度和高度
                $signature_width, $signature_height  // 源图像的宽度和高度
            );
        }
        $fontPath = public_path('fonts/jf-openhuninn-2.0.ttf');
        $black = imagecolorallocate($background, 0, 0, 0);
        $fullName = $data['name'];
        $onlyName = $data['name'];
        if(strlen($fullName) > 3){
        }else{
            if($data['sex']==0){
                $fullName = $data['name'].' 小姐/女士';
            }else{
                $fullName = $data['name'].' 先生';
            }
        }
        // Log::info('fullName '.strlen($fullName));
        if(strlen($fullName) > 12){
            $y = 170;
            $maxWidth = 220;
            $lines = $this->wrapText(20, 0, $fontPath, $fullName, $maxWidth);
            foreach ($lines as $line) {
                imagettftext($background, 18, 0, 330, $y, $black, $fontPath, $line);
                $y += 25;
            }
        }else{
            imagettftext($background, 24, 0, 340, 182, $black, $fontPath, $fullName);
        }
        // imagettftext($background, 26, 0, 670, 182, $black, $fontPath, $data['phone']);
        if(strlen($data['phone']) > 10){
            imagettftext($background, 20, 0, 660, 182, $black, $fontPath, $data['phone']);
        }else{
            imagettftext($background, 26, 0, 670, 182, $black, $fontPath, $data['phone']);
        }
        // imagettftext($background, 20, 0, 500, 220, $black, $fontPath, $data['dispatch_id']);
        imagettftext($background, 20, 0, 330, 242, $black, $fontPath, $data['customer_address']);
        // imagettftext($background, 18, 0, 1070, 242, $black, $fontPath, $data['customer_id']);
        imagettftext($background, 18, 0, 960, 128, $black, $fontPath, $data['dispatch_id']);
        // 自駕司機
        imagettftext($background, 24, 0, 470, 364, $black, $fontPath, $data['driver_name']);
        $y = 350;
        $maxWidth = 200;
        $lines = $this->wrapText(20, 0, $fontPath, $data['address'], $maxWidth);
        foreach ($lines as $line) {
            imagettftext($background, 20, 0, 1070, $y, $black, $fontPath, $line);
            $y += 25;
        }
        // imagettftext($background, 24, 0, 980, 360, $black, $fontPath, $data['address']);
        imagettftext($background, 22, 0, 470, 425, $black, $fontPath, $data['car_license']);
        imagettftext($background, 20, 0, 760, 364, $black, $fontPath, $data['person_id']);
        $y2 = 412;
        $maxWidth2 = 160;
        $lines2 = $this->wrapText(18, 0, $fontPath, $data['car_engine_no'], $maxWidth2);
        foreach ($lines2 as $line2) {
            imagettftext($background, 18, 0, 755, $y2, $black, $fontPath, $line2);
            $y2 += 25;
        }
        if(strlen($data['car_type']) > 12){
            $y3 = 420;
            $maxWidth3 = 200;
            $lines3 = $this->wrapText(18, 0, $fontPath, $data['car_type'], $maxWidth3);
            foreach ($lines3 as $line3) {
                imagettftext($background, 16, 0, 1080, $y3, $black, $fontPath, $line3);
                $y3 += 20;
            }
        }else{
            imagettftext($background, 18, 0, 1074, 425, $black, $fontPath, $data['car_type']);
        }
        if(!empty($data['start_time']) || !empty($data['end_time'])){
            $datestr = $this->convertToChineseDate($data['start_time'], $data['end_time']);
            imagettftext($background, 20, 0, 320, 490, $black, $fontPath, $datestr);
        }
        imagettftext($background, 30, 0, 356, 550, $black, $fontPath, $data['rental_cost']);
        imagettftext($background, 20, 0, 760, 550, $black, $fontPath, $data['odometer']);
        imagettftext($background, 18, 0, 7080, 550, $black, $fontPath, $data['flight_no']);
        imagettftext($background, 24, 0, 1160, 920, $black, $fontPath, $data['driver_name']);

        // 派車單
        // imagettftext($background, 20, 0, 248, 1050, $black, $fontPath, $fullName);
        if(strlen($fullName) > 12){
            $y = 1035;
            $maxWidth = 210;
            $lines = $this->wrapText(20, 0, $fontPath, $fullName, $maxWidth);
            foreach ($lines as $line) {
                imagettftext($background, 18, 0, 245, $y, $black, $fontPath, $line);
                $y += 25;
            }
        }else{
            imagettftext($background, 20, 0, 248, 1050, $black, $fontPath, $fullName);
        }
        imagettftext($background, 20, 0, 940, 1050, $black, $fontPath, $data['people']);

        if(strlen($data['phone']) > 10){
            imagettftext($background, 12, 0, 1184, 1050, $black, $fontPath, $data['phone']);
        }else{
            imagettftext($background, 16, 0, 1187, 1050, $black, $fontPath, $data['phone']);
        }
        // imagettftext($background, 16, 0, 1187, 1050, $black, $fontPath, $data['phone']);
        imagettftext($background, 24, 0, 248, 1110, $black, $fontPath, $data['car_license']);
        imagettftext($background, 24, 0, 630, 1110, $black, $fontPath, $data['driver_name']);
        imagettftext($background, 16, 0, 898, 1110, $black, $fontPath, $data['person_id']);
        imagettftext($background, 16, 0, 1187, 1110, $black, $fontPath, $data['driver_mobile']);

        if(!empty($data['start_time']) || !empty($data['end_time'])){
            $datestr = $this->convertToChineseDate($data['start_time'], $data['end_time']);
            imagettftext($background, 22, 0, 257, 1177, $black, $fontPath, $datestr);
        }
        // if(!empty($data['end_time'])){
        //     imagettftext($background, 24, 0, 680, 1177, $black, $fontPath, $data['end_time']);
        // }

        imagettftext($background, 18, 0, 1190, 1235, $black, $fontPath, $data['flight_no']);
        imagettftext($background, 24, 0, 257, 1235, $black, $fontPath, $data['route']);
        imagettftext($background, 24, 0, 930, 1235, $black, $fontPath, $data['rental_cost']);

        imagettftext($background, 24, 0, 257, 1600, $black, $fontPath, $data['driver_name']);
        imagettftext($background, 24, 0, 700, 1600, $black, $fontPath, $data['driver_name']);
        // imagettftext($background, 24, 0, 1148, 1600, $black, $fontPath, $onlyName);
        // if(strlen($onlyName) > 12){
        //     imagettftext($background, 12, 0, 1140, 1600, $black, $fontPath, $onlyName);
        // }else{
        //     imagettftext($background, 24, 0, 1148, 1600, $black, $fontPath, $onlyName);
        // }
        // header('Content-Type: image/jpeg');
        $outputFileName = $data['dispatch_id'] . '.jpg';
        $outputFilePath = public_path('images/' . $outputFileName);

        if(!empty($data['signature'])){
            imagecopy($background, $resized_signature, 1010, 1650, 0, 0, $new_width,$new_height);
        }
        if(imagejpeg($background, $outputFilePath)){
            $result =  'images/' . $outputFileName;
        }else{
            $result = null;
        }
        imagedestroy($background);
        if(!empty($data['signature'])){
            imagedestroy($signature);
        }
        // imagedestroy($resized_signature);
        return $result;
    }
    public function n8ncreatedispatch(Request $request){
        $input = $request->all();
        $validator = Validator::make($input, [
            // 'dispatch_id' => 'required',
            // 'line_id' => 'required',
            // 'imgBase64' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $input['dispatch_id'] = 'DP_' . date("YmdHis") . Str::random(3);
        $driver = Driver::where('name', $input['driver'])->first();
        if(empty($driver)){
            return $this->sendError('Driver not found.', 'Driver not found.');
        }
        $car = Car::where('driver_id', $driver->id)->first();
        // $signatureData = $request->input('imgBase64');
        $input['customer_name'] = (empty($input['passenger_name']) ? '' : $input['passenger_name']);
        $input['customer_mobile'] = (empty($input['passanger_mobile']) ? '' : $input['passanger_mobile']);
        $input['customer_address'] = (empty($input['start_location']) ? '' : $input['start_location']);
        $input['driver_id'] = $driver->id;
        $input['vendor_id'] = $driver->vendor_id;
        $input['route'] = (empty($input['start_location']) ? '' : $input['start_location']).(empty($input['end_location']) ? '' : '  至  ' . $input['end_location']) ;
        $input['people'] = $input['number_of_people'];
        $mydate = $input['start_time'];
        $hours = substr($mydate, 0, 2);
        $minutes = substr($mydate, 2, 2);

        $myTimeStr = $input['start_time'];
        // $input['start_time'] = empty($input['start_date']) ? date("Y-m-d") : (string)$input['start_date'] . ' ' . $hours.':'.$minutes;
        // var_dump($input);die;
        $dispatch = Dispatch::create($input);
        $data = [];
        if($dispatch){
            $data = array(
                'dispatch_id' => $dispatch->dispatch_id,
                'name' => $dispatch->customer_name,
                'phone' => $dispatch->customer_mobile,
                'customer_address' => (empty($input['start_location']) ? '' : $input['start_location']),
                'customer_id' => (empty($input['customer_id']) ? '' : $input['customer_id']),
                'sex' => (empty($input['customer_sex']) ? 0 : $input['customer_sex']),
                'people' => (empty($dispatch->people) ? '': $dispatch->people),
                'driver_name' => $driver->name,
                'driver_mobile' => $driver->mobile,
                'car_license' => $car->car_license,
                'car_engine_no' => (empty($car->car_engine_no) ? '' : $car->car_engine_no),
                'car_type' => $car->car_type,
                'start_time' => $dispatch->start_time,
                'end_time' => $dispatch->end_time,
                'vendor_id' => $driver->vendor_id,
                'person_id' => $driver->person_id,
                'address' => (empty($driver->address) ? '' : $driver->address),
                'odometer' => (empty($dispatch->odometer) ? '' : $dispatch->odometer),
                'route' => (empty($dispatch->route) ? '': $dispatch->route),
                'rental_cost' => (empty($dispatch->rental_cost) ? '': $dispatch->rental_cost),
                'flight_no' => (empty($dispatch->flight_no) ? '': $dispatch->flight_no),
            );
            Log::info('派車單DATA '.json_encode($data));
            // var_dump($data);die;
            $this->makeImage($dispatch->dispatch_id, $data, $driver->vendor_id);
        }
        $dispatch['dispatch_id'] = $dispatch->dispatch_id;
        $dispatch['image_path'] = $dispatch->dispatch_id. '.jpg';
        $dispatch->save();
        return $this->sendResponse($dispatch, 'Dispatch Created Successfully.');
    }
    function formatChineseDate($datetime) {
        if (empty($datetime)) {
            return '';
        }
        // 创建 DateTime 对象
        $date = new DateTime($datetime);
        // 获取公历年份
        $year = $date->format('Y');
        // 将公历年份转换为民国年份
        $rocYear = $year - 1911;
        // 获取月份和日期
        $month = $date->format('n'); // 'n' 获取没有前导零的月份
        $day = $date->format('j'); // 'j' 获取没有前导零的日期
        $hour = $date->format('G'); // 'G' 获取24小时制的小时，范围是 0-23
        $minute = $date->format('i'); // 'i' 获取分钟，范围是 00-59

        // 构建中文日期时间字符串
        return sprintf(
            "%d 年 %d 月 %d 日 %d 時 %d 分",
            $rocYear,
            $month,
            $day,
            $hour,
            $minute
        );
    }
    function convertToChineseDate($start_datetime, $end_datetime) {
        // 定义一个内部函数来处理日期格式化
        // 格式化开始和结束时间
        $startFormatted = $this->formatChineseDate($start_datetime);
        $endFormatted = $this->formatChineseDate($end_datetime);

        // 如果开始或结束时间为空，返回空白
        if (empty($startFormatted) || empty($endFormatted)) {
            return '';
        }

        // 构建最终的中文时间区间字符串
        return sprintf(
            "自民國 %s 起至 %s止",
            $startFormatted,
            $endFormatted
        );
    }

    public function myview(Request $request){

        $someVariable = "'%佳能%'";
        $data = DB::select("select customer_address, COUNT(id) AS COUNT, SUM(rental_cost) AS sum FROM dispatches WHERE customer_name LIKE '%佳能%' GROUP BY customer_address");
        foreach ($data as $key => $value) {
            $data[$key]->aa = Dispatch::where('customer_address', $value->customer_address)
                                    ->where('customer_name', 'like', '%佳能%')
                                    ->get();
        }
        $data[0]->all = Dispatch::where('customer_name', 'like', '%佳能%')
                                ->get();
        return view('myview', compact('data'));
    }
    public function myMonthSignatureView(Request $request){
        // 将 Carbon 的语言环境设置为中文
        \Carbon\Carbon::setLocale('zh');
        // 司機ID
        // 944 孫思程
        // 948 黃凱華
        // 957 狄偉漢
        // 946 陳煥奇
        // get sample
        // "id": 0,
        // "start": "2024-11-20",
        // "end": "2024-12-19"
        $data_array = [['id' => 944, 'name' => '孫思程'],
                        ['id' => 948, 'name' => '黃凱華'],
                        ['id' => 957, 'name' => '狄偉漢'],
                        ['id' => 946, 'name' => '陳煥奇'],];
        $input = $request->all();
        $output = $data_array[$input['id']];
        // var_dump($output['id']);die;
        $startDate = $input['start'].' 00:00:00';
        $endDate = $input['end'].' 23:59:59';
        // dd(['aa'=>$output['id'], 'bb'=>$startDate, 'cc'=>$endDate]);
        $dispatch = Dispatch::select('id', 'start_time', 'customer_name', 'customer_mobile', 'rental_cost', 'signature_file')
                        ->whereIn('driver_id', [$output['id']])
                        ->where('customer_name', 'like', '%佳能%')
                        ->where('rental_cost', 400)
                        ->where('signature_file', '!=', null)
                        ->where('regular', 1)
                        ->where('status', 0)
                        ->whereBetween('start_time', [$startDate, $endDate])
                        ->orderBy('start_time', 'asc')
                        ->distinct()
                        ->get();
                        // dd($dispatch);
        $dispatch->driver_name = $output['name'];
        // 初始化行计数器

        $pdf = Pdf::loadView('myview2', compact('dispatch'))->set_option('isFontSubsettingEnabled', true)->setPaper('a4', 'portrait');
        // 保存 PDF 到本地
        $fileName = '佳能固定車_'. $output['name'] . '_' . date('Ymd_His') . '_' . uniqid() . '.pdf';
        $filePath = storage_path('app/public/pdf_outputs/' . $fileName);
        $pdf->save($filePath);
        echo '輸出成功! 檔名 >> '.$filePath;
    }
    public function myMonthSignatureListView(Request $request){
        \Carbon\Carbon::setLocale('zh');
        $customerName = '佳能';
        $data = Dispatch::select('id', 'start_time', 'customer_name', 'rental_cost', 'signature_file')
                        ->where('customer_name', 'like', '%'.$customerName.'%')
                        ->where('regular', 0)
                        ->where('status', 0)
                        // ->whereNotIn('driver_id', [944, 948, 957, 946,971])
                        ->whereBetween('start_time', ['2024-11-21 00:00:00', '2024-12-20 23:59:59'])
                        ->orderBy('start_time', 'asc')
                        ->distinct()
                        ->get();
        $pdf = Pdf::loadView('myrouterview', compact('data'))->set_option('isFontSubsettingEnabled', true)->setPaper('a4', 'portrait');
        // 保存 PDF 到本地
        $fileName = 'MM_12_' . date('Ymd_His') . '.pdf';
        $filePath = storage_path('app/public/pdf_outputs/' . $fileName);
        $pdf->save($filePath);
    }
    public function mkepaper(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'dispatch_id' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $selectData =Dispatch::where('dispatch_id', '=', $input['dispatch_id'])->get()->first();
        $selectDriverData =Driver::where('id', $selectData->driver_id)->first();
        $selectCarData =Car::where('driver_id', '=', $selectData->driver_id)->first();
        $mydatetime = $selectData['start_time'];
        $mydatetimeArray = explode(" ", $mydatetime);
        $data = [
            'dispatch_id' => $selectData['dispatch_id'],
            'customer_name' => $selectData['customer_name'],
            'customer_mobile' => $selectData['customer_mobile'],
            'customer_department' => $selectData['customer_address'],
            'driver_name' => $selectDriverData->name,
            'driver_mobile' => $selectDriverData->name,
            'car_license' => $selectCarData->car_license,
            'driver_id' => $selectDriverData->id,
            'route' => (empty($selectData['route'])) ? '-' : $selectData['route'],
            'route_start' => (empty($selectData['route'])) ? '-' : $selectData['route'],
            'rental_cost' => (empty($selectData['rental_cost'])) ? '-' : $selectData['rental_cost'],
            'odometer' => 0,
            'signature' => (empty($selectData['signature_file'])) ? '' : storage_path('app/public/' . $selectData['signature_file']),
            'start_date' => $mydatetimeArray[0],
            'start_time' => ($mydatetimeArray[1] == '06:00:00') ? '日 DAY  06:00~23:00' : '夜 NIGHT  23:00~06:00',
        ];
        $makeImageResult = $this->makeYatanImage($data);
        if($makeImageResult=='error'){
            return  $this->sendError('message', 'Signature File failed.');
        }
        Dispatch::where('dispatch_id', $selectData['dispatch_id'])
            ->update([
                'image_path' => $makeImageResult,
            ]);
        return  $this->sendResponse('message', 'File uploaded successfully.');
    }
    public function makeMenuPublicImage(Request $request)
    {
        // 手動更新 V1 版本派車單， 只要給ID
        // Log::info(print_r($data));
        $input = $request->all();
        $validator = Validator::make($input, [
            'did' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $selectData =Dispatch::where('id', '=', $input['did'])->get()->first();
        $selectDriverData =Driver::where('id', $selectData->driver_id)->first();
        $selectCarData =Car::where('driver_id', '=', $selectData->driver_id)->first();
        $mydatetime = $selectData['start_time'];
        $vendor_id = $selectData->vendor_id;
        $mydatetimeArray = explode(" ", $mydatetime);
        // var_dump($selectData);die;
        $data = [
            'dispatch_id' => $selectData['dispatch_id'],
            'name' => $selectData['customer_name'],
            'phone' => $selectData['customer_mobile'],
            'customer_address' => (empty($selectData['customer_address']) ? '' : $selectData['customer_address']),
            'sex' => $selectData['customer_sex'],
            'people' => (empty($selectData['people']) ? '': $selectData['people']),
            'driver_name' => $selectDriverData->name,
            'driver_mobile' => $selectDriverData->mobile,
            'car_license' => $selectCarData->car_license,
            'car_engine_no' => $selectCarData->car_engine_no,
            'car_type' => $selectCarData->car_type,
            'start_time' => $selectData->start_time,
            'end_time' => $selectData->end_time,
            'vendor_id' => $selectDriverData->vendor_id,
            'person_id' => $selectDriverData->person_id,
            'address' => (empty($selectDriverData->address) ? '' : $selectDriverData->address),
            'odometer' => (empty($selectData['odometer']) ? '' : $selectData['odometer']),
            'route' => (empty($selectData['route']) ? '': $selectData['route']),
            'rental_cost' => (empty($selectData['rental_cost']) ? '': $selectData['rental_cost']),
            'flight_no' => (empty($selectData['flight_no']) ? '': $selectData['flight_no']),
            'signature' => '',
        ];

        $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_'.$vendor_id.'.jpg'));
        if(!empty($data['signature'])){
            $signature = imagecreatefrompng($data['signature']);
            $signature_width = imagesx($signature);
            $signature_height = imagesy($signature);
            $new_width = $signature_width / 5;
            $new_height = $signature_height / 5;
            $resized_signature = imagecreatetruecolor($new_width, $new_height);
            imagealphablending($resized_signature, false);
            imagesavealpha($resized_signature, true);
            imagecopyresampled(
                $resized_signature,  // 目标图像资源
                $signature,          // 源图像资源
                0, 0,                // 目标图像的 x, y 坐标
                0, 0,                // 源图像的 x, y 坐标
                $new_width, $new_height,  // 目标图像的宽度和高度
                $signature_width, $signature_height  // 源图像的宽度和高度
            );
        }
        $fontPath = public_path('fonts/jf-openhuninn-2.0.ttf');
        $black = imagecolorallocate($background, 0, 0, 0);
        $fullName = $data['name'];
        $onlyName = $data['name'];
        if(strlen($fullName) > 3){
        }else{
            if($data['sex']==0){
                $fullName = $data['name'].' 小姐/女士';
            }else{
                $fullName = $data['name'].' 先生';
            }
        }
        // Log::info('fullName '.strlen($fullName));
        if(strlen($fullName) > 12){
            $y = 170;
            $maxWidth = 220;
            $lines = $this->wrapText(20, 0, $fontPath, $fullName, $maxWidth);
            foreach ($lines as $line) {
                imagettftext($background, 18, 0, 330, $y, $black, $fontPath, $line);
                $y += 25;
            }
        }else{
            imagettftext($background, 24, 0, 340, 182, $black, $fontPath, $fullName);
        }
        // imagettftext($background, 26, 0, 670, 182, $black, $fontPath, $data['phone']);
        if(strlen($data['phone']) > 10){
            imagettftext($background, 20, 0, 660, 182, $black, $fontPath, $data['phone']);
        }else{
            imagettftext($background, 26, 0, 670, 182, $black, $fontPath, $data['phone']);
        }
        // imagettftext($background, 20, 0, 500, 220, $black, $fontPath, $data['dispatch_id']);
        imagettftext($background, 20, 0, 330, 242, $black, $fontPath, $data['customer_address']);
        // imagettftext($background, 18, 0, 1070, 242, $black, $fontPath, $data['customer_id']);
        imagettftext($background, 18, 0, 960, 128, $black, $fontPath, $data['dispatch_id']);
        // 自駕司機
        imagettftext($background, 24, 0, 470, 364, $black, $fontPath, $data['driver_name']);
        $y = 350;
        $maxWidth = 200;
        $lines = $this->wrapText(20, 0, $fontPath, $data['address'], $maxWidth);
        foreach ($lines as $line) {
            imagettftext($background, 20, 0, 1070, $y, $black, $fontPath, $line);
            $y += 25;
        }
        // imagettftext($background, 24, 0, 980, 360, $black, $fontPath, $data['address']);
        imagettftext($background, 22, 0, 470, 425, $black, $fontPath, $data['car_license']);
        imagettftext($background, 20, 0, 760, 364, $black, $fontPath, $data['person_id']);
        $y2 = 412;
        $maxWidth2 = 160;
        $lines2 = $this->wrapText(18, 0, $fontPath, $data['car_engine_no'], $maxWidth2);
        foreach ($lines2 as $line2) {
            imagettftext($background, 18, 0, 755, $y2, $black, $fontPath, $line2);
            $y2 += 25;
        }
        if(strlen($data['car_type']) > 12){
            $y3 = 420;
            $maxWidth3 = 200;
            $lines3 = $this->wrapText(18, 0, $fontPath, $data['car_type'], $maxWidth3);
            foreach ($lines3 as $line3) {
                imagettftext($background, 16, 0, 1080, $y3, $black, $fontPath, $line3);
                $y3 += 20;
            }
        }else{
            imagettftext($background, 18, 0, 1074, 425, $black, $fontPath, $data['car_type']);
        }
        if(!empty($data['start_time']) || !empty($data['end_time'])){
            $datestr = $this->convertToChineseDate($data['start_time'], $data['end_time']);
            imagettftext($background, 20, 0, 320, 490, $black, $fontPath, $datestr);
        }
        imagettftext($background, 30, 0, 356, 550, $black, $fontPath, $data['rental_cost']);
        imagettftext($background, 20, 0, 760, 550, $black, $fontPath, $data['odometer']);
        imagettftext($background, 18, 0, 7080, 550, $black, $fontPath, $data['flight_no']);
        imagettftext($background, 24, 0, 1160, 920, $black, $fontPath, $data['driver_name']);

        // 派車單
        // imagettftext($background, 20, 0, 248, 1050, $black, $fontPath, $fullName);
        if(strlen($fullName) > 12){
            $y = 1035;
            $maxWidth = 210;
            $lines = $this->wrapText(20, 0, $fontPath, $fullName, $maxWidth);
            foreach ($lines as $line) {
                imagettftext($background, 18, 0, 245, $y, $black, $fontPath, $line);
                $y += 25;
            }
        }else{
            imagettftext($background, 20, 0, 248, 1050, $black, $fontPath, $fullName);
        }
        imagettftext($background, 20, 0, 940, 1050, $black, $fontPath, $data['people']);

        if(strlen($data['phone']) > 10){
            imagettftext($background, 12, 0, 1184, 1050, $black, $fontPath, $data['phone']);
        }else{
            imagettftext($background, 16, 0, 1187, 1050, $black, $fontPath, $data['phone']);
        }
        // imagettftext($background, 16, 0, 1187, 1050, $black, $fontPath, $data['phone']);
        imagettftext($background, 24, 0, 248, 1110, $black, $fontPath, $data['car_license']);
        imagettftext($background, 24, 0, 630, 1110, $black, $fontPath, $data['driver_name']);
        imagettftext($background, 16, 0, 898, 1110, $black, $fontPath, $data['person_id']);
        imagettftext($background, 16, 0, 1187, 1110, $black, $fontPath, $data['driver_mobile']);

        if(!empty($data['start_time']) || !empty($data['end_time'])){
            $datestr = $this->convertToChineseDate($data['start_time'], $data['end_time']);
            imagettftext($background, 22, 0, 257, 1177, $black, $fontPath, $datestr);
        }
        // if(!empty($data['end_time'])){
        //     imagettftext($background, 24, 0, 680, 1177, $black, $fontPath, $data['end_time']);
        // }

        imagettftext($background, 18, 0, 1190, 1235, $black, $fontPath, $data['flight_no']);
        imagettftext($background, 24, 0, 257, 1235, $black, $fontPath, $data['route']);
        imagettftext($background, 24, 0, 930, 1235, $black, $fontPath, $data['rental_cost']);

        imagettftext($background, 24, 0, 257, 1600, $black, $fontPath, $data['driver_name']);
        imagettftext($background, 24, 0, 700, 1600, $black, $fontPath, $data['driver_name']);
        // imagettftext($background, 24, 0, 1148, 1600, $black, $fontPath, $onlyName);
        // if(strlen($onlyName) > 12){
        //     imagettftext($background, 12, 0, 1140, 1600, $black, $fontPath, $onlyName);
        // }else{
        //     imagettftext($background, 24, 0, 1148, 1600, $black, $fontPath, $onlyName);
        // }
        // header('Content-Type: image/jpeg');
        $outputFileName = $data['dispatch_id'] . '.jpg';
        $outputFilePath = public_path('images/' . $outputFileName);

        if(!empty($data['signature'])){
            imagecopy($background, $resized_signature, 1010, 1650, 0, 0, $new_width,$new_height);
        }
        if(imagejpeg($background, $outputFilePath)){
            $result =  'images/' . $outputFileName;
        }else{
            $result = null;
        }
        imagedestroy($background);
        if(!empty($data['signature'])){
            imagedestroy($signature);
        }
        // imagedestroy($resized_signature);
        return $result;
    }
}
