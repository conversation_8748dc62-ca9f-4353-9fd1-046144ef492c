<?php

namespace App\Http\Controllers\api;

use App\Models\Vendor;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class VendorController extends Controller
{
    public function index(Request $request)
    {
        $vendors = Vendor::where('status', 1)->get(['id','title']);
        foreach ($vendors as $key => $value) {
            $vendors[$key]['id'] = $value->id;
            $vendors[$key]['value'] = $value->title;
            $vendors[$key]['text'] = $value->title;
            unset($vendors[$key]['title']);
            // unset($vendors[$key]['id']);
        }

        return $this->sendResponse($vendors, 'Get Vendors Successfully.');
    }
}
