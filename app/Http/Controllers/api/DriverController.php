<?php

namespace App\Http\Controllers\api;

use App\Traits\CreateDispatch;
use Carbon\Carbon;
use App\Models\Car;
use App\Models\Driver;
use App\Models\Vendor;
use App\Models\Dispatch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Validator;

class DriverController extends Controller
{
    use CreateDispatch;
    public function store(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required|max:255',
            'nickname' => 'required',
            'name' => 'required',
            'sex' => 'required',
            'mobile' => 'required',
            'join_code' => 'required'
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $vendor = Vendor::where('join_code', '=', strtoupper($input['join_code']))->first();
        if(!$vendor){
            return $this->sendError('Validation Error.', '邀請碼錯誤');
        }
        Log::info('邀請碼 '.$input['join_code']);
        Log::info('註冊司機 '.json_encode($input));
        $driver = Driver::where('line_id', '=', $input['line_id'])->first();
        // Log::info('司機ID '.json_encode($input));
        if($driver) {
            // 已有資料，須更新
            Log::info('司機ID(更新) '.$driver->id);
            if($vendor){
                Log::info('VendorID '.$vendor->id);
                Driver::where('line_id', '=', $input['line_id'])
                    ->update([
                        'nickname' => $input['nickname'],
                        'name' => $input['name'],
                        'sex' => $input['sex'],
                        'mobile' => $input['mobile'],
                        'is_active' => 0,
                        'person_id' => $input['person_id'],
                        'address' => $input['address'],
                        'dealer_name' => $input['dealer_name'],
                        'vendor_id' => $vendor->id
                    ]);
            }else{
                return $this->sendError('DB Find Error.', $input['join_code']);
            }
        }else{
            // $vendor = Vendor::where('join_code', '=', $input['join_code'])->first();
            Log::info('新增司機 '.json_encode($input));
            if($vendor){
                Log::info('新增司機 ');
                $input['is_active'] = 0;
                $input['vendor_id'] = $vendor->id;
                $driver = Driver::create($input);
            }else{
                Log::info('新增司機錯誤 ');
                return $this->sendError('DB Find Error.', strtoupper($input['join_code']));
            }
            Log::info('司機ID(新增) '.$driver->id);
        }
        $car = Car::where('driver_id', '=', $driver->id)->first();
        if($car){
            Log::info('車輛已存在(編輯) '.$car->id);
            // 已有資料，須更新
            $cars = Car::where('driver_id', '=', $driver->id)->update([
                // 'driver_id' => $driver->id,
                'car_license' => $input['car']['car_license'],
                'car_type' => $input['car']['car_type'],
                'car_engine_no' => $input['car']['car_engine_no'],
            ]);
            return $this->sendResponse($car, 'Driver update Successfully.');
        }else{
            $cars = Car::create([
                'driver_id' => $driver->id,
                'car_license' => $input['car']['car_license'],
                'car_type' => $input['car']['car_type'],
                'car_engine_no' => $input['car']['car_engine_no'],
            ]);
            Log::info('車輛資料(新增) '.$cars->id);
        }
        return $this->sendResponse($cars, 'Driver Created Successfully.');
    }

    public function person(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required',

        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        Log::info('取得司機資料 '.json_encode($input));
        $driver = Driver::where('line_id', '=', $input['line_id'])->first();
        $car = Car::whereBelongsTo($driver)->get();
        $driver->car = $car;
        return $this->sendResponse($driver, '成功取得資料.');
    }

    public function history(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        Log::info('取得派車單資料 '.json_encode($input));
        $driver = Driver::where('line_id', '=', $input['line_id'])->first();
        $dispatch = Dispatch::where('driver_id', '=', $driver->id)->orderByDesc('id')->take(30)->get();
        // $dispatch['count'] = $dispatch->count();
        return $this->sendResponse($dispatch, '成功取得資料.');
    }
    public function historyv2(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required',
            'search' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        Log::info('取得派車單資料 '.json_encode($input));
        $driver = Driver::where('line_id', '=', $input['line_id'])->first();
        if($input['search'] == 'today'){
            $dispatch = Dispatch::where('driver_id', '=', $driver->id)
                            ->whereDate('start_time', '=', Carbon::now()->toDateString())
                            ->orderBy('start_time', 'asc')->get();
        }else{
            $dispatch = Dispatch::where('driver_id', '=', $driver->id)
                            ->orderByDesc('start_time')->take(20)->get();
        }

        // $dispatch['count'] = $dispatch->count();
        return $this->sendResponse($dispatch, '成功取得資料.');
    }
    public function historyv2admin(Request $request)
    {
        // 雅潭 admin
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required',
            'search' => 'required',
            'customer' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // Log::info('取得派車單資料 '.json_encode($input));
        $driver = Driver::where('line_id', '=', $input['line_id'])->first();
        if($driver){
            if($input['search'] == 'today'){
                if($input['customer'] == '全部'){
                    $dispatch = Dispatch::where('vendor_id', '=', $driver->vendor_id)
                                ->where('status', 0)
                                ->whereDate('start_time', '=', Carbon::now()->toDateString())
                                ->orderBy('start_time', 'desc')->get();
                }else{
                    $dispatch = Dispatch::where('vendor_id', '=', $driver->vendor_id)
                                ->where('status', 0)
                                ->where('customer_name', 'like', '%'.$input['customer'].'%')
                                ->whereDate('start_time', '=', Carbon::now()->toDateString())
                                ->orderBy('start_time', 'desc')->get();
                }
            }else{
                if($input['customer'] == '全部'){
                    $dispatch = Dispatch::where('vendor_id', '=', $driver->vendor_id)
                                ->where('status', 0)
                                ->orderByDesc('start_time')->take(100)->orderBy('start_time', 'desc')->get();
                }else{
                    $dispatch = Dispatch::where('vendor_id', '=', $driver->vendor_id)
                                ->where('status', 0)
                                ->where('customer_name', 'like', '%'.$input['customer'].'%')
                                // ->whereDate('start_time', '=', Carbon::now()->toDateString())
                                ->orderBy('start_time', 'desc')->get();
                }
            }
        }else{
            $dispatch = [];
            return $this->sendResponse($dispatch, '司機資料不正確，查無資料.');
        }
        return $this->sendResponse($dispatch, '成功取得資料.');
    }
    public function updatedata(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required',
            'dispatch_id' => 'required',
            'customer_name' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $dispatch = Dispatch::where('dispatch_id', '=', $input['dispatch_id'])->first();
        if($dispatch){
            $dispatch->customer_name = $input['customer_name'];
            $dispatch->customer_mobile = $input['customer_mobile'];
            $dispatch->start_time = $input['start_time'];
            $dispatch->rental_cost = $input['rental_cost'];
            $dispatch->route = $input['route'];
            // $dispatch->status = $input['status'];
            $dispatch->regular = $input['regular'];
            $dispatch->save();
            $selectDriverData =Driver::where('id', $dispatch->driver_id)->first();
            $selectCarData =Car::where('driver_id', '=', $dispatch->driver_id)->first();
            $mydatetime = $dispatch['start_time'];
            $mydatetimeArray = explode(" ", $mydatetime);

            $data = [
                'dispatch_id' => $dispatch['dispatch_id'],
                'customer_name' => $dispatch['customer_name'],
                'customer_mobile' => $dispatch['customer_mobile'],
                'customer_department' => $dispatch['customer_address'],
                'customer_id' => $dispatch['customer_id'],
                'driver_name' => $selectDriverData->name,
                'driver_mobile' => $selectDriverData->name,
                'car_license' => $selectCarData->car_license,
                'driver_id' => $selectDriverData->id,
                'route' => (empty($dispatch['route'])) ? '-' : $dispatch['route'],
                'route_start' => (empty($dispatch['route'])) ? '-' : $dispatch['route'],
                'rental_cost' => (empty($dispatch['rental_cost'])) ? '-' : $dispatch['rental_cost'],
                'odometer' => 0,
                'signature' => $dispatch['signature'],
                'start_date' => $mydatetimeArray[0],
                'start_time' => ($mydatetimeArray[1] == '06:00:00') ? '日 DAY  06:00~23:00' : '夜 NIGHT  23:00~06:00',
            ];
            $makeImageResult = $this->makeYatanImage($data);
            if($makeImageResult=='error'){
                return  $this->sendError('message', 'Signature File failed.');
            }
            Dispatch::where('dispatch_id', $dispatch['dispatch_id'])
                ->update([
                    'image_path' => $makeImageResult,
                ]);
            // return  $this->sendResponse('message', 'File uploaded successfully.');
            return $this->sendResponse($dispatch, '成功更新資料.');
        }
    }
    public function deldata(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required',
            'id' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $dispatch = Dispatch::where('id', '=', $input['id'])->first();
        if($dispatch){
            $dispatch->status = 1;
            $dispatch->save();
            return $this->sendResponse($dispatch, '成功更新資料.');
        }
    }
    public function edit(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required',
        ]);
        if($validator->fails()){
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $driver = Driver::where('line_id', '=', $input['line_id'])->first();
        $car = Car::whereBelongsTo($driver)->get();
        $driver->car = $car;
        return $this->sendResponse($driver, '成功取得資料.');
    }

    public function getdata()
    {
        // 获取最近7天的日期
        $dates = collect();
        for ($i = 0; $i < 7; $i++) {
            $dates->push(Carbon::now()->subDays($i)->format('Y-m-d'));
        }
        $registrations = Driver::select(DB::raw('DATE(created_at) as date'), DB::raw('count(*) as count'))
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        // 准备图表数据
        $data = $dates->map(function ($date) use ($registrations) {
            return $registrations->get($date)->count ?? 0;
        });
        return [
            'labels' => $dates->toArray(),
            'datasets' => [
                [
                    'label' => 'Registrations',
                    'data' => $data->toArray(),
                    'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'borderWidth' => 1,
                ],
            ],
        ];
    }
    public function makeYatanImage($data)
    {
        // 台中雅潭派車單
        $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_7.jpg'));
        if(!empty($data['signature'])){
            $signature = imagecreatefrompng($data['signature']);
            $signature_width = imagesx($signature);
            $signature_height = imagesy($signature);
            $new_width = $signature_width / 2;
            $new_height = $signature_height / 2;
            $resized_signature = imagecreatetruecolor($new_width, $new_height);
            imagealphablending($resized_signature, false);
            imagesavealpha($resized_signature, true);
            imagecopyresampled(
                $resized_signature,  // 目标图像资源
                $signature,          // 源图像资源
                0, 0,                // 目标图像的 x, y 坐标
                0, 0,                // 源图像的 x, y 坐标
                $new_width, $new_height,  // 目标图像的宽度和高度
                $signature_width, $signature_height  // 源图像的宽度和高度
            );
        }

        $fontPath = public_path('fonts/jf-openhuninn-2.0.ttf');
        $black = imagecolorallocate($background, 0, 0, 0);
        $dispatch_id_dest_x = 1500; // 合并时在背景图上的x坐标
        $dispatch_id_dest_y = 140; // 合并时在背景图上的y坐标
        $customer_name_dest_x = 1325; // 合并时在背景图上的x坐标
        $customer_name_dest_y = 410; // 合并时在背景图上的y坐标
        $customer_department_x = 1325; // 合并时在背景图上的x坐标
        $customer_department_y = 573; // 合并时在背景图上的y坐标
        $start_date_dest_x = 539; // 合并时在背景图上的x坐标
        $start_date_dest_y = 260; // 合并时在背景图上的y坐标
        $start_time_dest_x = 355; // 合并时在背景图上的y坐标
        $start_time_dest_y = 355; // 合并时在背景图上的y坐标
        $rental_cost_dest_x = 760; // 合并时在背景图上的y坐标
        $rental_cost_dest_y = 410; // 合并时在背景图上的y坐标
        $driver_name_dest_x = 780; // 合并时在背景图上的y坐标
        $driver_name_dest_y = 760; // 合并时在背景图上的y坐标
        $car_license_dest_x = 500; // 合并时在背景图上的y坐标
        $car_license_dest_y = 760; // 合并时在背景图上的y坐标
        $start_time_dest_x = 1330; // 合并时在背景图上的y坐标
        $start_time_dest_y = 260; // 合并时在背景图上的y坐标
        $route_start_dest_x = 530; // 合并时在背景图上的y坐标
        $route_start_dest_y = 550; // 合并时在背景图上的y坐标
        // $route_end_dest_x = 800; // 合并时在背景图上的y坐标
        // $route_end_dest_y = 550; // 合并时在背景图上的y坐标
        imagettftext($background, 24, 0, $dispatch_id_dest_x, $dispatch_id_dest_y, $black, $fontPath, $data['dispatch_id']);
        imagettftext($background, 32, 0, $customer_name_dest_x, $customer_name_dest_y, $black, $fontPath, $data['customer_name']);
        imagettftext($background, 40, 0, $customer_department_x, $customer_department_y, $black, $fontPath, $data['customer_department']);
        imagettftext($background, 40, 0, $start_date_dest_x, $start_date_dest_y, $black, $fontPath, $data['start_date']);
        imagettftext($background, 36, 0, $rental_cost_dest_x, $rental_cost_dest_y, $black, $fontPath, $data['rental_cost']);
        imagettftext($background, 40, 0, $driver_name_dest_x, $driver_name_dest_y, $black, $fontPath, $data['driver_name']);
        imagettftext($background, 40, 0, $car_license_dest_x, $car_license_dest_y, $black, $fontPath, $data['car_license']);
        imagettftext($background, 30, 0, $start_time_dest_x, $start_time_dest_y, $black, $fontPath, $data['start_time']);
        if(strlen($data['route']) > 36){
            $y = $route_start_dest_y-30;
            $maxWidth = 400;
            $lines = $this->wrapText(20, 0, $fontPath, $data['route'], $maxWidth);
            foreach ($lines as $line) {
                imagettftext($background, 24, 0, $route_start_dest_x, $y, $black, $fontPath, $line);
                $y += 40;
            }
        }else{
            imagettftext($background, 30, 0, $route_start_dest_x, $route_start_dest_y, $black, $fontPath, $data['route']);
        }
        // imagettftext($background, 30, 0, $route_end_dest_x, $route_end_dest_y, $black, $fontPath, $data['route_end']);
        $outputFileName = $data['dispatch_id'] . '.jpg';
        $outputFilePath = public_path('images/' . $outputFileName);
        if(!empty($data['signature'])){
            imagecopy($background, $resized_signature, 1325, 633, 0, 0, $new_width,$new_height);
        }
        if(imagejpeg($background, $outputFilePath)){
            $result =  'images/' . $outputFileName;
        }else{
            $result = 'error';
        }
        imagedestroy($background);
        if(!empty($data['signature'])){
            imagedestroy($signature);
        }
        // imagedestroy($resized_signature);
        return $result;
    }
    private function wrapText($fontSize, $angle, $fontPath, $text, $maxWidth)
    {
        $lines = [];
        $line = "";
        for ($i = 0; $i < mb_strlen($text); $i++) {
            $char = mb_substr($text, $i, 1);
            $testString = $line . $char;
            $size = imagettfbbox($fontSize, $angle, $fontPath, $testString);
            if ($size[2] > $maxWidth && !empty($line)) {
                $lines[] = $line;
                $line = $char;
            }else{
                $line = $testString;
            }
        }
        $lines[] = $line;
        return $lines;
    }
}
