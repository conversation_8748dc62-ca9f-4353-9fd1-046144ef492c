<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Mail\BookingMail;
use App\Models\ADispatch;
use App\Models\ADriver;
use App\Models\BlankDispatch;
use App\Models\BlankDriver;
use App\Models\BlankPoint;
use App\Models\BlankTopup;
use App\Models\City;
use App\Models\Dispatch;
use App\Models\Driver;
use App\Models\Position;
use App\Models\Price;
use App\Models\Questionnaire;  // 新增 Questionnaire 模型
use App\Models\Quotation;
use App\Models\Reserve;
use App\Models\User;
use App\Notifications\BlankTopupNotification;
use App\Traits\CreateYatanDispatch;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Exception;

class ReserveController extends Controller
{
    use CreateYatanDispatch;

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            // 'line_id' => 'required|max:255',
            'customer_name' => 'required',
            'customer_mobile' => 'required',
            'reserve_date' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $input['reserve_id'] = 'RS_' . date('YmdHis') . Str::random(3);
        // $driver = Driver::where('line_id', $input['line_id'])->first();
        $input['boarding_address'] = $input['airport'];
        // if($driver) {
        // 已有資料，須更新
        $reserve = Reserve::create([
            'reserve_id' => $input['reserve_id'],
            'customer_name' => $input['customer_name'],
            'customer_mobile' => $input['customer_mobile'],
            'type' => $input['type'],
            'reserve_date' => $input['reserve_date'],
            'reserve_time' => $input['reserve_time'],
            'boarding_address' => $input['boarding_address'],
            'drop_off_address' => $input['drop_off_address'],
            'flight_no' => $input['flight_no'],
            'no_of_passengers' => $input['no_of_passengers'],
            'no_of_bags' => $input['no_of_bags'],
            'vehicle_type' => $input['vehicle_type'],
            'updated_at' => now(),
        ]);
        return $this->sendResponse($reserve, '成功取得資料.');
        // } else {
        //     // 未有資料，須新增
        //     // $driver = Driver::create([
        //     //     'line_id' => $input['line_id'],
        //     //     'customer_name' => $input['customer_name'],
        //     //     'customer_mobile' => $input['customer_mobile'],
        //     //     'reserve_date' => $input['reserve_date'],
        //     //     'created_at' => now(),
        //     //     'updated_at' => now(),
        //     // ]);
        //     // return $this->sendResponse($driver, '成功取得資料.');
        // }
    }

    /**
     * Display the specified resource.
     */
    public function show(Reserve $reserve)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Reserve $reserve)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Reserve $reserve)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Reserve $reserve)
    {
        //
    }

    public function textadddispatch(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'data_str' => 'required|max:500',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // $token = $request->bearerToken(); // 獲取 Bearer Token
        // if (!$token) {
        //     return response()->json(['error' => 'Token not provided.'], 401);
        // }
        $inputString = $input['data_str'];
        $rs = $this->parseText($inputString);
        // var_dump($rs);die;
        if ($rs['status'] == 'success') {
            $driver = Driver::with('car')
                ->where('name', 'like', $rs['data']['driver'])
                ->where('vendor_id', 7)
                ->first();
            // var_dump($driver->car->car_license);die;
            if ($driver) {
                $payload = [
                    'customer_name' => $rs['data']['company'],
                    'customer_address' => !empty($rs['data']['department']) ? $rs['data']['department'] : '',
                    'start_time' => empty($rs['data']['start_time']) ? Carbon::now()->format('Y-m-d') . ' 06:00:00' : $rs['data']['start_time'] . ' 06:00:00',
                    'rental_cost' => $rs['data']['rental_cost'],
                    'driver_id' => $driver->id,
                    'car_license' => $driver->car->car_license,
                    'route' => $rs['data']['router'],
                    'vendor_id' => 7,
                    'carno2' => !empty($rs['data']['carno2']) ? $rs['data']['carno2'] : null,
                ];
                $createdRs = static::createYatanDispatchRecord($payload);
                if ($createdRs) {
                    $rs['data']['dispatch_id'] = $createdRs;
                    // Dispatch::where('dispatch_id', $createdRs)->first();
                    $prinrDispatchData = [
                        'dispatch_id' => $createdRs,
                        'customer_name' => $rs['data']['company'],
                        'customer_department' => !empty($rs['data']['department']) ? $rs['data']['department'] : '',
                        'start_date' => empty($rs['data']['start_time']) ? Carbon::now()->format('Y-m-d') : $rs['data']['start_time'],
                        'start_time' => '日 DAY  06:00~23:00',
                        'rental_cost' => $rs['data']['rental_cost'],
                        'driver_name' => $driver->name,
                        'car_license' => $driver->car->car_license,
                        'route' => $rs['data']['router'],
                    ];
                    $printDispatchPath = static::createDispatchNoSignature($prinrDispatchData);
                    Dispatch::where('dispatch_id', $createdRs)
                        ->update([
                            'image_path' => $printDispatchPath,
                        ]);
                    return $this->sendResponse($rs['data'], '成功建立資料.');
                } else {
                    return $this->sendResponse($rs, '建立資料失敗');
                }
            } else {
                return $this->sendResponse($rs, '司機不存在');
            }
        } else {
            return $this->sendResponse($rs, $rs['message']);
        }
    }

    public function parseText($text)
    {
        // 定義必填與選填欄位
        $fields = [
            'company' => '/公司名稱\s*[:：]\s*([^\s,]+)(?=[\s,]|$)/u',
            'department' => '/部門\s*[:：]\s*([^\s,]+)(?=[\s,]|$)/u',
            'router' => '/路程\s*[:：]\s*([^\s,]+(?:→[^\s,]+)*)(?=[\s,]|$)/u',
            'driver' => '/司機\s*[:：]\s*([^\s,]+)(?=[\s,]|$)/u',
            'carNumber' => '/車號\s*[:：]\s*([^\s,]+)(?=[\s,]|$)/u',
            'carModel' => '/車型\s*[:：]\s*([^\s,]+)(?=[\s,]|$)/u',
            'rental_cost' => '/車資\s*[:：]\s*(\d+)(?=[\s,]|$)/u',
            'start_time' => '/日期\s*[:：]\s*([^\s,]+)(?=[\s,]|$)/u',
        ];

        // 必填欄位列表
        $requiredFields = ['company', 'router', 'driver', 'rental_cost'];

        // 初始化結果
        $result = [];

        // 提取每個欄位資料
        foreach ($fields as $key => $pattern) {
            preg_match($pattern, $text, $matches);
            $result[$key] = trim($matches[1] ?? '');
        }

        // 檢查缺少的欄位
        $missingFields = array_filter($requiredFields, fn($field) => empty($result[$field]));

        // 回傳結果
        if (!empty($missingFields)) {
            return [
                'status' => 'error',
                'message' => '缺少必填欄位',
                'missing_fields' => $missingFields,
            ];
        }

        return [
            'status' => 'success',
            'data' => $result,
        ];
    }

    // web liff 取得詢價 時宜 liff
    public function getquote(Request $request)
    {
        $tmpPrice = 0;
        $tmpPriceArray = '';
        $bagStr = '';
        $otherStr = '';
        $message = '';
        $quotation = '';
        $lang = 'zh_TW';
        $messageTitle = '您的即時詢價資訊如下:';
        $input = $request->all();
        $validator = Validator::make($input, [
            'order_type' => 'required',
            'car_type' => 'required',
            'quo_type' => 'required',
            'line_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // Log::info("input->> -", $input);
        $lang = $input['lang'] ?? 'zh_TW';
        if ($lang == 'en-US') {
            $messageTitle = 'Your instant inquiry information is as follows:';
            $messageCarType = 'Car Type:';
            $messageOrderType = 'Order Type:';
            $messageRoute = 'Route:';
            $messageNumberOfPeople = 'Number of people:';
            $messageOtherService = 'Other Service:';
            $messageBags = 'Bags and Children Seat: ';
        } else {
            $messageTitle = '您的即時詢價資訊如下:';
            $messageCarType = '車型:';
            $messageOrderType = '類別:';
            $messageRoute = '往來地點:';
            $messageNumberOfPeople = '乘車人數:';
            $messageOtherService = '其他服務:';
            $messageBags = '行李與兒童座椅: ';
        }
        $tmpPriceArray = $this->getAddPrice('q', $input);
        if ($input['order_type'] == '0' || $input['order_type'] == '1') {
            // var_dump($tmpPrice);die;
            Log::info('input->> -', $tmpPriceArray);

            if (!empty($tmpPriceArray['price'])) {
                $bagServices = empty($tmpPriceArray['bagStr']) ? '' : PHP_EOL . $messageBags . $tmpPriceArray['bagStr'] . PHP_EOL;
                $OtherServices = empty($tmpPriceArray['otherStr']) ? '' : $messageOtherService . $tmpPriceArray['otherStr'] . PHP_EOL;
                $message = $messageTitle . PHP_EOL
                    . $messageOrderType . $this->getOrderTypeChinese($lang, $input['order_type']) . PHP_EOL
                    . $messageCarType . $this->getCarTypeChinese($lang, $input['car_type'], $input['vendor_id']) . PHP_EOL
                    . $messageRoute . $this->getLocationChinese($input['airport']) . '-' . $tmpPriceArray['price']['to_city_name'] . ' ' . $this->getLocationChinese($input['to_district_id']) . PHP_EOL
                    . $messageNumberOfPeople . (!empty($input['num_of_people']) ? $input['num_of_people'] : 0) . '人'
                    . $bagServices
                    . $OtherServices;
                $quotation = Quotation::create([
                    'vendor_id' => $input['vendor_id'],
                    'user_line_id' => $input['line_id'],
                    'order_type' => $input['order_type'],
                    'car_type' => $input['car_type'],
                    'tour_type' => $input['tour_car'],
                    'location_from_id' => $tmpPriceArray['price']['from_district_id'],
                    'location_from_name' => $tmpPriceArray['price']['from_district_name'],
                    'location_area_id' => $tmpPriceArray['price']['to_area_id'],
                    'location_area_name' => $tmpPriceArray['price']['to_area_name'],
                    'location_city_id' => $tmpPriceArray['price']['to_city_id'],
                    'location_city_name' => $tmpPriceArray['price']['to_city_name'],
                    'location_district_id' => $tmpPriceArray['price']['to_district_id'],
                    'location_district_name' => $tmpPriceArray['price']['to_district_name'],
                    'num_of_people' => $input['num_of_people'],
                    'num_of_bags' => $input['num_of_bags'],
                    'child_seat' => $input['child_seat'],
                    'booster_pad' => $input['booster_pad'],
                    'other_service' => $input['other_service'],
                    'total' => (int) $tmpPriceArray['tmpTotal'],
                    'created_at' => now(),
                    'note' => $input['note'],
                ]);
                // $quotation['quotation_id'] = $quotation->id;
            } else {
                $quotation = [
                    'total' => 0
                ];
                $requestedCity = $this->getLocationChinese($input['to_city_id'] ?? null, 'city');  // Pass 'city' flag
                $requestedDistrict = $this->getLocationChinese($input['to_district_id'] ?? null);
                $requestedCarType = $this->getCarTypeChinese($lang, $input['car_type'] ?? null, $input['vendor_id']);
                $requestedAirport = $this->getdAirportChinese($lang, $input['airport'] ?? null);
                $details = " (查詢條件: {$requestedCity} {$requestedDistrict}, 車型: {$requestedCarType}, 車型: {$requestedAirport})";
                if ($lang == 'en-US') {
                    $message = 'Currently no quotation for this area!' . PHP_EOL . 'We will contact you later!';
                } else {
                    $message = '目前查無該地區報價或符合車型!' . $details . PHP_EOL . '後續將由專人與您聯繫!';
                }
                Log::warning('Quotation not found:', ['input' => $input, 'details' => $details]);  // Log the failure
            }
            return $this->sendResponse($quotation, $message);
        } else if ($input['order_type'] == '2') {
            // 包車旅遊
            // $tmpPriceArray = $this->getAddPrice('q',$input);
            $quotation = Quotation::create([
                'vendor_id' => $input['vendor_id'],
                'user_line_id' => $input['line_id'],
                'order_type' => $input['order_type'],
                'car_type' => $input['car_type'],
                'tour_type' => $input['tour_car'],
                // 'location_area_id' => $result->to_area_id,
                'location_city_id' => $input['to_city_id'],
                'location_district_id' => $input['to_district_id'],
                // 'location_area_name' => $result->to_area_name,
                'location_city_name' => $this->getLocationChinese($input['to_city_id'], 'city'),
                'location_district_name' => $this->getLocationChinese($input['to_district_id']),
                'total' => (int) $tmpPriceArray['tmpTotal'],
                'created_at' => now(),
                'note' => $input['note'],
            ]);
            $message = $tmpPriceArray['bagStr'];
            return $this->sendResponse($quotation, $message);
            // $result = 500
        } else if ($input['order_type'] == '3') {
            // 機場來回
            Log::info('機場來回->> -', $tmpPriceArray);
            if (!empty($tmpPriceArray['price'])) {
                $bagServices = empty($tmpPriceArray['bagStr']) ? '' : PHP_EOL . $messageBags . $tmpPriceArray['bagStr'] . PHP_EOL;
                $OtherServices = empty($tmpPriceArray['otherStr']) ? '' : $messageOtherService . $tmpPriceArray['otherStr'] . PHP_EOL;
                $message = $messageTitle . PHP_EOL
                    . $messageOrderType . $this->getOrderTypeChinese($lang, $input['order_type']) . PHP_EOL
                    . $messageCarType . $this->getCarTypeChinese($lang, $input['car_type'], $input['vendor_id']) . PHP_EOL
                    . $messageRoute . $this->getLocationChinese($input['airport']) . '-' . $tmpPriceArray['price']['to_city_name'] . ' ' . $this->getLocationChinese($input['to_district_id']) . PHP_EOL
                    . $messageNumberOfPeople . (!empty($input['num_of_people']) ? $input['num_of_people'] : 0) . '人'
                    . $bagServices
                    . $OtherServices;
                $quotation = Quotation::create([
                    'vendor_id' => $input['vendor_id'],
                    'user_line_id' => $input['line_id'],
                    'order_type' => $input['order_type'],
                    'car_type' => $input['car_type'],
                    'tour_type' => $input['tour_car'],
                    'location_from_id' => $tmpPriceArray['price']['from_district_id'],
                    'location_from_name' => $tmpPriceArray['price']['from_district_name'],
                    'location_area_id' => $tmpPriceArray['price']['to_area_id'],
                    'location_area_name' => $tmpPriceArray['price']['to_area_name'],
                    'location_city_id' => $tmpPriceArray['price']['to_city_id'],
                    'location_city_name' => $tmpPriceArray['price']['to_city_name'],
                    'location_district_id' => $tmpPriceArray['price']['to_district_id'],
                    'location_district_name' => $tmpPriceArray['price']['to_district_name'],
                    'num_of_people' => $input['num_of_people'],
                    'num_of_bags' => $input['num_of_bags'],
                    'child_seat' => $input['child_seat'],
                    'booster_pad' => $input['booster_pad'],
                    'other_service' => $input['other_service'],
                    'total' => (int) $tmpPriceArray['tmpTotal'],
                    'created_at' => now(),
                    'note' => $input['note'],
                    'nickname' => $input['nickname'] ?? '',
                ]);
                // $quotation['quotation_id'] = $quotation->id;
            } else {
                $quotation = [
                    'total' => 0
                ];
                if ($lang == 'en-US') {
                    $message = 'Currently no quotation for this area!' . PHP_EOL . 'We will contact you later!';
                } else {
                    $message = '目前查無該地區報價或符合車型!' . PHP_EOL . '後續將由專人與您聯繫!';
                }
            }
            return $this->sendResponse($quotation, $message);
        }
        // $result = static::getQuote($data);
        // $data['price'] = $result->price ?? 0;
    }

    public function getquotev2(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'order_type' => 'required|in:0,1,2',
            'car_type' => 'required',
            'vendor_id' => 'required',
            'airport' => 'required_if:order_type,0,1',
            'to_city_id' => 'required_if:order_type,0,1',
            'to_district_id' => 'required_if:order_type,0,1',
            'num_of_people' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }

        $orderTypeText = ['0' => '接機', '1' => '送機', '2' => '包車'][$input['order_type']];
        $carTypeText = [
            '5' => '舒適小車',
            '51' => '五人座休旅車',
            '9' => '舒適大車',
            '91' => '尊爵大車'
        ][$input['car_type']] ?? '其他';

        $price = 0;
        $basePrice = null;
        $addItems = [];
        $addDesc = '';
        $note = $input['note'] ?? '';
        // 彈性加價規則（可依 vendor_id 調整）
        $vendorAddRules = [
            'default' => ['pickup' => 200, 'sign' => 100, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '2' => ['pickup' => 200, 'sign' => 232, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '7' => ['pickup' => 200, 'sign' => 231, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '10' => ['pickup' => 100, 'sign' => 100, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '12' => ['pickup' => 100, 'sign' => 100, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '13' => ['pickup' => 200, 'sign' => 100, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '17' => ['pickup' => 200, 'sign' => 200, 'stop' => 100, 'night_small' => 100, 'night_large' => 100],
        ];
        $rules = $vendorAddRules[$input['vendor_id']] ?? $vendorAddRules['default'];

        if ($input['order_type'] == '0' || $input['order_type'] == '1') {
            $tmpOrderType = ($input['order_type'] == '1') ? '0' : $input['order_type'];
            $basePrice = Price::where('car_type', $input['car_type'])
                ->where('order_type', $tmpOrderType)
                ->where('vendor_id', $input['vendor_id'])
                ->where('from_district_id', $input['airport'])
                ->where('to_city_id', $input['to_city_id'])
                ->where('to_district_id', $input['to_district_id'])
                ->first();

            if (!$basePrice) {
                return $this->sendError('查無此路線報價!將由客服人員與您接洽，報價!!', []);
            }
            $price = $basePrice->price;

            // 接機加價
            if ($input['order_type'] == '0') {
                $price += $rules['pickup'];
                $addItems[] = "接機加價+{$rules['pickup']}";
            }

            // 兒童座椅
            $childSeat = intval($input['child_seat'] ?? 0);
            if ($childSeat > 0) {
                $price += $childSeat * 200;
                $addItems[] = "兒童座椅+200x{$childSeat}";
            }

            // 增高墊
            $boosterPad = intval($input['booster_pad'] ?? 0);
            if ($boosterPad > 0) {
                $price += $boosterPad * 100;
                $addItems[] = "增高墊+100x{$boosterPad}";
            }

            // 其他服務
            if (!empty($input['other_service']) && is_array($input['other_service'])) {
                foreach ($input['other_service'] as $service) {
                    if ($service == '1') {
                        $price += $rules['sign'];
                        $addItems[] = "舉牌+{$rules['sign']}";
                    } elseif ($service == '2') {
                        $price += $rules['stop'];
                        $addItems[] = "停靠點+{$rules['stop']}";
                    } elseif ($service == '3') {
                        if (in_array($input['car_type'], ['5', '51'])) {
                            $price += $rules['night_small'];
                            $addItems[] = "夜間加成+{$rules['night_small']}";
                        } else {
                            $price += $rules['night_large'];
                            $addItems[] = "夜間加成+{$rules['night_large']}";
                        }
                    }
                }
            }

            $addDesc = $addItems ? '（含' . implode('、', $addItems) . '）' : '';

            // 地點名稱查詢
            $from = $this->getLocationChinese($input['airport']);
            $to = $this->getLocationChinese($input['to_city_id'], 'city') . ' ' . $this->getLocationChinese($input['to_district_id']);

            $result = "類別：{$orderTypeText}\n"
                . "上車地點：{$from}\n"
                . "下車地點：{$to}\n"
                . "搭乘人數：{$input['num_of_people']} 人\n"
                . '行李數：' . (!empty($input['num_of_bags']) ? $input['num_of_bags'] : '0') . " 件\n"
                . "車輛類型：{$carTypeText}\n"
                . '報價：NT$ ' . number_format($price) . "{$addDesc}";

            // 寫入報價紀錄
            $quotation = Quotation::create([
                'vendor_id' => $input['vendor_id'],
                'user_line_id' => $input['line_id'] ?? '',
                'order_type' => $input['order_type'],
                'car_type' => $input['car_type'],
                'tour_type' => $input['tour_car'] ?? '',
                'location_from_id' => $input['airport'],
                'location_from_name' => $from,
                'location_city_id' => $input['to_city_id'],
                'location_city_name' => $this->getLocationChinese($input['to_city_id'], 'city'),
                'location_district_id' => $input['to_district_id'],
                'location_district_name' => $this->getLocationChinese($input['to_district_id']),
                'num_of_people' => $input['num_of_people'],
                'num_of_bags' => $input['num_of_bags'] ?? 0,
                'child_seat' => $input['child_seat'] ?? 0,
                'booster_pad' => $input['booster_pad'] ?? 0,
                'other_service' => $input['other_service'] ?? [],
                'total' => (int) $price,
                'created_at' => now(),
                'note' => $input['note'] ?? '',
                'nickname' => $input['nickname'] ?? '',
                'status' => 0,
            ]);

            return $this->sendResponse(['quote' => $result, 'quotation_id' => $quotation->id, 'total' => number_format($price)], '報價成功');
        }

        if ($input['order_type'] == '2') {
            // 包車報價邏輯可依需求補上
            if($input['vendor_id'] == 17){
                $result = '包車報價請洽客服';
            }else{
                // 泓運包車
                $carTypeText = [
                    '5' => '五人小車',
                    '51' => '五人座休旅車',
                    '9' => '九人座大車',
                ][$input['car_type']] ?? '其他';
                $result = "類別：包車\n"
                . "乘車大名: {$input['passenger_name']}\n"
                . "連絡電話: {$input['passenger_mobile']}\n"
                . "預約日期時間: {$input['appointment_date']}\n"
                . "詳細地址：{$input['passenger_address']}\n"
                . "航班資訊： {$input['flightno']}\n"
                . "搭乘人數：{$input['num_of_people']} 人\n"
                . '行李數：' . (!empty($input['num_of_bags']) ? $input['num_of_bags'] : '0') . " 件\n"
                . "車輛類型：{$carTypeText}\n"
                . "付款方式：{$input['pay_type']}\n"
                . "包車方式： ({$input['charterType']}=='hour') ? '小時' : '天數' \n"
                . "包車時間： {$input['charterHour']} ({$input['charterType']}=='hour') ? '小時' : '天'\n"
                . "備註： {$note}\n"
                . "\n已收到你的預約，後續將由專人與你聯繫規劃與報價!!";
                // $result = '已收到你的預約，將由專人與你聯繫與報價!!';
            }
            return $this->sendResponse(['quote' => '包車報價將由客服人員與你聯繫，進行報價!'], $result);
        }
    }

    public function getquobyorder(Request $request)
    {
        /**
         * LINE liff 取得報價
         * firbase app
         */
        $input = $request->all();
        $validator = Validator::make($input, [
            'quotation_id' => 'required',
            'line_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $result = Quotation::where('id', $input['quotation_id'])
            ->where('user_line_id', $input['line_id'])
            ->first();
        if ($result) {
            return $this->sendResponse($result, '成功取得資料.');
        } else {
            $data['price'] = 0;
            return $this->sendResponse($data, '目前查無該訂單!');
        }
    }

    public function carorder(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            // 'quotation_id' => 'required',
            'line_id' => 'required',
            'passenger_name' => 'required',
            'passenger_mobile' => 'required',
            'passenger_address' => 'required',
            'appointment_date' => 'required',
            'flightno' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $lang = $input['lang'] ?? 'zh_TW';
        if ($lang == 'en-US') {
            $messageTitle = 'Your instant inquiry information is as follows:';
            $messageCarType = 'Car Type:';
            $messageOrderType = 'Order Type:';
            $messageRoute = 'Route:';
            $messageNumberOfPeople = 'Number of people:';
            $messageOtherService = 'Other Service:';
            $messageBags = 'Bags and Children Seat: ';
        } else {
            $messageTitle = '您的即時詢價資訊如下:';
            $messageCarType = '車型:';
            $messageOrderType = '類別:';
            $messageRoute = '往來地點:';
            $messageNumberOfPeople = '乘車人數:';
            $messageOtherService = '其他服務:';
            $messageBags = '行李與兒童座椅: ';
        }
        $tmpPriceArray = $this->getAddPrice('o', $input);
        Log::info('tmpPriceArray->> -', $tmpPriceArray);
        if ($input['quotation_id'] == 0 || $input['quotation_id'] == null) {
            if ($input['order_type'] == 0 || $input['order_type'] == 1) {
                $bagServices = empty($tmpPriceArray['bagStr']) ? '' : PHP_EOL . $messageBags . $tmpPriceArray['bagStr'] . PHP_EOL;
                $OtherServices = empty($tmpPriceArray['otherStr']) ? '' : $messageOtherService . $tmpPriceArray['otherStr'] . PHP_EOL;
                $PassengerServices = empty($tmpPriceArray['passengerStr']) ? PHP_EOL : PHP_EOL . $tmpPriceArray['passengerStr'];
                $message = $PassengerServices
                    . $messageOrderType . $this->getOrderTypeChinese($lang, $input['order_type']) . PHP_EOL
                    . $messageCarType . $this->getCarTypeChinese($lang, $input['car_type'], $input['vendor_id']) . PHP_EOL
                    // '往來地點:'.$this->getLocationChinese($input['airport']).'-'.$tmpPriceArray['price']['to_city_name'].' '.$this->getLocationChinese($input['to_district_id']).PHP_EOL.
                    . $messageNumberOfPeople . (!empty($input['num_of_people']) ? $input['num_of_people'] : 0) . '人'
                    . $bagServices
                    . $OtherServices;
                $quotation = Quotation::create([
                    'status' => 1,
                    'vendor_id' => $input['vendor_id'],
                    'passenger_name' => $input['passenger_name'],
                    'passenger_mobile' => $input['passenger_mobile'],
                    'passenger_address' => $input['passenger_address'],
                    'pay_type' => $input['pay_type'],
                    'note' => $input['note'] ?? '',
                    'user_line_id' => $input['line_id'],
                    'order_type' => $input['order_type'],
                    'car_type' => $input['car_type'],
                    'tour_type' => $input['tour_car'],
                    'location_from_id' => $input['airport'],
                    'location_from_name' => $tmpPriceArray['price']['from_district_name'],
                    'location_area_id' => $tmpPriceArray['price']['to_area_id'],
                    'location_area_name' => $tmpPriceArray['price']['to_area_name'],
                    'location_city_id' => $tmpPriceArray['price']['to_city_id'],
                    'location_city_name' => $tmpPriceArray['price']['to_city_name'],
                    'location_district_id' => $tmpPriceArray['price']['to_district_id'],
                    'location_district_name' => $tmpPriceArray['price']['to_district_name'],
                    'num_of_people' => $input['num_of_people'],
                    'num_of_bags' => $input['num_of_bags'],
                    'child_seat' => $input['child_seat'],
                    'booster_pad' => $input['booster_pad'],
                    'other_service' => $input['other_service'],
                    'appointment_date' => $input['appointment_date'],
                    'flightno' => $input['flightno'],
                    'nickname' => $input['nickname'] ?? null,
                    'updated_at' => now(),
                    'total' => (int) $tmpPriceArray['tmpTotal']
                ]);
                return $this->sendResponse($quotation, $message);
            } else {
                // 包車旅遊
                $tmpPriceArray = $this->getAddPrice('q', $input);
                $quotation = Quotation::create([
                    'vendor_id' => $input['vendor_id'],
                    'user_line_id' => $input['line_id'],
                    'order_type' => $input['order_type'],
                    'car_type' => $input['car_type'],
                    'tour_type' => $input['tour_car'],
                    // 'location_area_id' => $result->to_area_id,
                    'location_city_id' => $input['to_city_id'],
                    'location_district_id' => $input['to_district_id'],
                    // 'location_area_name' => $result->to_area_name,
                    'location_city_name' => $this->getLocationChinese($input['to_city_id'], 'city'),
                    'location_district_name' => $this->getLocationChinese($input['to_district_id']),
                    'total' => (int) $tmpPriceArray['tmpTotal'],
                    'created_at' => now(),
                    'note' => $input['note'] ?? '',
                    'appointment_date' => $input['appointment_date'],
                    'flightno' => $input['flightno'],
                ]);
                $message = $tmpPriceArray['passengerStr'] . PHP_EOL . $tmpPriceArray['bagStr'];
                return $this->sendResponse($quotation, $message);
            }
        }
        // $input['from_district_id'] = empty($input['airport']) ? '' : $input['airport'];
        $quotation = Quotation::where('id', $input['quotation_id'])
            ->where('user_line_id', $input['line_id'])
            ->first();

        // var_dump($aa);die;
        if ($quotation) {
            $quotation->status = 1;
            $quotation->passenger_name = $input['passenger_name'];
            $quotation->passenger_mobile = $input['passenger_mobile'];
            $quotation->passenger_address = $input['passenger_address'];
            $quotation->pay_type = $input['pay_type'];
            // 處理備註欄位，加入額外資訊
            $note = $input['note'] ?? '';
            $additionalInfo = [];

            if (!empty($input['num_of_bags_airport'])) {
                $additionalInfo[] = '可登機行李數: ' . $input['num_of_bags_airport'];
            }

            if (!empty($input['trunk'])) {
                $additionalInfo[] = '胖胖箱數: ' . $input['trunk'];
            }

            if (!empty($input['child'])) {
                $additionalInfo[] = '兒童數: ' . $input['child'];
            }

            if (!empty($additionalInfo)) {
                $note .= ' ' . implode(', ', $additionalInfo);
            }

            // $quotation->note = $note;
            $quotation->appointment_date = $input['appointment_date'];
            $quotation->flightno = $input['flightno'];
            $quotation->user_line_id = $input['line_id'];
            $quotation->order_type = $input['order_type'];
            $quotation->car_type = $input['car_type'];
            $quotation->tour_type = $input['tour_car'];
            if ($input['order_type'] == 2) {
                $quotation->location_city_id = $input['to_city_id'];
                $quotation->location_city_name = $this->getLocationChinese($input['to_city_id'], 'city');
                $quotation->location_district_id = $input['to_district_id'];
                $quotation->location_district_name = $this->getLocationChinese($input['to_district_id']);
            } else {
                $quotation->location_from_id = $input['airport'];
                $quotation->location_from_name = $tmpPriceArray['price']['from_district_name'];
                $quotation->location_area_id = $tmpPriceArray['price']['to_area_id'];
                $quotation->location_area_name = $tmpPriceArray['price']['to_area_name'];
                $quotation->location_city_id = $tmpPriceArray['price']['to_city_id'];
                $quotation->location_city_name = $tmpPriceArray['price']['to_city_name'];
                $quotation->location_district_id = $tmpPriceArray['price']['to_district_id'];
                $quotation->location_district_name = $tmpPriceArray['price']['to_district_name'];
            }
            $quotation->num_of_people = $input['num_of_people'];
            $quotation->num_of_bags = $input['num_of_bags'];
            $quotation->child_seat = $input['child_seat'];
            $quotation->booster_pad = $input['booster_pad'];
            $quotation->other_service = $input['other_service'];
            $quotation->updated_at = now();
            $quotation->total = (int) $tmpPriceArray['tmpTotal'];
            $quotation->note = $input['note'] ?? '';
            $quotation->save();
            $bagServices = empty($tmpPriceArray['bagStr']) ? PHP_EOL : PHP_EOL . '行李與兒童座椅: ' . $tmpPriceArray['bagStr'] . PHP_EOL;
            $OtherServices = empty($tmpPriceArray['otherStr']) ? PHP_EOL : PHP_EOL . '其他服務: ' . $tmpPriceArray['otherStr'];
            $PassengerServices = empty($tmpPriceArray['passengerStr']) ? PHP_EOL : PHP_EOL . $tmpPriceArray['passengerStr'] . PHP_EOL;
            if ($input['order_type'] == 2) {
                if ($input['tour_car'] == 1) {
                    // 一日遊
                    $message = '您的預約資訊如下:' . PHP_EOL . $PassengerServices
                        . '類別:' . '包車旅遊一日遊' . PHP_EOL
                        . '車型:' . $this->getCarTypeChinese($lang, $input['car_type'], $input['vendor_id']) . PHP_EOL
                        . '上車地點:' . $this->getLocationChinese($input['to_city_id'], 'city') . '-' . $this->getLocationChinese($input['to_district_id']);
                } else {
                    $message = '您的預約資訊如下:' . PHP_EOL . $PassengerServices
                        . '類別:' . '包車旅遊半日遊' . PHP_EOL
                        . '車型:' . $this->getCarTypeChinese($lang, $input['car_type'], $input['vendor_id']) . PHP_EOL
                        . '上車地點:' . $this->getLocationChinese($input['to_city_id'], 'city') . '-' . $this->getLocationChinese($input['to_district_id']);
                }
                if (!empty($input['note'])) {
                    $message .= PHP_EOL . '備註:' . $input['note'];
                }
            } else {
                $message = $PassengerServices
                    . '類別:' . $this->getOrderTypeChinese($lang, $input['order_type']) . PHP_EOL
                    . '車型:' . $this->getCarTypeChinese($lang, $input['car_type'], $input['vendor_id']) . PHP_EOL
                    // '往來地點:'.$this->getLocationChinese($input['airport']).'-'.$tmpPriceArray['price']['to_city_name'].' '.$this->getLocationChinese($input['to_district_id']).PHP_EOL.
                    . '乘車人數:' . (!empty($input['num_of_people']) ? $input['num_of_people'] : 0) . '人'
                    . $bagServices
                    . $OtherServices;
                if (!empty($input['note'])) {
                    $message .= PHP_EOL . '備註:' . $input['note'];
                }
            }
            return $this->sendResponse($quotation, $message);
        } else {
            $data['total'] = 0;
            return $this->sendResponse($data, '目前查無該訂單!');
        }
    }

    public function carorderv2(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'order_type' => 'required',
            'car_type' => 'required',
            'vendor_id' => 'required',
            'airport' => 'required_if:order_type,0,1',
            'to_city_id' => 'required_if:order_type,0,1',
            'to_district_id' => 'required_if:order_type,0,1',
            'num_of_people' => 'required',
            'passenger_name' => 'required',
            'passenger_mobile' => 'required',
            'passenger_address' => 'required',
            'appointment_date' => 'required',
            'flightno' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }

        // 查詢價格（與 getquotev2 相同邏輯）
        $orderTypeText = ['0' => '接機', '1' => '送機', '2' => '包車'][$input['order_type']];
        $carTypeText = [
            '5' => '舒適小車',
            '51' => '五人座休旅車',
            '9' => '舒適大車',
            '91' => '尊爵大車'
        ][$input['car_type']] ?? '其他';
        $price = 0;
        $basePrice = null;
        $addItems = [];
        $addDesc = '';
        $note = $input['note'] ?? '';
        $vendorAddRules = [
            'default' => ['pickup' => 200, 'sign' => 100, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '2' => ['pickup' => 200, 'sign' => 232, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '7' => ['pickup' => 200, 'sign' => 231, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '10' => ['pickup' => 100, 'sign' => 100, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '12' => ['pickup' => 100, 'sign' => 100, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '13' => ['pickup' => 200, 'sign' => 100, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '17' => ['pickup' => 200, 'sign' => 100, 'stop' => 100, 'night_small' => 100, 'night_large' => 100],
        ];
        $rules = $vendorAddRules[$input['vendor_id']] ?? $vendorAddRules['default'];
        if ($input['order_type'] == '0' || $input['order_type'] == '1') {
            $tmpOrderType = ($input['order_type'] == '1') ? '0' : $input['order_type'];
            $basePrice = Price::where('car_type', $input['car_type'])
                ->where('order_type', $tmpOrderType)
                ->where('vendor_id', $input['vendor_id'])
                ->where('from_district_id', $input['airport'])
                ->where('to_city_id', $input['to_city_id'])
                ->where('to_district_id', $input['to_district_id'])
                ->first();
            if (!$basePrice) {
                return $this->sendError('查無此路線報價', []);
            }
            $price = $basePrice->price;
            if ($input['order_type'] == '0') {
                $price += $rules['pickup'];
                $addItems[] = "接機加價+{$rules['pickup']}";
            }
            $childSeat = intval($input['child_seat'] ?? 0);
            if ($childSeat > 0) {
                $price += $childSeat * 200;
                $addItems[] = "兒童座椅+200x{$childSeat}";
            }
            $boosterPad = intval($input['booster_pad'] ?? 0);
            if ($boosterPad > 0) {
                $price += $boosterPad * 100;
                $addItems[] = "增高墊+100x{$boosterPad}";
            }
            if (!empty($input['other_service']) && is_array($input['other_service'])) {
                foreach ($input['other_service'] as $service) {
                    if ($service == '1') {
                        $price += $rules['sign'];
                        $addItems[] = "舉牌+{$rules['sign']}";
                    } elseif ($service == '3') {
                        $price += $rules['stop'];
                        $addItems[] = "停靠點+{$rules['stop']}";
                    } elseif ($service == '2') {
                        if (in_array($input['car_type'], ['5', '51'])) {
                            $price += $rules['night_small'];
                            $addItems[] = "夜間加成+{$rules['night_small']}";
                        } else {
                            $price += $rules['night_large'];
                            $addItems[] = "夜間加成+{$rules['night_large']}";
                        }
                    }
                }
            }

            $additionalInfo = [];

            if (!empty($input['num_of_bags_airport'])) {
                $additionalInfo[] = '可登機行李數: ' . $input['num_of_bags_airport'];
            }

            if (!empty($input['trunk'])) {
                $additionalInfo[] = '胖胖箱數: ' . $input['trunk'];
            }

            if (!empty($input['child'])) {
                $additionalInfo[] = '兒童數: ' . $input['child'];
            }

            if (!empty($additionalInfo)) {
                $note .= PHP_EOL . implode(', ', $additionalInfo);
            }

            $addDesc = $addItems ? '（含' . implode('、', $addItems) . '）' : '';
            // $from = $this->getLocationChinese($input['airport']);
            // $to = $this->getLocationChinese($input['to_city_id'], 'city') . ' ' . $this->getLocationChinese($input['to_district_id']);
            if (isset($input['order_type']) && $input['order_type'] == '1') {
                $from = "接送地址: " . $this->getLocationChinese($input['to_city_id'], 'city') . ' ' . $this->getLocationChinese($input['to_district_id']);
                $to = "接送機場: " . $this->getLocationChinese($input['airport']);
            } else {
                $from = "接送機場: " . $this->getLocationChinese($input['airport']);
                $to = "接送地址: " . $this->getLocationChinese($input['to_city_id'], 'city') . ' ' . $this->getLocationChinese($input['to_district_id']);
            }
            if($input['vendor_id']==23 && ($carTypeText == '其他')){
                $result = '特殊車輛種類，將由客服人員與你聯繫，進行報價!!';
            } else {
                $result = "類別：{$orderTypeText}\n"
                    . "乘車大名: {$input['passenger_name']}\n"
                    . "連絡電話: {$input['passenger_mobile']}\n"
                    . "預約日期時間: {$input['appointment_date']}\n"
                    . "{$from}\n"
                    . "{$to}\n"
                    . "詳細地址：{$input['passenger_address']}\n"
                    . "航班資訊： {$input['flightno']}\n"
                    . "搭乘人數：{$input['num_of_people']} 人\n"
                    . '行李數：' . (!empty($input['num_of_bags']) ? $input['num_of_bags'] : '0') . " 件\n"
                    . "車輛類型：{$carTypeText}\n"
                    . "付款方式：{$input['pay_type']}\n"
                    . '報價：NT$ ' . number_format($price) . "{$addDesc}"
                    . "\n備註： {$note}";
            }
                
        } else {
            if($input['vendor_id'] == 17 || $input['vendor_id'] == 23){
                $result = '包車報價將由客服人員與你聯繫，進行報價!!';
            }else{
                // 泓運包車
                $carTypeText = [
                    '5' => '五人小車',
                    '51' => '五人座休旅車',
                    '9' => '九人座大車',
                ][$input['car_type']] ?? '其他';
                $result = "類別：包車\n"
                . "乘車大名: {$input['passenger_name']}\n"
                . "連絡電話: {$input['passenger_mobile']}\n"
                . "預約日期時間: {$input['appointment_date']}\n"
                . "詳細地址：{$input['passenger_address']}\n"
                . "航班資訊： {$input['flightno']}\n"
                . "搭乘人數：{$input['num_of_people']} 人\n"
                . '行李數：' . (!empty($input['num_of_bags']) ? $input['num_of_bags'] : '0') . " 件\n"
                . "車輛類型：{$carTypeText}\n"
                . "付款方式：{$input['pay_type']}\n"
                . "包車方式： ({$input['charterType']}=='hour') ? '小時' : '天數' \n"
                . "包車時間： {$input['charterHour']} ({$input['charterType']}=='hour') ? '小時' : '天'\n"
                . "備註： {$note}\n"
                . "\n已收到你的預約，後續將由專人與你聯繫規劃與報價!!";
                // $result = '已收到你的預約，將由專人與你聯繫與報價!!';
            }
        }
        // 建立或更新 Quotation
        $quotation = null;
        if (empty($input['quotation_id']) || $input['quotation_id'] == 0) {
            $quotation = new \App\Models\Quotation();
            $quotation->created_at = now();
        } else {
            $quotation = \App\Models\Quotation::find($input['quotation_id']);
            if (!$quotation) {
                return $this->sendError('查無此報價單', []);
            }
        }
        $quotation->status = 1;
        $quotation->vendor_id = $input['vendor_id'];
        $quotation->user_line_id = $input['line_id'] ?? '';
        $quotation->order_type = $input['order_type'];
        $quotation->car_type = $input['car_type'];
        $quotation->tour_type = $input['tour_car'] ?? '';
        // if (!isset($input['airport'])) {
        //     return $this->sendError('缺少機場信息', []);
        // }
        if ($input['order_type'] == 2) {
            $quotation->location_from_id = null;
            $quotation->location_from_name = null;
        } else {
            $quotation->location_from_id = $input['airport'];
            $quotation->location_from_name = $this->getLocationChinese($input['airport']);
        }
        $quotation->location_city_id = $input['to_city_id'];
        $quotation->location_city_name = $this->getLocationChinese($input['to_city_id'], 'city');
        $quotation->location_district_id = $input['to_district_id'];
        $quotation->location_district_name = $this->getLocationChinese($input['to_district_id']);
        $quotation->num_of_people = $input['num_of_people'];
        $quotation->num_of_bags = $input['num_of_bags'] ?? 0;
        $quotation->child_seat = $input['child_seat'] ?? 0;
        $quotation->booster_pad = $input['booster_pad'] ?? 0;
        $quotation->other_service = $input['other_service'] ?? [];
        $quotation->passenger_name = $input['passenger_name'];
        $quotation->passenger_mobile = $input['passenger_mobile'];
        $quotation->passenger_address = $input['passenger_address'];
        $quotation->pay_type = $input['pay_type'] ?? '';
        // $quotation->note = $input['note'] ?? '';
        $quotation->appointment_date = $input['appointment_date'];
        $quotation->flightno = $input['flightno'];
        $quotation->total = isset($price) ? (int) $price : 0;
        // 更新備註欄
        $quotation->note = $note;
        $quotation->nickname = $input['nickname'];
        $quotation->updated_at = now();
        $quotation->save();
        return $this->sendResponse([
            'quotation_id' => $quotation->id,
            'total' => number_format($price),
            'result' => $result,
            'quotation' => $quotation
        ], '預約成功');
    }

    public function getcitys(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'vendor_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // $user =
        $results = DB::table('rent_car_prices')
            ->select(
                'to_area_id',
                DB::raw('MAX(to_area_name) as to_area_name'),
                'to_city_id',
                DB::raw('MAX(to_city_name) as to_city_name'),
                'to_district_id',
                DB::raw('MAX(to_district_name) as to_district_name')
            )
            ->where('vendor_id', $input['vendor_id'])
            ->where('status', 0)
            ->groupBy('to_area_id', 'to_city_id', 'to_district_id')
            ->get();

        $toAreaIds = [];
        $toCityIds = [];
        $toDistrictIds = [];

        foreach ($results as $result) {
            $toAreaIds[$result->to_area_id] = $result->to_area_name;
            $toCityIds[$result->to_city_id] = $result->to_city_name;
            $toDistrictIds[$result->to_district_id] = $result->to_district_name;
        }

        $data = [
            'province_list' => $toAreaIds,
            'city_list' => $toCityIds,
            'county_list' => $toDistrictIds,
        ];
        return $this->sendResponse($data, '成功取得城市資料!');
    }

    public function getcitys2(Request $request)
    {
        // 帶入參數不同 vendor_id, car_type
        $input = $request->all();
        $validator = Validator::make($input, [
            'vendor_id' => 'required',
            'car_type' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // $user =
        $results = DB::table('rent_car_prices')
            ->select(
                'to_area_id',
                DB::raw('MAX(to_area_name) as to_area_name'),
                'to_city_id',
                DB::raw('MAX(to_city_name) as to_city_name'),
                'to_district_id',
                DB::raw('MAX(to_district_name) as to_district_name')
            )
            ->where('vendor_id', $input['vendor_id'])
            ->where('car_type', $input['car_type'])
            ->where('status', 0)
            ->groupBy('to_area_id', 'to_city_id', 'to_district_id')
            ->get();

        $toAreaIds = [];
        $toCityIds = [];
        $toDistrictIds = [];

        foreach ($results as $result) {
            $toAreaIds[$result->to_area_id] = $result->to_area_name;
            $toCityIds[$result->to_city_id] = $result->to_city_name;
            $toDistrictIds[$result->to_district_id] = $result->to_district_name;
        }

        $data = [
            'province_list' => $toAreaIds,
            'city_list' => $toCityIds,
            'county_list' => $toDistrictIds,
        ];
        return $this->sendResponse($data, '成功取得城市資料!');
    }
    public function getcitys21(Request $request)
    {
        // 帶入參數不同 vendor_id, car_type
        $input = $request->all();
        $validator = Validator::make($input, [
            'vendor_id' => 'required',
            'car_type' => 'required',
            'airport' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        // $user =
        $results = DB::table('rent_car_prices')
            ->select(
                'to_area_id',
                DB::raw('MAX(to_area_name) as to_area_name'),
                'to_city_id',
                DB::raw('MAX(to_city_name) as to_city_name'),
                'to_district_id',
                DB::raw('MAX(to_district_name) as to_district_name')
            )
            ->where('vendor_id', $input['vendor_id'])
            ->where('car_type', $input['car_type'])
            ->where('from_district_id', $input['airport'])
            ->where('status', 0)
            ->groupBy('to_area_id', 'to_city_id', 'to_district_id')
            ->get();

        $toAreaIds = [];
        $toCityIds = [];
        $toDistrictIds = [];

        foreach ($results as $result) {
            $toAreaIds[$result->to_area_id] = $result->to_area_name;
            $toCityIds[$result->to_city_id] = $result->to_city_name;
            $toDistrictIds[$result->to_district_id] = $result->to_district_name;
        }

        $data = [
            'province_list' => $toAreaIds,
            'city_list' => $toCityIds,
            'county_list' => $toDistrictIds,
        ];
        return $this->sendResponse($data, '成功取得城市資料!');
    }

    // 取得城市資料英文版本
    public function getcitys3(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'vendor_id' => 'required',
            'car_type' => 'required',
            'lang' => 'sometimes|in:zh-TW,en-US',  // <--- 新增 lang 驗證 (可選)
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }

        // 獲取語言參數，預設為中文
        $lang = $request->input('lang', 'zh-TW');

        // 根據語言選擇要查詢的名稱欄位
        $areaNameColumn = ($lang === 'en-US') ? 'c_area.city_en' : 'c_area.city';
        $cityNameColumn = ($lang === 'en-US') ? 'c_city.city_en' : 'c_city.city';
        $districtNameColumn = ($lang === 'en-US') ? 'c_district.city_en' : 'c_district.city';

        // 查詢 rent_car_prices 獲取有效的地區 ID
        $priceResults = DB::table('rent_car_prices')
            ->select('to_area_id', 'to_city_id', 'to_district_id')
            ->where('vendor_id', $input['vendor_id'])
            ->where('car_type', $input['car_type'])
            ->where('status', 0)
            ->distinct()  // 使用 distinct 獲取唯一的 ID 組合
            ->get();

        if ($priceResults->isEmpty()) {
            return $this->sendResponse([
                'province_list' => [],
                'city_list' => [],
                'county_list' => [],
            ], '成功取得城市資料!');
        }

        // 提取唯一的 ID
        $uniqueAreaIds = $priceResults->pluck('to_area_id')->filter()->unique()->values()->all();
        $uniqueCityIds = $priceResults->pluck('to_city_id')->filter()->unique()->values()->all();
        $uniqueDistrictIds = $priceResults->pluck('to_district_id')->filter()->unique()->values()->all();

        // 初始化名稱查找表
        $areaNames = [];
        $cityNames = [];
        $districtNames = [];

        // 查詢區域名稱 (Province)
        if (!empty($uniqueAreaIds)) {
            $areaNames = City::whereIn('area_code', $uniqueAreaIds)
                ->whereNull('city_code')
                ->whereNull('district_code')
                ->pluck(($lang === 'en-US' ? 'city_en' : 'city'), 'area_code')
                ->all();  // 使用 all() 轉換為普通陣列
        }

        // 查詢城市名稱 (City)
        if (!empty($uniqueCityIds)) {
            $cityNames = City::whereIn('city_code', $uniqueCityIds)
                ->whereNull('district_code')  // 確保是城市級別
                ->pluck(($lang === 'en-US' ? 'city_en' : 'city'), 'city_code')
                ->all();
        }

        // 查詢區域名稱 (County/District)
        if (!empty($uniqueDistrictIds)) {
            $districtNames = City::whereIn('district_code', $uniqueDistrictIds)
                ->pluck(($lang === 'en-US' ? 'city_en' : 'city'), 'district_code')
                ->all();
        }

        // 建立最終的回傳列表，確保只包含 rent_car_prices 中存在的 ID
        $finalAreaList = [];
        $finalCityList = [];
        $finalDistrictList = [];

        foreach ($priceResults as $priceResult) {
            if ($priceResult->to_area_id && isset($areaNames[$priceResult->to_area_id])) {
                $finalAreaList[$priceResult->to_area_id] = $areaNames[$priceResult->to_area_id];
            }
            if ($priceResult->to_city_id && isset($cityNames[$priceResult->to_city_id])) {
                $finalCityList[$priceResult->to_city_id] = $cityNames[$priceResult->to_city_id];
            }
            if ($priceResult->to_district_id && isset($districtNames[$priceResult->to_district_id])) {
                $finalDistrictList[$priceResult->to_district_id] = $districtNames[$priceResult->to_district_id];
            }
        }

        $data = [
            // 使用 array_filter 移除可能存在的 null 值 (如果英文名稱不存在)
            'province_list' => (object) array_filter($finalAreaList),  // 轉回 object 符合原格式
            'city_list' => (object) array_filter($finalCityList),
            'county_list' => (object) array_filter($finalDistrictList),
        ];

        return $this->sendResponse($data, '成功取得城市資料!');
    }

    public function getOrderTypeChinese($lang, $order_type)
    {
        if ($order_type == '0') {
            if ($lang == 'en') {
                return 'Pick up';
            } else {
                return '接機';
            }
        } else if ($order_type == '1') {
            if ($lang == 'en') {
                return 'Drop off';
            } else {
                return '送機';
            }
        } else if ($order_type == '2') {
            if ($lang == 'en') {
                return 'Charter Tour';
            } else {
                return '包車旅遊';
            }
        } else if ($order_type == '3') {
            if ($lang == 'en') {
                return 'Charter Tour';
            } else {
                return '機場接送來回';
            }
        }
    }

    public function getCarTypeChinese($lang, $car_type, $vendor_id)
    {
        if ($car_type == '5') {
            if ($lang == 'en') {
                return '5-seater Car';
            } else {
                if ($vendor_id == 17) {
                    return '舒適小車';
                }
                return '五人座車小客車';
            }
        } else if ($car_type == '51') {
            if ($lang == 'en') {
                return '5-SUV';
            } else {
                if ($vendor_id == 17) {
                    return '舒適小車';
                }
                return '五人座車休旅車';
            }
        } else if ($car_type == '9') {
            if ($lang == 'en') {
                return '9-seater';
            } else {
                if ($vendor_id == 17) {
                    return '舒適大車';
                }
                return '九人座車';
            }
        } else if ($car_type == '91') {
            if ($lang == 'en') {
                return '9-BENZ Vito';
            } else {
                if ($vendor_id == 17) {
                    return '尊爵大車';
                }
                return '賓士Vito九人座';
            }
        } else if ($car_type == '10') {
            if ($lang == 'en') {
                return 'Alphard';
            } else {
                return 'Alphard尊爵';
            }
        }
    }

    public function getdAirportChinese($lang, $airport)
    {
        if ($airport == '110900') {
            if ($lang == 'en') {
                return 'Taipei Songshan Airport';
            } else {
                return '松山機場';
            }
        } else if ($airport == '110903') {
            if ($lang == 'en') {
                return 'Taoyuan Airport';
            } else {
                return '桃園機場';
            }
        } else if ($airport == '120901') {
            if ($lang == 'en') {
                return 'Taichung Airport';
            } else {
                return '台中機場';
            }
        } else if ($airport == '130901') {
            if ($lang == 'en') {
                return 'Kaohsiung Airport';
            } else {
                return '高雄小港機場';
            }
        }
    }

    public function getLocationChinese($location_id, $city = null)
    {
        Log::info('FF Location ID CITY 123:: ' . $location_id . ' :: ' . $city);
        if ($location_id == '110903') {
            return '桃園機場(Taoyuan)';
        } else if ($location_id == '110900') {
            return '松山機場(Taipei)';
        } else if ($location_id == '120901') {
            return '台中機場(Taichung)';
        } else {
            if ($city == 'city') {
                Log::info('Location ID CITY:: ' . $location_id);
                return City::where('city_code', $location_id)->first()->city;
            } else {
                Log::info('Location ID:: ' . $location_id);
                return City::where('district_code', $location_id)->first()->city;
            }
        }
    }

    public function getAddPrice($type, $input)
    {
        $tmpTotal = 0;
        $tmpPrice = 0;
        $message = '';
        $otherStr = '';
        $bagStr = '';
        $passengerStr = '';
        $basePrice = [];
        // $input['location_from_id'] = $input['airport'];
        $tmpOrderType = ($input['order_type'] == '1' || $input['order_type'] == '3') ? '0' : $input['order_type'];
        Log::info('input->> 00  -', $input);
        if ($input['lang'] == 'en-US') {
            $message = 'The price is calculated as follows:';
            $messageUpLocation = 'Up Location: ';
            $messageTourOneDay = 'Tour one day: ';
            $messageTourHalfDay = 'Tour half day: ';
            $messageNote = 'Note:';
            $messageUpTime = 'Up Time: ';
            $messageArrivalAirport = 'Arrival Airport: ';
            $messageDown = 'Down: ';
            $messageUp = 'Up: ';
            $messageFlightTime = 'Flight Time: ';
            $messagetakeAirport = 'Take Airport: ';
            $messagePassengerName = 'Passenger Name: ';
            $messagePassengerMobile = 'Passenger Mobile: ';
            $messageLocation = 'Location:';
            $messageFlightNo = 'Flight Information:';
            $messagePayType = 'Pay Type:';
        } else {
            $message = '價格計算如下:';
            $messageUpLocation = '上車區域: ';
            $messageTourOneDay = '包車一日遊: ';
            $messageTourHalfDay = '包車一日遊: ';
            $messageNote = '備註:';
            $messageUpTime = '上車時間: ';
            $messageArrivalAirport = '抵達機場: ';
            $messageDown = '下車';
            $messageUp = '上車';
            $messageFlightTime = '班機時間: ';
            $messagetakeAirport = '接機機場: ';
            $messagePassengerName = '乘客姓名: ';
            $messagePassengerMobile = '乘客手機: ';
            $messageLocation = '地點:';
            $messageFlightNo = '航班資訊:';
            $messagePayType = '付款方式:';
        }
        if ($input['order_type'] == '0' || $input['order_type'] == '1' || $input['order_type'] == '3') {
            $basePrice = Price::where('car_type', $input['car_type'])
                ->where('order_type', $tmpOrderType)
                ->where('vendor_id', $input['vendor_id'])
                ->where('from_district_id', $input['airport'])
                ->where('to_city_id', $input['to_city_id'])
                ->where('to_district_id', $input['to_district_id']);

            // 方法1：使用 toSql() 輸出純SQL語句
            Log::info('SQL Query:', [
                'sql' => $basePrice->toSql(),
                'bindings' => $basePrice->getBindings(),
                'input' => $input
            ]);

            $basePrice = $basePrice->first();
            Log::info('AA Query:', [$basePrice]);
            if (!$basePrice) {
                return [
                    'price' => '',
                    'tmpTotal' => $tmpTotal,
                    'otherStr' => $otherStr,
                    'bagStr' => $bagStr,
                ];
            }

            if ($input['order_type'] == '0' || $input['order_type'] == '3') {
                // 接機加100

                if ($input['vendor_id'] == '10' || $input['vendor_id'] == '12') {
                    $tmpPrice = 100;
                } else if ($input['vendor_id'] == '7' || $input['vendor_id'] == '13') {
                    $tmpPrice = 200;
                }
            }
            if (!empty($input['child_seat']) && $input['child_seat'] > 0 && $input['child_seat'] < 3) {
                if ($input['vendor_id'] == '13' && $input['order_type'] == '3') {
                    $tmpPrice += ($input['child_seat'] * 100) * 2;
                } else if ($input['vendor_id'] == '7') {
                    $tmpPrice += $input['child_seat'] * 200;
                }else{
                    $tmpPrice += $input['child_seat'] * 100;
                }
            }
            // 增高墊 +100
            if (!empty($input['booster_pad']) && $input['booster_pad'] > 0 && $input['booster_pad'] < 3) {  // 修正：使用 booster_pad 判斷
                if ($input['vendor_id'] == '13' && $input['order_type'] == '3') {
                    $tmpPrice += ($input['booster_pad'] * 100) * 2;
                } else if ($input['vendor_id'] == '7') {
                    $tmpPrice += $input['booster_pad'] * 200;
                } else {
                    $tmpPrice += $input['booster_pad'] * 100;
                }
            }

            if (!empty($input['other_service'])) {
                $othrtPrice = 0;
                foreach ($input['other_service'] as $service) {
                    if ($service == '1') {
                        if ($input['vendor_id'] == '10' || $input['vendor_id'] == '12' || $input['vendor_id'] == '13') {
                            $othrtPrice += 100;
                            $otherStr .= '舉牌(+100),';
                        } else if ($input['vendor_id'] == '7') {
                            $othrtPrice += 200;
                            $otherStr .= '舉牌(+200),';
                        }
                    } elseif ($service == '3') {
                        $othrtPrice += 100;
                        $otherStr .= '停靠點(一個點+100),';
                    } elseif ($service == '2') {
                        // 夜間加成
                        // 先檢查 vendor_id=13 且 order_type=3 的特殊情況
                        if ($input['vendor_id'] == '13' && $input['order_type'] == '3') {
                            $othrtPrice += 200;
                            $otherStr .= '夜間加成(+200),';
                        } else {
                            // 一般夜間加成邏輯
                            if ($input['car_type'] == '5' || $input['car_type'] == '51') {  // 小車
                                $othrtPrice += 100;
                                $otherStr .= '夜間加成(+100),';
                            } else {  // 大車
                                $othrtPrice += 200;
                                $otherStr .= '夜間加成(+200),';
                            }
                        }
                    }
                }
                $tmpPrice += $othrtPrice;
            }
            Log::info('ABC Query:', [$tmpPrice]);
            if ($input['vendor_id'] == '13' && $input['order_type'] == '3') {
                $tmpTotal = ($basePrice->price * 2) + $tmpPrice;
            } else {
                $tmpTotal = $basePrice->price + $tmpPrice;
            }
            if (!empty($input['num_of_bags'])) {
                $bagStr .= '行李數:' . $input['num_of_bags'] . '件,';
            }
            if (!empty($input['child_seat'])) {
                $bagStr .= '兒童座椅:' . $input['child_seat'] . '件,';
            }
            if (!empty($input['booster_pad'])) {
                $bagStr .= '增高墊:' . $input['booster_pad'] . '件,';
            }
        } else {
            // 包車旅遊
            if ($input['tour_car'] == 1) {
                // 一日遊
                switch ($input['car_type']) {
                    case '5':
                        if ($input['vendor_id'] == '10' || $input['vendor_id'] == '13') {
                            $tmpTotal = 5000;
                        } else if ($input['vendor_id'] == '12') {
                            $tmpTotal = 4000;
                        } else if ($input['vendor_id'] == '7') {
                            $tmpTotal = 4000;
                        }
                        break;
                    case '51':
                        if ($input['vendor_id'] == '10' || $input['vendor_id'] == '13') {
                            $tmpTotal = 5000;
                        } else if ($input['vendor_id'] == '12') {
                            $tmpTotal = 6000;
                        } else if ($input['vendor_id'] == '7') {
                            $tmpTotal = 4000;
                        }
                        break;
                    case '7':
                        if ($input['vendor_id'] == '10' || $input['vendor_id'] == '13') {
                            $tmpTotal = 6000;
                        } else if ($input['vendor_id'] == '12') {
                            $tmpTotal = 7000;
                        } else if ($input['vendor_id'] == '7') {
                            $tmpTotal = 4000;
                        }
                        break;
                    case '9':
                        if ($input['vendor_id'] == '10') {
                            $tmpTotal = 7000;
                        } else if ($input['vendor_id'] == '7') {
                            $tmpTotal = 5500;
                        } else if ($input['vendor_id'] == '12') {
                            $tmpTotal = 7000;
                        } else if ($input['vendor_id'] == '13') {
                            $tmpTotal = 8000;
                        }
                        break;
                    case '91':
                        if ($input['vendor_id'] == '10') {
                            $tmpTotal = 7000;
                        } else if ($input['vendor_id'] == '7') {
                            $tmpTotal = 5500;
                        } else if ($input['vendor_id'] == '12') {
                            $tmpTotal = 8000;
                        } else if ($input['vendor_id'] == '13') {
                            $tmpTotal = 8000;
                        }
                        break;
                }
                $bagStr = $messageUpLocation . $this->getLocationChinese($input['to_city_id'], 'city') . ' ' . $this->getLocationChinese($input['to_district_id'])
                    . PHP_EOL . $messageTourOneDay . $tmpTotal;
                if (!empty($input['note'])) {
                    $bagStr .= PHP_EOL . $messageNote . $input['note'];
                }
            } else {
                // 半日遊
                switch ($input['car_type']) {
                    case '5':
                        if ($input['vendor_id'] == '10' || $input['vendor_id'] == '13') {
                            $tmpTotal = 2500;
                        } else if ($input['vendor_id'] == '7') {
                            $tmpTotal = 2000;
                        }
                        break;
                    case '51':
                        if ($input['vendor_id'] == '10' || $input['vendor_id'] == '13') {
                            $tmpTotal = 2500;
                        } else if ($input['vendor_id'] == '7') {
                            $tmpTotal = 2000;
                        } else if ($input['vendor_id'] == '12') {
                            $tmpTotal = 3000;
                        }
                        break;
                    case '7':
                        $tmpTotal = 2500;
                        break;
                    case '9':
                        if ($input['vendor_id'] == '10' || $input['vendor_id'] == '13') {
                            $tmpTotal = 3500;
                        } else if ($input['vendor_id'] == '7') {
                            $tmpTotal = 3000;
                        } else if ($input['vendor_id'] == '12') {
                            $tmpTotal = 3500;
                        }
                        break;
                    case '91':
                        if ($input['vendor_id'] == '10' || $input['vendor_id'] == '13') {
                            $tmpTotal = 4000;
                        } else if ($input['vendor_id'] == '7') {
                            $tmpTotal = 4000;
                        } else if ($input['vendor_id'] == '12') {
                            $tmpTotal = 4000;
                        }
                        break;
                }
                $bagStr = $messageUpLocation . $this->getLocationChinese($input['to_city_id'], 'city') . ' ' . $this->getLocationChinese($input['to_district_id'])
                    . PHP_EOL . $messageTourHalfDay . $tmpTotal;
                if (!empty($input['note'])) {
                    $bagStr .= PHP_EOL . $messageNote . $input['note'];
                }
            }
        }
        Log::info('AD Query:', []);
        if (!empty($input['passenger_name'])) {
            $tmpUp = '';
            $tmpTime = $messageUpTime;
            $tmpAir = $messageArrivalAirport;
            if ($input['order_type'] == '0') {
                $tmpUp = $messageDown;
                $tmpTime = $messageFlightTime;
                $tmpAir = $messagetakeAirport;
            } else if ($input['order_type'] == '1' || $input['order_type'] == '2') {
                $tmpUp = $messageUp;
            }
            if ($input['order_type'] == '0' || $input['order_type'] == '1') {
                $tmpAirport = $tmpAir . $this->getLocationChinese($input['airport']);
            } else {
                $tmpAirport = '';
            }
            // $passengerStr = $tmpLocation.'區域: '.$this->getLocationChinese($input['to_city_id']).' '.$this->getLocationChinese($input['to_district_id']).PHP_EOL;
            $passengerStr .= $messagePassengerName . $input['passenger_name'] . PHP_EOL;
            $passengerStr .= $messagePassengerMobile . ($input['passenger_mobile'] ?? '') . PHP_EOL;
            $passengerStr .= empty($tmpAirport) ? '' : $tmpAirport . PHP_EOL;
            $passengerStr .= $tmpUp . $messageLocation . $this->getLocationChinese($input['to_city_id'], 'city') . ' ' . $this->getLocationChinese($input['to_district_id']) . ' ' . ($input['passenger_address'] ?? '') . PHP_EOL;
            $passengerStr .= $tmpTime . ($input['appointment_date'] ?? '') . PHP_EOL;
            if ($input['order_type'] != '2') {
                $passengerStr .= $messageFlightNo . ($input['flightno'] ?? '') . PHP_EOL;
            }
            $passengerStr .= $messagePayType . ($input['pay_type'] ?? '') . PHP_EOL;
            $passengerStr .= '=================' . PHP_EOL;
        }

        // \Log::info("input basePrice 00 ->> - ", $basePrice->toArray());
        if (!$basePrice) {
            $basePrice = [
                'from_district_name' => '',
                'to_area_id' => '',
                'to_area_name' => '',
                'to_city_id' => '',
                'to_city_name' => '',
                'to_district_name' => ''
            ];
        }
        return [
            'price' => $basePrice,
            'tmpTotal' => $tmpTotal,
            'otherStr' => $otherStr,
            'bagStr' => $bagStr,
            'passengerStr' => $passengerStr,
        ];
    }

    public function updateposition(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'latitude' => 'required',
            'longitude' => 'required',
            'user_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $rs = Position::create([
            'latitude' => $input['latitude'],
            'longitude' => $input['longitude'],
            'user_id' => $input['user_id'],
        ]);
        if ($rs) {
            return $this->sendResponse($rs, '成功新增資料.');
        } else {
            return $this->sendResponse($rs, '新增資料失敗.');
        }
    }

    // 空白表單功能
    public function editblanksignature(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required',
            'imgBase64' => 'required',
            'dispatch_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $driver = BlankDriver::where('line_id', '=', $input['line_id'])->first();
        if (!$driver) {
            return $this->sendError('查無此司機!請與車行洽詢!');
        }
        $signatureData = $input['imgBase64'];
        $dispatchData = BlankDispatch::where('id', '=', $input['dispatch_id'])->first();
        $signatureData = str_replace('data:image/png;base64,', '', $signatureData);
        $signatureData = str_replace(' ', '+', $signatureData);
        $signature = base64_decode($signatureData);
        // 簽名檔名
        $fileName = 'st_' . $dispatchData->dispatch_no . '.png';
        Storage::put('public/signatures/' . $fileName, $signature);
        $input['signature'] = storage_path('app/public/signatures/' . $fileName);
        $data = array(
            'dispatch_no' => $dispatchData->dispatch_no,
            'customer' => $dispatchData->customer,
            'unified' => $dispatchData->unified,
            'passenger' => $dispatchData->passenger,
            'mobile' => $dispatchData->mobile,
            'driver_name' => $driver->name,
            'driver_mobile' => $driver->mobile,
            'car_license' => $driver->car_license,
            'car_type' => $driver->car_type,
            'paytype' => (int) ($dispatchData->paytype == '1') ? '現金' : '月結',
            'start_location' => $dispatchData->start_location,
            // 'end_location' => $dispatchData->end_location,
            'start_date' => Carbon::parse($dispatchData->start_date)->format('Y-m-d'),
            'start_time' => Carbon::parse($dispatchData->start_date)->format('H:i'),
            'cost' => (empty($dispatchData->cost) ? null : $dispatchData->cost),
            'note' => (empty($dispatchData->note) ? '' : $dispatchData->note),
            'signature' => (empty($input['signature']) ? '' : $input['signature']),
        );
        $result = $this->makeBlankImage($data);

        $updateData = BlankDispatch::where('dispatch_no', '=', $dispatchData->dispatch_no)->update([
            'signature_file' => $input['signature'],
            'image_path' => $result,
        ]);
        return $this->sendResponse($updateData, '成功新增資料.');
    }

    public function addblankdispatch(Request $request)
    {
        $input = $request->all();

        // 驗證輸入資料
        $validator = Validator::make($input, [
            'line_id' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }

        // 檢查司機是否存在
        $driver = BlankDriver::where('line_id', '=', $input['line_id'])->first();
        if (!$driver) {
            return $this->sendError('查無此司機!請與車行洽詢!');
        }

        try {
            // 生成新的派車單號
            $newDispatchNo = 'PDP_' . date('YmdHis') . Str::random(3);

            // 建立新的空白派車單
            $dispatch = BlankDispatch::create([
                'dispatch_no' => $newDispatchNo,
                'customer' => $input['customer'],
                'unified' => $input['unified'],
                'passenger' => $input['passenger'],
                'mobile' => $input['mobile'],
                'start_date' => $input['start_date'],
                'start_location' => $input['start_location'],
                // 'end_location' => $input['end_location'],
                'cost' => $input['cost'],
                'driver_id' => $driver->id,
                'paytype' => $input['paytype'],
                'note' => $input['note'] ?? '',
            ]);

            if ($dispatch) {
                // 建立圖片所需的資料
                $data = [
                    'dispatch_no' => $dispatch->dispatch_no,
                    'customer' => $dispatch->customer,
                    'unified' => $dispatch->unified,
                    'passenger' => $dispatch->passenger,
                    'mobile' => $dispatch->mobile,
                    'driver_name' => $driver->name,
                    'driver_mobile' => $driver->mobile,
                    'car_license' => $driver->car_license,
                    'car_type' => $driver->car_type,
                    'start_location' => $dispatch->start_location,
                    // 'end_location' => $dispatch->end_location,
                    'paytype' => ($dispatch->paytype == '1') ? '現金' : '月結',
                    'start_date' => Carbon::parse($dispatch->start_date)->format('Y-m-d'),
                    'start_time' => Carbon::parse($dispatch->start_date)->format('H:i'),
                    'cost' => $dispatch->cost,
                    'note' => $dispatch->note ?? '',
                    'signature' => null
                ];

                // 生成派車單圖片
                $result = $this->makeBlankImage($data);

                if ($result) {
                    // 更新派車單圖片路徑
                    $updateData = BlankDispatch::where('dispatch_no', '=', $dispatch->dispatch_no)
                        ->update(['image_path' => $result]);

                    if (!$updateData) {
                        return $this->sendError('新增派車單圖片失敗.');
                    }

                    // 扣除點數
                    $updatePoint = BlankPoint::create([
                        'line_id' => $input['line_id'],
                        'point' => -1,
                        'type' => 2,
                        'reason' => '使用空白表單'
                    ]);

                    // 更新司機總點數
                    $totalPoints = BlankPoint::where('line_id', $input['line_id'])->sum('point');
                    $driver->total_points = $totalPoints;
                    $driver->save();
                }

                return $this->sendResponse($dispatch, '成功新增派車單.');
            }

            return $this->sendError('新增派車單失敗.');
        } catch (\Exception $e) {
            Log::error('addblankdispatch error: ' . $e->getMessage());
            return $this->sendError('新增派車單時發生錯誤: ' . $e->getMessage());
        }
    }

    public function editblankdispatch(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $driver = BlankDriver::where('line_id', '=', $input['line_id'])->first();
        if (!$driver) {
            return $this->sendError('查無此司機!請與車行洽詢!');
        }
        $blankdispatch = BlankDispatch::where('id', $input['id'])->first();
        if (!$blankdispatch) {
            return $this->sendError('查無此表單!');
        }
        $blankdispatch->customer = $input['customer'];
        $blankdispatch->unified = $input['unified'];
        $blankdispatch->passenger = $input['passenger'];
        $blankdispatch->mobile = $input['mobile'];
        $blankdispatch->paytype = (int) $input['paytype'];
        $blankdispatch->start_date = $input['start_date'];
        $blankdispatch->start_location = $input['start_location'];
        // $blankdispatch->end_location = $input['end_location'];
        $blankdispatch->cost = ($input['cost'] == null) ? null : $input['cost'];
        $blankdispatch->note = $input['note'] ?? '';
        $blankdispatch->save();
        $data = array(
            'dispatch_no' => $blankdispatch->dispatch_no,
            'customer' => $blankdispatch->customer,
            'unified' => $blankdispatch->unified,
            'passenger' => $blankdispatch->passenger,
            'mobile' => $blankdispatch->mobile,
            'driver_name' => $driver->name,
            'driver_mobile' => $driver->mobile,
            'car_license' => $driver->car_license,
            'car_type' => $driver->car_type,
            'paytype' => ($blankdispatch->paytype == '1') ? '現金' : '月結',
            'start_location' => $blankdispatch->start_location,
            // 'end_location' => $blankdispatch->end_location,
            'start_date' => Carbon::parse($blankdispatch->start_date)->format('Y-m-d'),
            'start_time' => Carbon::parse($blankdispatch->start_date)->format('H:i'),
            'cost' => (empty($blankdispatch->cost) ? null : $blankdispatch->cost),
            'note' => (empty($blankdispatch->note) ? '' : $blankdispatch->note),
            'signature' => (empty($blankdispatch->signature_file) ? null : $blankdispatch->signature_file),
        );
        $result = $this->makeBlankImage($data);
        if ($result) {
            Log::info('edit image path->> -' . $result);
            $updateData = BlankDispatch::where('dispatch_no', '=', $blankdispatch->dispatch_no)->update([
                'image_path' => $result,
            ]);
            Log::info('edit blank dispatch->> -' . $updateData);
            if (!$updateData) {
                return $this->sendResponse($blankdispatch, '更新資料失敗.');
            }
            $updatePoint = BlankPoint::create([
                'line_id' => $input['line_id'],
                'point' => -1,
                'type' => 3,
                'reason' => '編輯表單',
            ]);
            Log::info('updatePoint->> -', $updatePoint->toArray());
            $totalPoints = BlankPoint::where('line_id', $input['line_id'])->sum('point');
            // $rs->total_points = $totalPoints;
            $driver->total_points = $totalPoints;
            $driver->save();
        }
        return $this->sendResponse($blankdispatch, '成功更新資料.');
    }

    public function depositlist(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'search' => 'required',
            'line_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $driver = BlankDriver::where('line_id', '=', $input['line_id'])->first();
        if (!$driver) {
            return $this->sendError('查無此司機!請與車行洽詢!');
        }
        // $rs = Position::where('user_id', $input['user_id'])->orderBy('created_at', 'desc')->first();
        if ($driver) {
            if ($input['search'] == 'today') {
                $point = BlankPoint::where('line_id', '=', $input['line_id'])
                    // ->where('deleted_at', '=', null)
                    ->whereDate('start_date', '=', Carbon::now()->toDateString())
                    ->orderBy('start_date', 'desc')
                    ->get();
            } else {
                $point = BlankPoint::where('line_id', '=', $input['line_id'])
                    ->orderBy('id', 'desc')
                    ->get();
            }
            return $this->sendResponse($point, '成功取得資料.');
        } else {
            return $this->sendResponse($driver, '查無資料.');
        }
    }

    public function blanklist(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'search' => 'required',
            'line_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $driver = BlankDriver::where('line_id', '=', $input['line_id'])->first();
        if (!$driver) {
            return $this->sendError('查無此司機!請與車行洽詢!');
        }
        // $rs = Position::where('user_id', $input['user_id'])->orderBy('created_at', 'desc')->first();
        if ($driver) {
            if ($input['search'] == 'today') {
                $dispatch = BlankDispatch::where('driver_id', '=', $driver->id)
                    // ->where('deleted_at', '=', null)
                    ->whereDate('start_date', '=', Carbon::now()->toDateString())
                    ->orderBy('start_date', 'desc')
                    ->get();
            } else {
                $dispatch = BlankDispatch::where('driver_id', '=', $driver->id)
                    ->orderBy('start_date', 'desc')
                    ->get();
            }
            return $this->sendResponse($dispatch, '成功取得資料.');
        } else {
            return $this->sendResponse($driver, '查無資料.');
        }
    }

    // 空白表單建立
    public function blankdispatch(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $driver = BlankDriver::where('line_id', $input['line_id'])->first();
        if (!$driver) {
            return $this->sendResponse($driver, '查無此司機資料.');
        }
        // dd($driver);
        // $signatureData = $input['signature']['image'];
        $newDispatchNo = 'PDP_' . date('YmdHis') . Str::random(3);
        // if(empty($signatureData)){
        // $input['signature'] = '';
        //     // return  $this->sendResponse('message', 'add successfully.');
        // }else{
        //     $signatureData = str_replace('data:image/png;base64,', '', $signatureData);
        //     $signatureData = str_replace(' ', '+', $signatureData);
        //     $signature = base64_decode($signatureData);
        //     // 簽名檔名
        //     $fileName = 'st_' . $newDispatchNo . '.png';
        //     Storage::put('public/signatures_2/' . $fileName, $signature);
        //     $input['signature'] = storage_path('app/public/signatures_2/' . $fileName);
        //     // $updateData = Dispatch::where('dispatch_id', '=', $input['dispatch_id'])->update([
        //     //     'signature_file' => 'signatures/' .$fileName,
        //     // ]);
        // }

        $rs = BlankDispatch::create([
            'dispatch_no' => $newDispatchNo,
            'customer' => $input['customer'],
            'unified' => $input['unified'],
            'passenger' => $input['passenger'],
            'mobile' => $input['mobile'],
            'start_date' => $input['appointment_date'],
            'start_location' => $input['start_location'],
            // 'end_location' => $input['end_location'],
            'cost' => $input['cost'],
            'driver_id' => (int) $driver->id,
            'paytype' => (int) $input['paytype'],
            'note' => $input['note'] ?? '',
            'signature_file' => null,
        ]);
        // Log::info('輸出檔案路徑: '.$rs);
        if ($rs) {
            $data = array(
                'dispatch_no' => $rs->dispatch_no,
                'customer' => $rs->customer,
                'unified' => $rs->unified,
                'passenger' => $rs->passenger,
                'mobile' => $rs->mobile,
                'driver_name' => $driver->name,
                'driver_mobile' => $driver->mobile,
                'car_license' => $driver->car_license,
                'car_type' => $driver->car_type,
                'start_location' => $rs->start_location,
                // 'end_location' => $rs->end_location,
                'paytype' => (int) ($rs->paytype == '1') ? '現金' : '月結',
                'start_date' => Carbon::parse($rs->start_date)->format('Y-m-d'),
                'start_time' => Carbon::parse($rs->start_date)->format('H:i'),
                'cost' => (empty($rs->cost) ? null : $rs->cost),
                'note' => (empty($rs->note) ? '' : $rs->note),
                'signature' => (empty($rs->signature_file) ? '' : $rs->signature_file),
            );
            $result = $this->makeBlankImage($data);
            if ($result) {
                $updateData = BlankDispatch::where('dispatch_no', '=', $rs->dispatch_no)->update([
                    'image_path' => $result,
                ]);
                if (!$updateData) {
                    return $this->sendResponse($rs, '新增資料失敗.');
                }
                $updatePoint = BlankPoint::create([
                    'line_id' => $input['line_id'],
                    'point' => -1,
                    'type' => 2,
                    'reason' => '使用空白表單',
                ]);
                // Add the code to count the total points for the line_id here
                $totalPoints = BlankPoint::where('line_id', $input['line_id'])->sum('point');
                // $rs->total_points = $totalPoints;
                $driver->total_points = $totalPoints;
                $driver->save();
                // 4. 儲存更新後的 BlankDriver 記錄
                // $driver->save();
            }
            $mydata = BlankDispatch::where('id', '=', $rs->id)->first();
            Log::info('空白表單新增成功: ', $mydata->toArray());
            return $this->sendResponse($mydata, '成功新增資料.');
        } else {
            Log::info('空白表單新增失敗!!: ');
            return $this->sendResponse($rs, '新增資料失敗.');
        }
    }

    public function register(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required',
            'name' => 'required',
            'mobile' => 'required',
            'car_license' => 'required',
            'car_type' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $driver = BlankDriver::where('line_id', $input['line_id'])->first();
        if ($driver) {
            return $this->sendResponse($driver, '司機資料已存在!');
        }
        $driverData = BlankDriver::create([
            'line_id' => $input['line_id'],
            'name' => $input['name'],
            'nick_name' => $input['nickname'],
            'mobile' => $input['mobile'],
            'car_license' => $input['car_license'],
            'car_type' => $input['car_type'],
        ]);
        if ($driverData) {
            $addPoint = BlankPoint::create([
                'line_id' => $input['line_id'],
                'point' => 3,
                'type' => 1,
                'reason' => '註冊司機',
            ]);
            $driverData->total_points = 3;
            $driverData->save();
            return $this->sendResponse($driverData, '成功新增資料.');
        } else {
            return $this->sendResponse($driverData, '新增資料失敗.');
        }
    }

    public function updatedriver(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $driver = BlankDriver::where('line_id', $input['line_id'])->first();
        if (!$driver) {
            return $this->sendResponse($driver, '查無此司機資料.');
        }
        // 更新司機資料
        if (isset($input['name'])) {
            $driver->name = $input['name'];
        }
        if (isset($input['mobile'])) {
            $driver->mobile = $input['mobile'];
        }
        if (isset($input['car_license'])) {
            $driver->car_license = $input['car_license'];
        }
        if (isset($input['car_type'])) {
            $driver->car_type = $input['car_type'];
        }
        $driver->save();
        return $this->sendResponse($driver, '成功取得資料.');
    }

    public function checkdriver(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $driver = BlankDriver::where('line_id', $input['line_id'])->first();
        if (!$driver) {
            return $this->sendResponse($driver, 'EMPTY');
        }
        return $this->sendResponse($driver, '成功取得資料.');
    }

    public function point(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'line_id' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $driver = BlankDriver::where('line_id', $input['line_id'])->first();
        if (!$driver) {
            return $this->sendError('查無此司機!請與車行洽詢!');
        }
        return $this->sendResponse($driver, '成功取得資料.');
    }

    public function makeBlankImage($data)
    {
        $background = imagecreatefromjpeg(public_path('images/bg/blank.jpg'));
        $fontPath = public_path('fonts/SimHei.ttf');
        $black = imagecolorallocate($background, 0, 0, 0);
        $position = [
            'dispatch_no' => [1350, 230],
            'customer' => [1250, 310],
            'unified' => [1250, 380],
            'passenger' => [1250, 460],
            'mobile' => [1260, 540],
            'driver_name' => [450, 460],
            'driver_mobile' => [330, 242],
            'car_license' => [450, 630],
            'car_type' => [450, 540],
            'start_date' => [450, 310],
            'start_time' => [450, 380],
            'start_location' => [450, 740],
            // 'end_location' => [480, 840],
            'cost' => [1230, 620],
            'note' => [450, 970],
            'paytype' => [1720, 620],
            'signature' => [1320, 910],
        ];
        if (!empty($data['signature'])) {
            $signature = imagecreatefrompng($data['signature']);
            $signature_width = imagesx($signature);
            $signature_height = imagesy($signature);
            $new_width = $signature_width / 2;
            $new_height = $signature_height / 2;
            $resized_signature = imagecreatetruecolor($new_width, $new_height);
            imagealphablending($resized_signature, false);
            imagesavealpha($resized_signature, true);
            imagecopyresampled(
                $resized_signature,  // 目标图像资源
                $signature,  // 源图像资源
                0, 0,  // 目标图像的 x, y 坐标
                0, 0,  // 源图像的 x, y 坐标
                $new_width, $new_height,  // 目标图像的宽度和高度
                $signature_width, $signature_height  // 源图像的宽度和高度
            );
        }
        // dd($data);
        imagettftext($background, 30, 0, $position['dispatch_no'][0], $position['dispatch_no'][1], $black, $fontPath, $data['dispatch_no']);
        imagettftext($background, 40, 0, $position['start_date'][0], $position['start_date'][1], $black, $fontPath, $data['start_date']);
        imagettftext($background, 40, 0, $position['customer'][0], $position['customer'][1], $black, $fontPath, $data['customer']);
        imagettftext($background, 40, 0, $position['unified'][0], $position['unified'][1], $black, $fontPath, $data['unified']);
        imagettftext($background, 40, 0, $position['passenger'][0], $position['passenger'][1], $black, $fontPath, $data['passenger']);
        imagettftext($background, 40, 0, $position['mobile'][0], $position['mobile'][1], $black, $fontPath, $data['mobile']);
        imagettftext($background, 40, 0, $position['driver_name'][0], $position['driver_name'][1], $black, $fontPath, $data['driver_name']);
        imagettftext($background, 40, 0, $position['car_type'][0], $position['car_type'][1], $black, $fontPath, $data['car_type']);
        imagettftext($background, 40, 0, $position['car_license'][0], $position['car_license'][1], $black, $fontPath, $data['car_license']);
        imagettftext($background, 40, 0, $position['cost'][0], $position['cost'][1], $black, $fontPath, $data['cost']);
        imagettftext($background, 40, 0, $position['paytype'][0], $position['paytype'][1], $black, $fontPath, $data['paytype']);
        imagettftext($background, 40, 0, $position['start_time'][0], $position['start_time'][1], $black, $fontPath, $data['start_time']);
        // imagettftext($background, 40, 0, $position['start_location'][0], $position['start_location'][1], $black, $fontPath, $data['start_location']);
        // imagettftext($background, 40, 0, $position['end_location'][0], $position['end_location'][1], $black, $fontPath, $data['end_location']);
        // imagettftext($background, 40, 0, $position['note'][0], $position['note'][1], $black, $fontPath, $data['note']);
        // 新增換行處理
        $startLocationLines = $this->wrapText(40, 0, $fontPath, $data['start_location'], 1400);
        $startLocationX = $position['start_location'][0];
        $startLocationY = $position['start_location'][1];
        foreach ($startLocationLines as $line) {
            imagettftext($background, 40, 0, $startLocationX, $startLocationY, $black, $fontPath, $line);
            $startLocationY += 50;  // 行高可依需求調整
        }
        $noteX = 420;  // Start X
        $noteY = 970;  // Start Y
        $noteMaxWidth = 990 - 420;  // Max width
        $noteFontSize = 30;
        $noteLines = $this->wrapText($noteFontSize, 0, $fontPath, $data['note'], $noteMaxWidth);
        foreach ($noteLines as $line) {
            imagettftext($background, $noteFontSize, 0, $noteX, $noteY, $black, $fontPath, $line);
            $noteY += 40;  // Adjust line height as needed
        }

        $outputFileName = $data['dispatch_no'] . '.jpg';
        $outputFilePath = public_path('images_2/' . $outputFileName);
        // Log::info('輸出檔案路徑: '.$outputFilePath);
        imagejpeg($background, $outputFilePath);
        if (is_resource($background) && get_resource_type($background) === 'gd') {
            if (is_resource($background)) {
                imagedestroy($background);
            }
        }
        if (!empty($data['signature'])) {
            imagecopy($background, $resized_signature, $position['signature'][0], $position['signature'][1], 0, 0, $new_width, $new_height);
        }
        if (imagejpeg($background, $outputFilePath)) {
            $result = 'images_2/' . $outputFileName;
        } else {
            $result = 'error';
        }
        imagedestroy($background);
        if (!empty($data['signature'])) {
            imagedestroy($signature);
        }
        return $result;
    }

    private function wrapText($fontSize, $angle, $fontPath, $text, $maxWidth)
    {
        $lines = [];
        $line = '';
        for ($i = 0; $i < mb_strlen($text); $i++) {
            $char = mb_substr($text, $i, 1);
            $testString = $line . $char;
            $size = imagettfbbox($fontSize, $angle, $fontPath, $testString);
            if ($size[2] > $maxWidth && !empty($line)) {
                $lines[] = $line;
                $line = $char;
            } else {
                $line = $testString;
            }
        }
        $lines[] = $line;
        return $lines;
    }

    /**
     * 由 Chrome Extension 建立派車單
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createdispatchbychrome(Request $request)
    {
        Log::info('createdispatchbychrome request received', $request->all());
        $startTime = microtime(true);

        $input = $request->input('msg');
        // 檢查 'msg' 是否存在且為陣列
        if (!$input || !is_array($input)) {
            Log::error('createdispatchbychrome: "msg" key is missing or not an array in the request.', $request->all());
            return $this->sendError('請求格式錯誤：缺少 "msg" 資料或格式不正確。', [], 400);  // 400 Bad Request
        }

        // 1. 驗證輸入資料
        $validator = Validator::make($input, [
            '預約日期時間' => 'required|date_format:Y-m-d H:i',
            '乘客人數' => 'nullable|numeric',  // 允許空值或數字
            '乘客' => 'required|array|min:1',  // 必須是陣列且至少有一位乘客
            '乘客.*.姓名' => 'required|string|max:255',
            '乘客.*.編號' => 'nullable|string|max:255',  // 允許空編號
            '上車地點' => 'required|string|max:255',
            '下車地點' => 'required|string|max:255',
            '駕駛人' => 'nullable|string|max:255',  // 允許 null 或字串
            '車號' => 'nullable|string|max:255',
        ], [
            // 自訂中文錯誤訊息
            '預約日期時間.required' => '預約日期時間為必填。',
            '預約日期時間.date_format' => '預約日期時間格式應為 YYYY-MM-DD HH:MM。',
            '乘客.required' => '乘客資訊為必填。',
            '乘客.array' => '乘客資訊必須是陣列格式。',
            '乘客.min' => '至少需要一位乘客資訊。',
            '乘客.*.姓名.required' => '乘客姓名為必填。',
            '上車地點.required' => '上車地點為必填。',
            '下車地點.required' => '下車地點為必填。',
            // '駕駛人.required' => '駕駛人為必填。',
            // '車號.required' => '車號為必填。',
        ]);

        if ($validator->fails()) {
            Log::error('createdispatchbychrome validation failed', $validator->errors()->toArray());
            return $this->sendError('輸入資料驗證失敗', $validator->errors()->toArray());
        }

        $validatedData = $validator->validated();
        Log::info('createdispatchbychrome validation passed');

        try {
            // 2. 查找司機 (修改：僅在提供駕駛人姓名時查找)
            $driverName = $validatedData['駕駛人'] ?? null;  // 使用 ?? null 確保變數存在
            $serviceType = $input['服務類別'] ?? null;  // 使用 ?? null 確保變數存在
            $driver = null;
            $driverId = 1;
            $vendorId = 7;  // vendor_id 也依賴 driver

            if (!is_null($driverName) && !empty(trim($driverName))) {
                // 只有在提供了非空駕駛人姓名時才查找
                Log::info('createdispatchbychrome: Attempting to find driver', ['driver_name' => $driverName]);
                $driver = ADriver::where('name', $driverName)->first();

                if (!$driver) {
                    // 如果提供了姓名但找不到，報錯 (維持原有邏輯)
                    Log::warning('createdispatchbychrome: Driver name provided but not found in database', ['driver_name' => $driverName]);
                    return $this->sendError('提供了駕駛人姓名，但在資料庫中找不到: ' . $driverName);
                } else {
                    // 找到司機，設定 ID 和 Vendor ID
                    $driverId = $driver->id;
                    $vendorId = $driver->vendor_id;
                    Log::info('createdispatchbychrome: Found driver', ['driver_id' => $driverId, 'vendor_id' => $vendorId]);
                }
            } else {
                // 沒有提供駕駛人姓名，driverId 和 vendorId 保持 null
                Log::info('createdispatchbychrome: No driver name provided, skipping driver lookup.');
            }

            // 3. 準備要儲存的資料
            $dispatchId = 'EDP_' . date('YmdHis') . Str::random(3);
            $passengers = $validatedData['乘客'];
            $appointmentDateTime = Carbon::parse($validatedData['預約日期時間']);

            // 將乘客資訊格式化為 JSON 字串
            $passengerMultiData = array_map(function ($passenger) {
                return [
                    'id' => $passenger['編號'] ?? null,  // 如果編號為空或不存在，設為 null
                    'name' => $passenger['姓名'],
                    'signature' => '',  // 這裡不處理簽名
                ];
            }, $passengers);

            // 設定 customer_name (例如使用第一位乘客姓名)
            $customerName = $passengers[0]['姓名'] ?? '未提供';
            $carLicense = $validatedData['車號'] ?? null;  // 直接使用驗證後的值，若為 null 則存 null

            $dataToStore = [
                'dispatch_no' => $dispatchId,
                'start_date' => Carbon::parse($validatedData['預約日期時間'])->format('Y-m-d H:i:s'),
                'end_time' => null,
                'passenger' => $customerName,
                'mobile' => null,
                'people' => $validatedData['乘客人數'] ?? count($passengers),
                'start_location' => $validatedData['上車地點'],
                'end_location' => $validatedData['下車地點'],
                'service_type' => $serviceType,
                // --- 修改：使用查找後或為 null 的 ID ---
                'driver_id' => $driverId,
                'vendor_id' => $vendorId,
                // --- ---
                'status' => '未指派',
                'rental_cost' => null,
                'flight_no' => null,
                'passenger_multi' => json_encode($passengerMultiData, JSON_UNESCAPED_UNICODE),
                // --- 修改：使用驗證後或為 null 的車號 ---
                'car_license' => $carLicense,
                // --- ---
                // 根據 Dispatch 模型的 $fillable 欄位，加入其他必要的預設值
            ];

            Log::info('createdispatchbychrome: Data prepared for creation', $dataToStore);

            // 4. 建立 Dispatch 記錄
            $dispatch = ADispatch::create($dataToStore);

            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime);
            Log::info('createdispatchbychrome: Dispatch created successfully', [
                'dispatch_id' => $dispatch->id,
                'dispatch_no' => $dispatch->dispatch_id,
                'execution_time' => $executionTime
            ]);

            // 5. 準備圖片生成所需資料
            $imageData = [
                'dispatch_id' => $dispatch->dispatch_id,
                'customer_name' => $dataToStore['passenger'],  // '矽品'
                'customer_department' => '',  // 乘客姓名列表
                'start_date' => $appointmentDateTime->format('Y-m-d'),
                // 'start_time' => $appointmentDateTime->format('H:i'), // 直接用 HH:mm 格式
                // 根據時間判斷是日班還是夜班 (與 makeYatanImage 邏輯類似)
                'start_time_for_image' => ($appointmentDateTime->hour >= 6 && $appointmentDateTime->hour < 23) ? '日 DAY  06:00~23:00' : '夜 NIGHT  23:00~06:00',
                'rental_cost' => $dataToStore['rental_cost'] ?? '-',  // 如果是 null 顯示 '-'
                'driver_name' => $driver ? $driver->name : '未指派',  // 如果 driver 是 null
                'car_license' => $carLicense ?? '未指派',  // 如果車號是 null
                'route' => $dataToStore['start_location'] . '  到  ' . $dataToStore['end_location'],
                // 'signature' => null, // 這個方法目前不處理簽名
            ];
            Log::info('createdispatchbychrome: Data prepared for image generation', $imageData);

            // 6. 生成圖片
            $imagePath = $this->makeSipinDispatchImage($imageData);  // 呼叫圖片生成方法

            // 7. 更新圖片路徑到 Dispatch 記錄
            if ($imagePath && $imagePath !== 'error') {
                $dispatch->image_path = $imagePath;
                $dispatch->save();
                Log::info('createdispatchbychrome: Image generated and path updated successfully', ['dispatch_id' => $dispatch->dispatch_id, 'image_path' => $imagePath]);
            } else {
                Log::error('createdispatchbychrome: Failed to generate or save image path', ['dispatch_id' => $dispatch->dispatch_id]);
                // 考慮是否要因此回傳錯誤，或者只是記錄下來
            }

            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime);
            Log::info('createdispatchbychrome: Process completed', [
                'dispatch_id' => $dispatch->dispatch_id,
                'execution_time' => $executionTime
            ]);

            // 8. 回傳成功訊息
            return $this->sendResponse($dispatch->toArray(), '派車單已成功建立。');
        } catch (\Exception $e) {
            Log::error('createdispatchbychrome: Error during dispatch creation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()  // 可選，用於詳細偵錯
            ]);
            // 回傳伺服器錯誤訊息
            return $this->sendError('建立派車單時發生錯誤。', [], 500);
        }
    }

    /**
     * 生成矽品派車單圖片 (基於 makeYatanImage 修改)
     *
     * @param array $data 包含圖片所需資料的陣列
     * @return string|null 成功時返回圖片路徑，失敗時返回 'error' 或 null
     */
    private function makeSipinDispatchImage($data)
    {
        try {
            $backgroundPath = public_path('images/bg/yatan_sipin_bg.jpg');
            if (!file_exists($backgroundPath)) {
                Log::error('makeSipinDispatchImage: Background image not found', ['path' => $backgroundPath]);
                return 'error';
            }
            $background = imagecreatefromjpeg($backgroundPath);
            if (!$background) {
                Log::error('makeSipinDispatchImage: Failed to create image resource from background', ['path' => $backgroundPath]);
                return 'error';
            }

            $fontPath = public_path('fonts/jf-openhuninn-2.0.ttf');
            if (!file_exists($fontPath)) {
                Log::error('makeSipinDispatchImage: Font file not found', ['path' => $fontPath]);
                imagedestroy($background);
                return 'error';
            }
            $black = imagecolorallocate($background, 0, 0, 0);

            // --- 文字座標 (需要根據 yatan_sipin_bg.jpg 調整) ---
            // 這些座標是基於 dispatch_bg_7.jpg 的，你需要實際量測 yatan_sipin_bg.jpg 來調整！
            $position = [
                'dispatch_id' => [1500, 140],
                'customer_name' => [1325, 410],  // 公司名稱 (e.g., 矽品)
                'customer_department' => [1325, 573],  // 部門/乘客
                'start_date' => [539, 260],
                'start_time' => [1330, 260],  // 日班/夜班文字
                'rental_cost' => [760, 410],
                'driver_name' => [780, 760],
                'car_license' => [500, 760],
                'route' => [530, 550],
            ];
            // --- ---

            // 繪製文字
            imagettftext($background, 24, 0, $position['dispatch_id'][0], $position['dispatch_id'][1], $black, $fontPath, $data['dispatch_id'] ?? '');
            imagettftext($background, 32, 0, $position['customer_name'][0], $position['customer_name'][1], $black, $fontPath, $data['customer_name'] ?? '');

            // 部門/乘客可能需要換行
            $deptText = $data['customer_department'] ?? '';
            $deptX = $position['customer_department'][0];
            $deptY = $position['customer_department'][1];
            $deptMaxWidth = 400;  // 假設最大寬度，需要調整
            $deptFontSize = 30;  // 調整字體大小
            $deptLines = $this->wrapText($deptFontSize, 0, $fontPath, $deptText, $deptMaxWidth);
            foreach ($deptLines as $line) {
                imagettftext($background, $deptFontSize, 0, $deptX, $deptY, $black, $fontPath, $line);
                $deptY += 40;  // 調整行高
            }

            imagettftext($background, 40, 0, $position['start_date'][0], $position['start_date'][1], $black, $fontPath, $data['start_date'] ?? '');
            imagettftext($background, 30, 0, $position['start_time'][0], $position['start_time'][1], $black, $fontPath, $data['start_time_for_image'] ?? '');  // 使用格式化後的時間
            imagettftext($background, 36, 0, $position['rental_cost'][0], $position['rental_cost'][1], $black, $fontPath, $data['rental_cost'] ?? '');
            imagettftext($background, 40, 0, $position['driver_name'][0], $position['driver_name'][1], $black, $fontPath, $data['driver_name'] ?? '');
            imagettftext($background, 40, 0, $position['car_license'][0], $position['car_license'][1], $black, $fontPath, $data['car_license'] ?? '');

            // 路線可能需要換行
            $routeText = $data['route'] ?? '';
            $routeX = $position['route'][0];
            $routeY = $position['route'][1];
            $routeMaxWidth = 800;  // 假設最大寬度，需要調整
            $routeFontSize = 30;  // 調整字體大小
            $routeLines = $this->wrapText($routeFontSize, 0, $fontPath, $routeText, $routeMaxWidth);
            foreach ($routeLines as $line) {
                imagettftext($background, $routeFontSize, 0, $routeX, $routeY, $black, $fontPath, $line);
                $routeY += 40;  // 調整行高
            }

            // 儲存圖片
            $outputFileName = $data['dispatch_id'] . '.jpg';
            // 注意：路徑改為 public/images/ 以便公開訪問
            $outputDirectory = public_path('images/');
            $outputFilePath = $outputDirectory . $outputFileName;
            $relativePath = 'images/' . $outputFileName;  // 相對路徑用於存儲

            // 確保目錄存在
            if (!is_dir($outputDirectory)) {
                mkdir($outputDirectory, 0775, true);
            }

            if (imagejpeg($background, $outputFilePath, 90)) {  // 90 是 JPEG 品質
                Log::info('makeSipinDispatchImage: Image saved successfully', ['path' => $outputFilePath]);
                imagedestroy($background);
                return $relativePath;  // 返回相對路徑
            } else {
                Log::error('makeSipinDispatchImage: Failed to save JPEG image', ['path' => $outputFilePath]);
                imagedestroy($background);
                return 'error';
            }
        } catch (\Exception $e) {
            Log::error('makeSipinDispatchImage: Exception during image generation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            if (isset($background) && is_resource($background)) {
                imagedestroy($background);
            }
            return 'error';
        }
    }

    /**
     * 將文字換行以適應最大寬度 (從 DispatchController 複製)
     *
     * @param int $fontSize 字體大小
     * @param int $angle 角度
     * @param string $fontPath 字體檔案路徑
     * @param string $text 文字內容
     * @param int $maxWidth 最大寬度 (pixels)
     * @return array 包含多行文字的陣列
     */
    // private function wrapText($fontSize, $angle, $fontPath, $text, $maxWidth)
    // {
    //     $lines = [];
    //     $line = "";
    //     $text = $text ?? ''; // 確保 $text 不是 null
    //     if (empty(trim($text))) {
    //         return ['']; // 如果文字為空，返回包含空字串的陣列
    //     }
    //     for ($i = 0; $i < mb_strlen($text); $i++) {
    //         $char = mb_substr($text, $i, 1);
    //         $testString = $line . $char;
    //         // 使用 imagettfbbox 計算文字邊界框
    //         $bbox = imagettfbbox($fontSize, $angle, $fontPath, $testString);
    //         // $bbox[2] 是右下角 x 座標, $bbox[0] 是左下角 x 座標
    //         $textWidth = $bbox[2] - $bbox[0];

    //         if ($textWidth > $maxWidth && !empty($line)) {
    //             $lines[] = $line;
    //             $line = $char;
    //         } else {
    //             $line = $testString;
    //         }
    //     }
    //     $lines[] = $line; // 加入最後一行
    //     return $lines;
    // }

    /**
     * Send booking email with the provided data
     */
    public function sendBookingEmail(Request $request)
    {
        // 使用靜態數據而不是從請求獲取
        try {
            $input = $request->all();  // 獲取請求中的所有參數

            $emailContent = [
                'to' => '<EMAIL>',
                'subject' => '官網訂車需求',
                'text' => "
                    預約類型: {$input['bookingType']}
                    姓名: {$input['name']}
                    聯絡電話: {$input['mobile']}
                    Email: {$input['email']}
                    用車日期: {$input['bookingDate']}
                    用車時間: {$input['bookingTime']}
                    車型: {$input['vehicleType']}
                    乘客數: {$input['passengers']}
                    行李數: {$input['luggage']}
                    兒童座椅: {$input['childSeats']}
                    增高墊: {$input['boosterSeats']}
                    機場: {$input['airport']}
                "
            ];

            Mail::to($emailContent['to'])->send(new BookingMail($emailContent));

            return $this->sendResponse(
                ['success' => true],
                'Email sent successfully.'
            );
        } catch (\Exception $e) {
            Log::error('Email sending failed: ' . $e->getMessage());
            return $this->sendError(
                'Email Sending Error.',
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * 處理聯絡表單提交並轉發 Email
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendContactEmail(Request $request)
    {
        Log::info('sendContactEmail API called', $request->all());
        $startTime = microtime(true);

        // 1. 驗證傳入的請求資料
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',  // 可根據需要調整最大長度
        ], [
            // 123 自訂錯誤訊息 (可選)
            'name.required' => '姓名為必填欄位。',
            'email.required' => '電子郵件為必填欄位。',
            'email.email' => '請輸入有效的電子郵件地址。',
            'subject.required' => '主旨為必填欄位。',
            'message.required' => '訊息內容為必填欄位。',
        ]);

        if ($validator->fails()) {
            Log::error('sendContactEmail validation failed', $validator->errors()->toArray());
            // 使用 Controller 基類的 sendError 方法回傳錯誤
            return $this->sendError('輸入資料驗證失敗', $validator->errors()->toArray());
        }

        // 2. 取得驗證後的資料
        $validatedData = $validator->validated();
        $recipientEmail = '<EMAIL>';  // 目標 Email 地址

        // 3. 準備 Email 內容
        $emailSubject = '網站聯絡表單訊息: ' . $validatedData['subject'];
        $emailBody = "您收到一封來自網站聯絡表單的新訊息：\n\n";
        $emailBody .= '姓名 (Name): ' . $validatedData['name'] . "\n";
        $emailBody .= '電子郵件 (Email): ' . $validatedData['email'] . "\n";
        $emailBody .= '主旨 (Subject): ' . $validatedData['subject'] . "\n\n";
        $emailBody .= "訊息內容 (Message):\n" . $validatedData['message'] . "\n";

        // 4. 寄送 Email
        try {
            // 使用 Mail::raw() 寄送純文字郵件
            // 如果需要 HTML 郵件，可以建立一個 Mailable Class
            Mail::raw($emailBody, function ($mail) use ($recipientEmail, $emailSubject, $validatedData) {
                $mail
                    ->to($recipientEmail)
                    ->subject($emailSubject)
                    ->replyTo($validatedData['email'], $validatedData['name']);  // 設定 Reply-To，方便直接回覆給使用者
                // ->from(config('mail.from.address'), config('mail.from.name')); // 可選：如果需要指定寄件人
            });

            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime);
            Log::info('Contact email sent successfully', [
                'recipient' => $recipientEmail,
                'subject' => $validatedData['subject'],
                'sender_email' => $validatedData['email'],
                'execution_time' => $executionTime
            ]);

            // 使用 Controller 基類的 sendResponse 方法回傳成功訊息
            return $this->sendResponse([], '您的訊息已成功寄出，我們會盡快與您聯繫。');
        } catch (Exception $e) {
            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime);
            Log::error('Failed to send contact email', [
                'error' => $e->getMessage(),
                'recipient' => $recipientEmail,
                'data' => $validatedData,
                'execution_time' => $executionTime
                // 'trace' => $e->getTraceAsString() // 如果需要詳細追蹤，取消註解此行
            ]);

            // 使用 Controller 基類的 sendError 方法回傳一般錯誤訊息給使用者
            return $this->sendError('無法寄送訊息，請稍後再試或聯繫客服。', [], 500);
        }
    }

    /**
     * 軟刪除空白派車單（active=0）
     */
    public function deleteBlankDispatch(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }
        $dispatch = BlankDispatch::find($input['id']);
        if (!$dispatch) {
            return $this->sendError('查無此派車單!');
        }
        $dispatch->active = 0;
        $dispatch->save();
        return $this->sendResponse($dispatch, '已刪除(軟刪除)');
    }

    // 儲值
    public function topup(Request $request)
    {
        $request->validate([
            'line_id' => 'required|string',
            'amount' => 'required|numeric',
            'point' => 'required|numeric',
        ]);

        // 根據 line_id 查找司機
        $driver = BlankDriver::where('line_id', $request->line_id)->firstOrFail();
        if (!$driver) {
            return $this->sendError('查無此司機!請與車行洽詢!');
        }

        $topup = BlankTopup::create([
            'line_id' => $driver->line_id,
            'amount' => $request->amount,
            'point' => $request->point,
            'status' => 'pending'
        ]);
        if (!$topup) {
            return $this->sendError('儲值失敗!');
        }

        // 發送 Slack 通知
        // try {
        //     Notification::route('slack', config('notifications.slack.invoice_paid'))
        //         ->notify(new BlankTopupNotification($topup, $driver));
        //     Log::info('Slack notification sent for new topup', ['topup_id' => $topup->id, 'driver' => $driver->name]);
        // } catch (Exception $e) {
        //     Log::error('Failed to send Slack notification for topup', [
        //         'error' => $e->getMessage(),
        //         'topup_id' => $topup->id
        //     ]);
        // }

        return response()->json([
            'success' => true,
            'message' => '儲值申請已送出!轉帳/匯款後，請告知帳號後5碼',
            'data' => $topup
        ], 201);
    }

    // 取得公告
    public function bulletin(Request $request)
    {
        $request->validate([
            'vendor_id' => 'required|numeric'
        ]);

        $vendorId = $request->input('vendor_id');

        $user = User::where('vendor_id', $vendorId)->first();

        if ($user && isset($user->custom_fields['bulletin'])) {
            $bulletin = $user->custom_fields['bulletin'];
        } else {
            $bulletin = null;  // 或者您可以回傳一個預設值，例如空字串 ""
        }

        return $this->sendResponse($bulletin, '成功取得公告.');
    }

    /**
     * Store a newly created questionnaire in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function storeQuestionnaire(Request $request)
    {
        $input = $request->all();

        // 為了與 Questionnaire 模型的 $fillable 一致，建議請求的 JSON key 也使用 snake_case
        // 例如: booker_name 而不是 bookerName
        $validator = Validator::make($input, [
            'line_id' => 'required|string|max:255',
            'booker_name' => 'required|string|max:255',
            'ride_date' => 'required|date',
            'customer_service_rating' => 'required|integer|min:1|max:5',
            'driver_service_rating' => 'required|integer|min:1|max:5',
            'vehicle_condition_rating' => 'required|integer|min:1|max:5',
            'vehicle_cleanliness_rating' => 'required|integer|min:1|max:5',
            'has_recommended_ansing' => 'required|string|in:yes,no,maybe',  // 根據您的需求調整 in 的值
            'likelihood_to_recommend' => 'required|integer|min:0|max:10',
            'how_heard_about_ansing' => 'nullable|string|max:255',
            'booking_channel_awareness' => 'nullable|string|max:255',
            'additional_feedback' => 'nullable|string',
            'vendor_id' => 'required|integer|exists:vendors,id',  // 假設您有 vendors 資料表
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }

        try {
            $questionnaire = Questionnaire::create($validator->validated());
            return $this->sendResponse($questionnaire, '問卷已成功儲存。');
        } catch (\Exception $e) {
            Log::error('Error storing questionnaire: ' . $e->getMessage());
            return $this->sendError('儲存問卷時發生錯誤。', [], 500);
        }
    }

    public function carorderv3(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'order_type' => 'required',
            'car_type' => 'required',
            'vendor_id' => 'required',
            'airport' => 'required',
            'to_city_id' => 'required',
            'to_district_id' => 'required',
            'num_of_people' => 'required',
            'passenger_name' => 'required',
            'passenger_mobile' => 'required',
            'passenger_address' => 'required',
            'appointment_date' => 'required',
            'flightno' => 'required',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors());
        }

        // 查詢價格（與 getquotev2 相同邏輯）
        $orderTypeText = ['0' => '接機', '1' => '送機', '2' => '包車', '3' => '兩地接送'][$input['order_type']];
        $carTypeText = [
            '5' => '舒適小車',
            '51' => '五人座休旅車',
            '9' => '舒適大車',
            '91' => '尊爵小車'
        ][$input['car_type']] ?? '其他';
        $price = 0;
        $basePrice = null;
        $addItems = [];
        $addDesc = '';
        $vendorAddRules = [
            'default' => ['pickup' => 200, 'sign' => 100, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '2' => ['pickup' => 200, 'sign' => 200, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '7' => ['pickup' => 200, 'sign' => 231, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '10' => ['pickup' => 100, 'sign' => 100, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '12' => ['pickup' => 100, 'sign' => 200, 'stop' => 100, 'night_small' => 0, 'night_large' => 0],
            '13' => ['pickup' => 200, 'sign' => 100, 'stop' => 100, 'night_small' => 100, 'night_large' => 200],
            '17' => ['pickup' => 200, 'sign' => 100, 'stop' => 100, 'night_small' => 100, 'night_large' => 100],
        ];
        $rules = $vendorAddRules[$input['vendor_id']] ?? $vendorAddRules['default'];
        $note = $input['note'] ?? '';  // 確保備註欄存在
        if ($input['order_type'] == '0' || $input['order_type'] == '1') {
            $tmpOrderType = ($input['order_type'] == '1') ? '0' : $input['order_type'];
            $basePrice = Price::where('car_type', $input['car_type'])
                ->where('order_type', $tmpOrderType)
                ->where('vendor_id', $input['vendor_id'])
                ->where('from_district_id', $input['airport'])
                ->where('to_city_id', $input['to_city_id'])
                ->where('to_district_id', $input['to_district_id'])
                ->first();
            if (!$basePrice) {
                return $this->sendError('查無此路線報價', []);
            }
            $price = $basePrice->price;
            if ($input['order_type'] == '0') {
                $price += $rules['pickup'];
                $addItems[] = "接機加價+{$rules['pickup']}";
            }
            $childSeat = intval($input['child_seat'] ?? 0);
            if ($childSeat > 0) {
                $price += $childSeat * 200;
                $addItems[] = "兒童座椅+200x{$childSeat}";
            }
            $boosterPad = intval($input['booster_pad'] ?? 0);
            if ($boosterPad > 0) {
                $price += $boosterPad * 200;
                $addItems[] = "增高墊+200x{$boosterPad}";
            }
            if (!empty($input['other_service']) && is_array($input['other_service'])) {
                foreach ($input['other_service'] as $service) {
                    if ($service == '1') {
                        $price += $rules['sign'];
                        $addItems[] = "舉牌+{$rules['sign']}";
                    } elseif ($service == '3') {
                        $price += $rules['stop'];
                        $addItems[] = "停靠點+{$rules['stop']}";
                    } elseif ($service == '2') {
                        if (in_array($input['car_type'], ['5', '51'])) {
                            $price += $rules['night_small'];
                            $addItems[] = "夜間加成+{$rules['night_small']}";
                        } else {
                            $price += $rules['night_large'];
                            $addItems[] = "夜間加成+{$rules['night_large']}";
                        }
                    }
                }
            }
            $note = $input['note'] ?? '';  // 確保備註欄存在

            $additionalInfo = [];

            if (!empty($input['num_of_bags_airport'])) {
                $additionalInfo[] = '可登機行李數: ' . $input['num_of_bags_airport'];
            }

            if (!empty($input['trunk'])) {
                $additionalInfo[] = '胖胖箱數: ' . $input['trunk'];
            }

            if (!empty($input['child'])) {
                $additionalInfo[] = '兒童數: ' . $input['child'];
            }

            if (!empty($additionalInfo)) {
                $note .= PHP_EOL . implode(', ', $additionalInfo);
            }

            $addDesc = $addItems ? '（含' . implode('、', $addItems) . '）' : '';
            $from = $this->getLocationChinese($input['airport']);
            $to = $this->getLocationChinese($input['to_city_id'], 'city') . ' ' . $this->getLocationChinese($input['to_district_id']);
            $result = "類別：{$orderTypeText}\n"
                . "乘車大名: {$input['passenger_name']}\n"
                . "連絡電話: {$input['passenger_mobile']}\n"
                . "預約日期時間: {$input['appointment_date']}\n"
                . "上車地點：{$from}\n"
                . "下車地點：{$to}\n"
                . "詳細地址：{$input['passenger_address']}\n"
                . "航班資訊： {$input['flightno']}\n"
                . "搭乘人數：{$input['num_of_people']} 人\n"
                . '行李數：' . (!empty($input['num_of_bags']) ? $input['num_of_bags'] : '0') . " 件\n"
                . "車輛類型：{$carTypeText}\n"
                . "付款方式：{$input['pay_type']}\n"
                . '報價：NT$ ' . number_format($price) . "{$addDesc}"
                . "\n備註： {$note}";
        } else {
            $result = '包車報價請洽客服';
            $price = $input['total_price'];
        }
        // 建立或更新 Quotation
        $quotation = null;
        if (empty($input['quotation_id']) || $input['quotation_id'] == 0) {
            $quotation = new \App\Models\Quotation();
            $quotation->created_at = now();
        } else {
            $quotation = \App\Models\Quotation::find($input['quotation_id']);
            if (!$quotation) {
                return $this->sendError('查無此報價單', []);
            }
        }
        $quotation->status = 1;
        $quotation->vendor_id = $input['vendor_id'];
        $quotation->user_line_id = $input['line_id'] ?? '';
        $quotation->order_type = $input['order_type'];
        $quotation->car_type = $input['car_type'];
        $quotation->tour_type = $input['tour_type'] ?? 1;
        // if (!isset($input['airport'])) {
        //     return $this->sendError('缺少機場信息', []);
        // }
        if ($input['order_type'] == 2) {
            $quotation->location_from_id = null;
            $quotation->location_from_name = null;
        } else {
            $quotation->location_from_id = $input['airport'];
            $quotation->location_from_name = $this->getLocationChinese($input['airport']);
        }
        $quotation->location_city_id = $input['to_city_id'];
        $quotation->location_city_name = $this->getLocationChinese($input['to_city_id'], 'city');
        $quotation->location_district_id = $input['to_district_id'];
        $quotation->location_district_name = $this->getLocationChinese($input['to_district_id']);
        $quotation->num_of_people = $input['num_of_people'];
        $quotation->num_of_bags = $input['num_of_bags'] ?? 0;
        $quotation->child_seat = $input['child_seat'] ?? 0;
        $quotation->booster_pad = $input['booster_pad'] ?? 0;
        $quotation->other_service = $input['other_service'] ?? [];
        $quotation->passenger_name = $input['passenger_name'];
        $quotation->passenger_mobile = $input['passenger_mobile'];
        $quotation->passenger_address = $input['passenger_address'];
        $quotation->pay_type = $input['pay_type'] ?? '';
        // $quotation->note = $input['note'] ?? '';
        $quotation->appointment_date = $input['appointment_date'];
        $quotation->flightno = $input['flightno'];
        $quotation->total = isset($price) ? (int) $price : 0;
        // 更新備註欄
        $quotation->note = $note ?? '';
        $quotation->nickname = $input['nickname'] ?? null;
        $quotation->updated_at = now();
        $quotation->save();
        return $this->sendResponse([
            'quotation_id' => $quotation->id,
            'total' => number_format($price),
            'result' => $result,
            'quotation' => $quotation
        ], '預約成功');
    }
}
