<?php

namespace App\Traits;

use App\Models\Dispatch;
use Illuminate\Support\Str;

trait CreateYatanDispatch
{
    public static function createDispatchNoSignature($data)
    {
        // 台中雅潭派車單
        $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_7.jpg'));

        $fontPath = public_path('fonts/jf-openhuninn-2.0.ttf');
        $black = imagecolorallocate($background, 0, 0, 0);
        $dispatch_id_dest_x = 1500; // 合并时在背景图上的x坐标
        $dispatch_id_dest_y = 140; // 合并时在背景图上的y坐标
        $customer_name_dest_x = 1325; // 合并时在背景图上的x坐标
        $customer_name_dest_y = 410; // 合并时在背景图上的y坐标
        $customer_department_x = 1325; // 合并时在背景图上的x坐标
        $customer_department_y = 573; // 合并时在背景图上的y坐标
        $start_date_dest_x = 539; // 合并时在背景图上的x坐标
        $start_date_dest_y = 260; // 合并时在背景图上的y坐标
        $start_time_dest_x = 355; // 合并时在背景图上的y坐标
        $start_time_dest_y = 355; // 合并时在背景图上的y坐标
        $rental_cost_dest_x = 760; // 合并时在背景图上的y坐标
        $rental_cost_dest_y = 410; // 合并时在背景图上的y坐标
        $driver_name_dest_x = 780; // 合并时在背景图上的y坐标
        $driver_name_dest_y = 760; // 合并时在背景图上的y坐标
        $car_license_dest_x = 500; // 合并时在背景图上的y坐标
        $car_license_dest_y = 760; // 合并时在背景图上的y坐标
        $start_time_dest_x = 1330; // 合并时在背景图上的y坐标
        $start_time_dest_y = 260; // 合并时在背景图上的y坐标
        $route_start_dest_x = 530; // 合并时在背景图上的y坐标
        $route_start_dest_y = 550; // 合并时在背景图上的y坐标
        // $route_end_dest_x = 800; // 合并时在背景图上的y坐标
        // $route_end_dest_y = 550; // 合并时在背景图上的y坐标
        imagettftext($background, 24, 0, $dispatch_id_dest_x, $dispatch_id_dest_y, $black, $fontPath, $data['dispatch_id']);
        imagettftext($background, 32, 0, $customer_name_dest_x, $customer_name_dest_y, $black, $fontPath, $data['customer_name']);
        imagettftext($background, 40, 0, $customer_department_x, $customer_department_y, $black, $fontPath, $data['customer_department']);
        imagettftext($background, 40, 0, $start_date_dest_x, $start_date_dest_y, $black, $fontPath, $data['start_date']);
        imagettftext($background, 36, 0, $rental_cost_dest_x, $rental_cost_dest_y, $black, $fontPath, $data['rental_cost']);
        imagettftext($background, 40, 0, $driver_name_dest_x, $driver_name_dest_y, $black, $fontPath, $data['driver_name']);
        imagettftext($background, 40, 0, $car_license_dest_x, $car_license_dest_y, $black, $fontPath, $data['car_license']);
        imagettftext($background, 30, 0, $start_time_dest_x, $start_time_dest_y, $black, $fontPath, $data['start_time']);
        if(strlen($data['route']) > 36){
            $y = $route_start_dest_y-30;
            $maxWidth = 400;
            $lines = static::wrapText(20, 0, $fontPath, $data['route'], $maxWidth);
            foreach ($lines as $line) {
                imagettftext($background, 24, 0, $route_start_dest_x, $y, $black, $fontPath, $line);
                $y += 40;
            }
        }else{
            imagettftext($background, 30, 0, $route_start_dest_x, $route_start_dest_y, $black, $fontPath, $data['route']);
        }
        // imagettftext($background, 30, 0, $route_end_dest_x, $route_end_dest_y, $black, $fontPath, $data['route_end']);
        $outputFileName = $data['dispatch_id'] . '.jpg';
        $outputFilePath = public_path('images/' . $outputFileName);
        if(imagejpeg($background, $outputFilePath)){
            $result =  'images/' . $outputFileName;
        }else{
            $result = 'error';
        }
        imagedestroy($background);
        return $result;
    }
    private static function wrapText($fontSize, $angle, $fontPath, $text, $maxWidth)
    {
        $lines = [];
        $line = "";
        for ($i = 0; $i < mb_strlen($text); $i++) {
            $char = mb_substr($text, $i, 1);
            $testString = $line . $char;
            $size = imagettfbbox($fontSize, $angle, $fontPath, $testString);
            if ($size[2] > $maxWidth && !empty($line)) {
                $lines[] = $line;
                $line = $char;
            }else{
                $line = $testString;
            }
        }
        $lines[] = $line;
        return $lines;
    }
    public static function createYatanDispatchRecord($data)
    {
        $dispatch = new Dispatch();
        $dispatch->dispatch_id = 'DP_' . date('YmdHis') . Str::random(3);
        $dispatch->customer_name = $data['customer_name'];
        $dispatch->customer_address = $data['customer_address'];
        $dispatch->rental_cost = $data['rental_cost'];
        $dispatch->driver_id = $data['driver_id'];
        $dispatch->carno2 = $data['carno2'];
        $dispatch->start_time = $data['start_time'];
        $dispatch->route = $data['route'];
        $dispatch->vendor_id = $data['vendor_id'];
        $rs = $dispatch->save();
        if($rs) {
            return $dispatch->dispatch_id;
        }else{
            return false;
        }

    }
}
