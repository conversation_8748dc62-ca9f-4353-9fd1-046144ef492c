<?php

namespace App\Traits\Traits;

use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Models\Car;
use App\Models\Driver;
use App\Models\Vendor;
use App\Models\Dispatch;
use Illuminate\Support\Str;
use App\Traits\CreateYatanDispatch;

trait ScheduleDispatch
{
    use CreateYatanDispatch;
    // 雅潭 4位 固定班表
    public static function sendScheduleDispatch($vendor_id)
    {
        $arrCustomerAndDriver = ['passenger' =>
                ['河野廣瀨', '今村、沼野、福田', '牧野、瀧上、小島', '加藤，石塚，安永'],
            'driver' =>
                [944, 948, 957, 946],
            'route' =>
                ['文心 至 潭子', '太子國寶、星捷市、宏台松築  至  佳能', '文心One  至  佳能', '文心One  至  佳能']];
        $vendor = Vendor::where('id', $vendor_id)->first();

        for ($i = 0; $i < count($arrCustomerAndDriver['passenger']); $i++) {
            $dispatch = new Dispatch();
            try {
                $dispatch->dispatch_id = 'DP_' . date('YmdHis') . Str::random(3);
                $dispatch->customer_name = '佳能';
                $dispatch->customer_address = $arrCustomerAndDriver['passenger'][$i];
                $dispatch->driver_id = $arrCustomerAndDriver['driver'][$i];
                $dispatch->start_time = Carbon::now()->setTimezone('Asia/Taipei')->format('Y-m-d').' 06:00:00';
                $dispatch->route = $arrCustomerAndDriver['route'][$i];
                $dispatch->rental_cost = 400;
                $dispatch->vendor_id = 7;
                $dispatch->status = 0;
                $dispatch->save();
                Log::info('新增 Dispatch 成功', ['dispatch_id' => $dispatch->dispatch_id]);
            } catch (\Exception $e) {
                echo $e->getMessage();
                Log::error('新增 Dispatch 失敗', ['error' => $e->getMessage(), 'customer_address' => $arrCustomerAndDriver['passenger'][$i], 'driver_id' => $arrCustomerAndDriver['driver'][$i]]);
            }
            $driver = Driver::where('id', $arrCustomerAndDriver['driver'][$i])->first();
            $car = Car::where('driver_id', $arrCustomerAndDriver['driver'][$i])->first();
            $myTimeStr = $dispatch->start_time;
            $data = [
                'dispatch_id' => $dispatch->dispatch_id,
                'customer_name' => '佳能',
                'customer_address' =>  $arrCustomerAndDriver['passenger'][$i],
                'customer_department' =>  $dispatch->customer_address,
                'driver_id' => $dispatch->driver_id,
                'start_date' => Carbon::now()->setTimezone('Asia/Taipei')->format('Y-m-d'),
                'start_time' => '日 DAY  06:00~23:00',
                'route' => $dispatch->route,
                'rental_cost' => $dispatch->rental_cost,
                'customer_mobile' => '',
                'driver_name' => $driver->name,
                'driver_mobile' => $driver->mobile,
                'car_license' => $car->car_license,
            ];
            $path = static::createDispatchNoSignature($data);
            Log::info('發送派車單成功', ['path ' => $path]);
            if($path=='error'){
                Log::info('建立派車單失敗', ['data ' => $data]);
                return  'Signature File failed.';
            }
            Dispatch::where('id', $dispatch->id)
                ->update([
                    'image_path' => 'images/' . $dispatch->dispatch_id . '.jpg',
                ]);
                Log::info('建立派車單成功', ['data ' => $dispatch->id]);
        }
    }
}
