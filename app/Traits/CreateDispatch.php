<?php

namespace App\Traits;

use DateTime;
use App\Models\Car;
use App\Models\Driver;
use App\Models\Dispatch;
use Illuminate\Support\Str;

trait CreateDispatch
{
    public static function createDispatchSignature($data)
    {
        $vendor_id = $data['vendor_id'];
        // 通用簽名派車單
        if($vendor_id==1){
            $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg.jpg'));
        }else if($vendor_id==3){
            $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_03.jpg'));
        }else if($vendor_id==4){
            $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_4.jpg'));
        }else if($vendor_id>=5){
            $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_'.$vendor_id.'.jpg'));
        }else{
            $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_test.jpg'));
        }
        if(!empty($data['signature_file'])){
            $signature = imagecreatefrompng(storage_path('app/public/' . $data['signature_file']));
            $signature_width = imagesx($signature);
            $signature_height = imagesy($signature);
            $new_width = $signature_width / 2;
            $new_height = $signature_height / 2;
            $resized_signature = imagecreatetruecolor($new_width, $new_height);
            imagealphablending($resized_signature, false);
            imagesavealpha($resized_signature, true);
            imagecopyresampled(
                $resized_signature,  // 目标图像资源
                $signature,          // 源图像资源
                0, 0,                // 目标图像的 x, y 坐标
                0, 0,                // 源图像的 x, y 坐标
                $new_width, $new_height,  // 目标图像的宽度和高度
                $signature_width, $signature_height  // 源图像的宽度和高度
            );
        }
        $fontPath = public_path('fonts/SimHei.ttf');
        $black = imagecolorallocate($background, 0, 0, 0);
        $fullName = $data['name'];
        $onlyName = $data['name'];
        if(strlen($fullName) > 3){
        }else{
            if($data['sex']==0){
                $fullName = $data['name'].' 小姐/女士';
            }else{
                $fullName = $data['name'].' 先生';
            }
        }
        if($vendor_id==5){
            // $this->makeEPaper($background, $dpid, $data, $vendor_id, $black, $fontPath, $fullName, $onlyName);
        }else{
            // Log::info('fullName '.strlen($fullName));
            if(strlen($fullName) > 12){
                $y = 170;
                $maxWidth = 220;
                $lines = static::wrapText(20, 0, $fontPath, $fullName, $maxWidth);
                foreach ($lines as $line) {
                    imagettftext($background, 18, 0, 330, $y, $black, $fontPath, $line);
                    $y += 25;
                }
            }else{
                imagettftext($background, 24, 0, 340, 182, $black, $fontPath, $fullName);
            }
            // imagettftext($background, 26, 0, 670, 182, $black, $fontPath, $data['phone']);
            if(strlen($data['phone']) > 10){
                imagettftext($background, 20, 0, 660, 182, $black, $fontPath, $data['phone']);
            }else{
                imagettftext($background, 26, 0, 670, 182, $black, $fontPath, $data['phone']);
            }
            // imagettftext($background, 20, 0, 500, 220, $black, $fontPath, $data['dispatch_id']);
            imagettftext($background, 20, 0, 330, 242, $black, $fontPath, $data['customer_address']);
            imagettftext($background, 18, 0, 1070, 242, $black, $fontPath, $data['customer_id']);
            imagettftext($background, 18, 0, 960, 128, $black, $fontPath, $data['dispatch_id']);
            // 自駕司機
            imagettftext($background, 24, 0, 470, 364, $black, $fontPath, $data['driver_name']);
            $y = 350;
            $maxWidth = 200;
            $lines = static::wrapText(20, 0, $fontPath, $data['address'], $maxWidth);
            foreach ($lines as $line) {
                imagettftext($background, 20, 0, 1070, $y, $black, $fontPath, $line);
                $y += 25;
            }
            // imagettftext($background, 24, 0, 980, 360, $black, $fontPath, $data['address']);
            imagettftext($background, 22, 0, 470, 425, $black, $fontPath, $data['car_license']);
            imagettftext($background, 20, 0, 760, 364, $black, $fontPath, $data['person_id']);
            // imagettftext($background, 14, 0, 755, 425, $black, $fontPath, $data['car_engine_no']);
            $y2 = 412;
            $maxWidth2 = 160;
            $lines2 = static::wrapText(18, 0, $fontPath, $data['car_engine_no'], $maxWidth2);
            foreach ($lines2 as $line2) {
                imagettftext($background, 18, 0, 755, $y2, $black, $fontPath, $line2);
                $y2 += 25;
            }
            if(strlen($data['car_type']) > 12){
                $y3 = 420;
                $maxWidth3 = 200;
                $lines3 = static::wrapText(18, 0, $fontPath, $data['car_type'], $maxWidth3);
                foreach ($lines3 as $line3) {
                    imagettftext($background, 16, 0, 1080, $y3, $black, $fontPath, $line3);
                    $y3 += 20;
                }
            }else{
                imagettftext($background, 18, 0, 1074, 425, $black, $fontPath, $data['car_type']);
            }
            if(!empty($data['start_time']) || !empty($data['end_time'])){
                $datestr = static::convertToChineseDate($data['start_time'], $data['end_time']);
                imagettftext($background, 20, 0, 320, 490, $black, $fontPath, $datestr);
            }
            // if(!empty($data['end_time'])){
            //     imagettftext($background, 28, 0, 765, 490, $black, $fontPath, $data['end_time']);
            // }
            imagettftext($background, 30, 0, 356, 550, $black, $fontPath, $data['rental_cost']);
            imagettftext($background, 20, 0, 760, 550, $black, $fontPath, $data['odometer']);
            imagettftext($background, 18, 0, 1080, 550, $black, $fontPath, $data['flight_no']);
            imagettftext($background, 24, 0, 1160, 920, $black, $fontPath, $data['driver_name']);

            // 派車單
            // imagettftext($background, 20, 0, 248, 1050, $black, $fontPath, $fullName);
            if(strlen($fullName) > 12){
                $y = 1035;
                $maxWidth = 210;
                $lines = static::wrapText(20, 0, $fontPath, $fullName, $maxWidth);
                foreach ($lines as $line) {
                    imagettftext($background, 18, 0, 245, $y, $black, $fontPath, $line);
                    $y += 25;
                }
            }else{
                imagettftext($background, 20, 0, 248, 1050, $black, $fontPath, $fullName);
            }
            imagettftext($background, 20, 0, 940, 1050, $black, $fontPath, $data['people']);

            if(strlen($data['phone']) > 10){
                imagettftext($background, 12, 0, 1184, 1050, $black, $fontPath, $data['phone']);
            }else{
                imagettftext($background, 16, 0, 1187, 1050, $black, $fontPath, $data['phone']);
            }
            // imagettftext($background, 16, 0, 1187, 1050, $black, $fontPath, $data['phone']);
            imagettftext($background, 24, 0, 248, 1110, $black, $fontPath, $data['car_license']);
            imagettftext($background, 24, 0, 630, 1110, $black, $fontPath, $data['driver_name']);
            imagettftext($background, 16, 0, 898, 1110, $black, $fontPath, $data['person_id']);
            imagettftext($background, 16, 0, 1187, 1110, $black, $fontPath, $data['driver_mobile']);
            // Log::info('start==== date  '.$data['start_time'].'------'.$data['end_time']);
            if(!empty($data['start_time']) || !empty($data['end_time'])){
                $datestr = static::convertToChineseDate($data['start_time'], $data['end_time']);
                imagettftext($background, 22, 0, 257, 1177, $black, $fontPath, $datestr);
            }
            // if(!empty($data['end_time'])){
            //     imagettftext($background, 28, 0, 680, 1177, $black, $fontPath, $data['end_time']);
            // }

            imagettftext($background, 18, 0, 1190, 1235, $black, $fontPath, $data['flight_no']);
            imagettftext($background, 24, 0, 257, 1235, $black, $fontPath, $data['route']);
            imagettftext($background, 24, 0, 930, 1235, $black, $fontPath, $data['rental_cost']);

            imagettftext($background, 24, 0, 257, 1600, $black, $fontPath, $data['driver_name']);
            imagettftext($background, 24, 0, 700, 1600, $black, $fontPath, $data['driver_name']);
            // imagettftext($background, 24, 0, 1148, 1600, $black, $fontPath, $onlyName);
            if(strlen($onlyName) > 12){
                imagettftext($background, 12, 0, 1140, 1600, $black, $fontPath, $onlyName);
            }else{
                imagettftext($background, 24, 0, 1148, 1600, $black, $fontPath, $onlyName);
            }
        }
        // imagettftext($background, 30, 0, $route_end_dest_x, $route_end_dest_y, $black, $fontPath, $data['route_end']);
        $outputFileName = $data['dispatch_id'] . '.jpg';
        $outputFilePath = public_path('images/' . $outputFileName);
        if(imagejpeg($background, $outputFilePath)){
            $result =  'images/' . $outputFileName;
        }else{
            $result = 'error';
        }
        imagedestroy($background);
        return $result;
    }
    private static function wrapText($fontSize, $angle, $fontPath, $text, $maxWidth)
    {
        $lines = [];
        $line = "";
        for ($i = 0; $i < mb_strlen($text); $i++) {
            $char = mb_substr($text, $i, 1);
            $testString = $line . $char;
            $size = imagettfbbox($fontSize, $angle, $fontPath, $testString);
            if ($size[2] > $maxWidth && !empty($line)) {
                $lines[] = $line;
                $line = $char;
            }else{
                $line = $testString;
            }
        }
        $lines[] = $line;
        return $lines;
    }
    private static function convertToChineseDate($start_datetime, $end_datetime) {
        // 定义一个内部函数来处理日期格式化
        // 格式化开始和结束时间
        $startFormatted = static::formatChineseDate($start_datetime);
        $endFormatted = static::formatChineseDate($end_datetime);

        // 如果开始或结束时间为空，返回空白
        if (empty($startFormatted) || empty($endFormatted)) {
            return '';
        }

        // 构建最终的中文时间区间字符串
        return sprintf(
            "自民國 %s 起至 %s止",
            $startFormatted,
            $endFormatted
        );
    }
    private static function formatChineseDate($datetime) {
        if (empty($datetime)) {
            return '';
        }
        // 创建 DateTime 对象
        $date = new DateTime($datetime);
        // 获取公历年份
        $year = $date->format('Y');
        // 将公历年份转换为民国年份
        $rocYear = $year - 1911;
        // 获取月份和日期
        $month = $date->format('n'); // 'n' 获取没有前导零的月份
        $day = $date->format('j'); // 'j' 获取没有前导零的日期
        $hour = $date->format('G'); // 'G' 获取24小时制的小时，范围是 0-23
        $minute = $date->format('i'); // 'i' 获取分钟，范围是 00-59

        // 构建中文日期时间字符串
        return sprintf(
            "%d 年 %d 月 %d 日 %d 時 %d 分",
            $rocYear,
            $month,
            $day,
            $hour,
            $minute
        );
    }

    public static function createDispatch($data) {
        return $data;
        // dd($data);
        // $dispatch = new Dispatch();
        // $dispatch->fill($data);
        // $dispatch->save();
    }
    public static function createDispatchImage($dispatch_id) {
        $data = Dispatch::where('id', $dispatch_id)->first();
        $driver = Driver::where('id', $data->driver_id)->first();
        $car = Car::where('driver_id', $data->driver_id)->first();
        // $driver_car = DriverCar::where('driver_id', $data->driver_id
        $data->driver_name = $driver->name;
        $data->driver_mobile = $driver->mobile;
        $data->car_license = $car->car_license;
        $data->car_engine_no = $car->car_engine_no;
        $data->car_type = $car->car_type;
        $data->person_id = $driver->person_id;
        $data->address = (empty($driver->address) ? '' : $driver->address);

        if($data->vendor_id==1){
            $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg.jpg'));
        }else if($data->vendor_id==3){
            $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_03.jpg'));
        }else if($data->vendor_id==4){
            $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_4.jpg'));
        }else if($data->vendor_id>=5){
            $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_'.$data->vendor_id.'.jpg'));
        }else{
            $background = imagecreatefromjpeg(public_path('images/bg/dispatch_bg_test.jpg'));
        }
        $fontPath = public_path('fonts/jf-openhuninn-2.0.ttf');
        $black = imagecolorallocate($background, 0, 0, 0);
        $fullName = $data->customer_name;
        $onlyName = $data->customer_name;

        if(strlen($fullName) > 12){
            $y = 170;
            $maxWidth = 220;
            $lines = static::wrapText(20, 0, $fontPath, $fullName, $maxWidth);
            foreach ($lines as $line) {
                imagettftext($background, 18, 0, 330, $y, $black, $fontPath, $line);
                $y += 25;
            }
        }else{
            imagettftext($background, 24, 0, 340, 182, $black, $fontPath, $fullName);
        }
        // imagettftext($background, 26, 0, 670, 182, $black, $fontPath, $data['phone']);
        if(strlen($data['phone']) > 10){
            imagettftext($background, 20, 0, 660, 182, $black, $fontPath, $data['phone']);
        }else{
            imagettftext($background, 26, 0, 670, 182, $black, $fontPath, $data['phone']);
        }
        // imagettftext($background, 20, 0, 500, 220, $black, $fontPath, $data['dispatch_id']);
        imagettftext($background, 20, 0, 330, 242, $black, $fontPath, $data['customer_address']);
        imagettftext($background, 18, 0, 1070, 242, $black, $fontPath, $data['customer_id']);
        imagettftext($background, 18, 0, 960, 128, $black, $fontPath, $data['dispatch_id']);
        // 自駕司機
        imagettftext($background, 24, 0, 470, 364, $black, $fontPath, $data['driver_name']);
        $y = 350;
        $maxWidth = 200;
        $lines = static::wrapText(20, 0, $fontPath, $data['address'], $maxWidth);
        foreach ($lines as $line) {
            imagettftext($background, 20, 0, 1070, $y, $black, $fontPath, $line);
            $y += 25;
        }
        // imagettftext($background, 24, 0, 980, 360, $black, $fontPath, $data['address']);
        imagettftext($background, 22, 0, 470, 425, $black, $fontPath, $data['car_license']);
        imagettftext($background, 20, 0, 760, 364, $black, $fontPath, $data['person_id']);
        // imagettftext($background, 14, 0, 755, 425, $black, $fontPath, $data['car_engine_no']);
        $y2 = 412;
        $maxWidth2 = 160;
        $lines2 = static::wrapText(18, 0, $fontPath, $data['car_engine_no'], $maxWidth2);
        foreach ($lines2 as $line2) {
            imagettftext($background, 18, 0, 755, $y2, $black, $fontPath, $line2);
            $y2 += 25;
        }
        if(strlen($data['car_type']) > 12){
            $y3 = 420;
            $maxWidth3 = 200;
            $lines3 = static::wrapText(18, 0, $fontPath, $data['car_type'], $maxWidth3);
            foreach ($lines3 as $line3) {
                imagettftext($background, 16, 0, 1080, $y3, $black, $fontPath, $line3);
                $y3 += 20;
            }
        }else{
            imagettftext($background, 18, 0, 1074, 425, $black, $fontPath, $data['car_type']);
        }
        if(!empty($data['start_time']) || !empty($data['end_time'])){
            $datestr = static::convertToChineseDate($data['start_time'], $data['end_time']);
            imagettftext($background, 20, 0, 320, 490, $black, $fontPath, $datestr);
        }
        // if(!empty($data['end_time'])){
        //     imagettftext($background, 28, 0, 765, 490, $black, $fontPath, $data['end_time']);
        // }
        imagettftext($background, 30, 0, 356, 550, $black, $fontPath, $data['rental_cost']);
        imagettftext($background, 20, 0, 760, 550, $black, $fontPath, $data['odometer']);
        imagettftext($background, 18, 0, 1080, 550, $black, $fontPath, $data['flight_no']);
        imagettftext($background, 24, 0, 1160, 920, $black, $fontPath, $data['driver_name']);

        // 派車單
        // imagettftext($background, 20, 0, 248, 1050, $black, $fontPath, $fullName);
        if(strlen($fullName) > 12){
            $y = 1035;
            $maxWidth = 210;
            $lines = static::wrapText(20, 0, $fontPath, $fullName, $maxWidth);
            foreach ($lines as $line) {
                imagettftext($background, 18, 0, 245, $y, $black, $fontPath, $line);
                $y += 25;
            }
        }else{
            imagettftext($background, 20, 0, 248, 1050, $black, $fontPath, $fullName);
        }
        imagettftext($background, 20, 0, 940, 1050, $black, $fontPath, $data['people']);

        if(strlen($data['phone']) > 10){
            imagettftext($background, 12, 0, 1184, 1050, $black, $fontPath, $data['phone']);
        }else{
            imagettftext($background, 16, 0, 1187, 1050, $black, $fontPath, $data['phone']);
        }
        // imagettftext($background, 16, 0, 1187, 1050, $black, $fontPath, $data['phone']);
        imagettftext($background, 24, 0, 248, 1110, $black, $fontPath, $data['car_license']);
        imagettftext($background, 24, 0, 630, 1110, $black, $fontPath, $data['driver_name']);
        imagettftext($background, 16, 0, 898, 1110, $black, $fontPath, $data['person_id']);
        imagettftext($background, 16, 0, 1187, 1110, $black, $fontPath, $data['driver_mobile']);
        // Log::info('start==== date  '.$data['start_time'].'------'.$data['end_time']);
        if(!empty($data['start_time']) || !empty($data['end_time'])){
            $datestr = static::convertToChineseDate($data['start_time'], $data['end_time']);
            imagettftext($background, 22, 0, 257, 1177, $black, $fontPath, $datestr);
        }
        // if(!empty($data['end_time'])){
        //     imagettftext($background, 28, 0, 680, 1177, $black, $fontPath, $data['end_time']);
        // }

        imagettftext($background, 18, 0, 1190, 1235, $black, $fontPath, $data['flight_no']);
        imagettftext($background, 24, 0, 257, 1235, $black, $fontPath, $data['route']);
        imagettftext($background, 24, 0, 930, 1235, $black, $fontPath, $data['rental_cost']);

        imagettftext($background, 24, 0, 257, 1600, $black, $fontPath, $data['driver_name']);
        imagettftext($background, 24, 0, 700, 1600, $black, $fontPath, $data['driver_name']);
        // imagettftext($background, 24, 0, 1148, 1600, $black, $fontPath, $onlyName);
        if(strlen($onlyName) > 12){
            imagettftext($background, 12, 0, 1140, 1600, $black, $fontPath, $onlyName);
        }else{
            imagettftext($background, 24, 0, 1148, 1600, $black, $fontPath, $onlyName);
        }
        // header('Content-Type: image/jpeg');
        $outputFileName = $data->dispatch_id . '.jpg';
        $outputFilePath = public_path('images/' . $outputFileName);
        // Log::info('輸出檔案路徑: '.$outputFilePath);
        imagejpeg($background, $outputFilePath);
        imagedestroy($background);
        return $outputFileName;
    }
}
