<?php

namespace App\Exports;

use App\Models\Quotation;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class QuotationExport implements FromQuery, WithHeadings, WithMapping
{
    protected $filters;
    protected $ids;

    public function __construct($filters = [], $ids = [])
    {
        $this->filters = $filters;
        $this->ids = $ids;
    }

    public function query()
    {
        $query = Quotation::query();

        if (!empty($this->ids)) {
            $query->whereIn('id', $this->ids);
        } else {
            // Apply filters if no specific IDs are provided
            // 檢查 status 是否存在且不為 -1 (假設 -1 代表 "全部")
            if (isset($this->filters['status']) && $this->filters['status'] != -1 && $this->filters['status'] !== null) {
                $query->where('status', $this->filters['status']);
            }
            // 檢查 order_type 是否存在且不為 -1
            if (isset($this->filters['order_type']) && $this->filters['order_type'] != -1 && $this->filters['order_type'] !== null) {
                $query->where('order_type', $this->filters['order_type']);
            }
            if (!empty($this->filters['from'])) {
                $query->whereDate('appointment_date', '>=', $this->filters['from']);
            }
            if (!empty($this->filters['to'])) {
                $query->whereDate('appointment_date', '<=', $this->filters['to']);
            }
        }
        // 根據 Filament Resource 中的 getEloquentQuery() 邏輯來調整這裡的查詢
        // 例如，處理 vendor_id 的過濾
        // if(auth()->check() && auth()->user()->vendor_id != 0){
        //     $query->where('vendor_id', auth()->user()->vendor_id);
        // }
        return $query->whereNull('deleted_at'); // 假設您不想匯出軟刪除的記錄

        // 確保查詢有排序，否則匯出順序可能不固定
        // return $query->orderBy('id', 'desc');
    }

    public function headings(): array
    {
        return [
            'ID',
            '狀態',
            '類別', // order_type
            '車型', // car_type
            '聯絡人姓名',
            '聯絡人電話',
            '上車地點',
            '下車地點',
            '預約日期時間',
            '乘車人數',
            '行李數',
            '航班編號',
            '金額',
            '備註',
            '建立日期' // <--- 新增欄位標題
        ];
    }
    public function map($quotation): array
    {
        // 轉換 order_type
        $orderTypeChinese = '';
        if ($quotation->order_type == 0) {
            $orderTypeChinese = '接機';
        } elseif ($quotation->order_type == 1) {
            $orderTypeChinese = '送機';
        } elseif ($quotation->order_type == 2) {
            $orderTypeChinese = '包車';
        }

        // 轉換 car_type (參考 QuotationResource.php 中的邏輯)
        $carTypeChinese = '';
        if ($quotation->vendor_id == 17) {
            if ($quotation->car_type == 5) {
                $carTypeChinese = '舒適小車';
            } elseif ($quotation->car_type == 9) {
                $carTypeChinese = '舒適大車';
            } elseif ($quotation->car_type == 91) {
                $carTypeChinese = '尊榮大車';
            }
        } else {
            if ($quotation->car_type == 5) {
                $carTypeChinese = '五人轎車';
            } elseif ($quotation->car_type == 51) {
                $carTypeChinese = '五人休旅車';
            } elseif ($quotation->car_type == 9) {
                $carTypeChinese = '九人座';
            } elseif ($quotation->car_type == 91) { // 根據您的 ReserveController::getCarTypeChinese 邏輯，這裡可能是賓士Vito九人座
                 $carTypeChinese = '賓士Vito九人座';
            } elseif ($quotation->car_type == 10) { // 根據您的 ReserveController::getCarTypeChinese 邏輯
                 $carTypeChinese = 'Alphard尊爵';
            } else {
                $carTypeChinese = '五人轎車'; // 預設
            }
        }

        // 上車地點邏輯 (參考 QuotationResource.php)
        $locationFromName = '';
        $city = $quotation->location_from_name ?? '';
        $city2 = $quotation->location_city_name ?? '';
        $district = $quotation->location_district_name ?? '';
        $address = $quotation->passenger_address ?? '';
        if($quotation->status == 1){ // 預約狀態
            if($quotation->order_type == 0){ // 接機
                $locationFromName = trim("{$city}");
            } else if($quotation->order_type == 1 || $quotation->order_type == 2){ // 送機或包車
                $locationFromName = trim("{$city2} {$district}" . (!empty($address) ? " {$address}" : ''));
            }
        } else { // 詢價狀態
            if($quotation->order_type == 0){ // 接機
                $locationFromName = trim("{$city}");
            } else if($quotation->order_type == 1 || $quotation->order_type == 2){ // 送機或包車
                $locationFromName = trim("{$city2} {$district}");
            }
        }

        // 下車地點邏輯 (參考 QuotationResource.php)
        $locationToName = '';
        $cityTo = $quotation->location_city_name ?? '';
        $districtTo = $quotation->location_district_name ?? '';
        $fromTo = $quotation->location_from_name ?? ''; // 用於送機時的機場
        $addressTo = $quotation->passenger_address ?? '';
        if($quotation->status == 1){ // 預約狀態
            if($quotation->order_type == 0){ // 接機
                $fullTo = trim("{$cityTo} {$districtTo}");
                $locationToName = !empty($addressTo) ? $fullTo . " " . $addressTo : $fullTo;
            } else if($quotation->order_type == 1){ // 送機
                $locationToName = trim("{$fromTo}");
            }
        } else { // 詢價狀態
             if($quotation->order_type == 0){ // 接機
                $locationToName = trim("{$cityTo} {$districtTo}");
            } else if($quotation->order_type == 1){ // 送機
                $locationToName = trim("{$fromTo}");
            }
        }


        return [
            $quotation->id,
            $quotation->status == 0 ? '詢價' : '預約',
            $orderTypeChinese,
            $carTypeChinese,
            $quotation->passenger_name,
            $quotation->passenger_mobile,
            $locationFromName,
            $locationToName,
            $quotation->appointment_date,
            $quotation->num_of_people,
            $quotation->num_of_bags,
            $quotation->flightno,
            $quotation->total,
            $quotation->note,
            $quotation->created_at ? $quotation->created_at->format('Y-m-d H:i:s') : '', // <--- 新增欄位資料，並格式化日期
        ];
    }
}
