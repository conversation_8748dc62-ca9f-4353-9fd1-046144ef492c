<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\SlackMessage;

class CannonReport extends Notification
{
    use Queueable;
    public $title;
    public $driverCount;
    public $signCount;
    public $priceCount;

    /**
     * Create a new notification instance.
     */
    public function __construct($title, $driverCount, $signCount, $priceCount)
    {
        $this->title = $title;
        $this->driverCount = $driverCount;
        $this->signCount = $signCount;
        $this->priceCount = $priceCount;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['slack'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toSlack($notifiable)
    {
        return (new SlackMessage)
                    ->content($this->title)
                    ->attachment(function ($attachment) {
                        $attachment->title('詳細資訊')
                                  ->fields([
                                      '司機開車人數' => $this->driverCount,
                                      '簽名人數' => $this->signCount,
                                      '單價檢查' => $this->priceCount,
                                  ]);
                    });
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
