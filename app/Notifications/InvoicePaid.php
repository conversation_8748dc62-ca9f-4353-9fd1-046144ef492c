<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\SlackMessage;

class InvoicePaid extends Notification
{
    use Queueable;
    public $title;

    /**
     * Create a new notification instance.
     */
    public function __construct($title)
    {
        $this->title = $title;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['slack'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toSlack($notifiable)
    {
        $url = 'https://cars.chihlistudio.com/storage/pdf_outputs/Three_4_20241129_093354_67491a02d220e.pdf';
        return (new SlackMessage)
                    ->content($this->title)
                    ->attachment(function ($attachment) use ($url) {
                        $attachment->title('Invoice Paid')
                                  ->content('An invoice has been paid.')
                                  ->fields([
                                      'Customer' => '#1234',
                                      'Invoice No' => '1000',
                                      'Invoice Recipient' => '<EMAIL>',
                                  ])
                                  ->action('View Invoice', $url);
                    })
                    ->attachment(function ($attachment) {
                        $attachment->content('Congratulations!');
                    });
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
