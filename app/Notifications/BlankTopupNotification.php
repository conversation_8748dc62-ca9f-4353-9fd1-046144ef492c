<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\SlackMessage;

class BlankTopupNotification extends Notification
{
    use Queueable;

    protected $topup;
    protected $driver;

    /**
     * Create a new notification instance.
     */
    public function __construct($topup, $driver)
    {
        $this->topup = $topup;
        $this->driver = $driver;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['slack'];
    }

    /**
     * Get the Slack representation of the notification.
     */
    public function toSlack($notifiable)
    {
        return (new SlackMessage)
            ->content('新增儲值申請')
            ->attachment(function ($attachment) {
                $attachment->title('詳細資訊')
                          ->fields([
                              '司機' => $this->driver->name,
                              '儲值金額' => $this->topup->amount,
                              '點數' => $this->topup->point,
                              '狀態' => $this->getStatusText($this->topup->status),
                              '申請時間' => $this->topup->created_at,
                          ]);
            });
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    /**
     * Get the status text in Chinese.
     */
    protected function getStatusText($status)
    {
        switch ($status) {
            case 'pending':
                return '待審核';
            case 'approved':
                return '已通過';
            case 'rejected':
                return '已拒絕';
            default:
                return $status;
        }
    }
}
