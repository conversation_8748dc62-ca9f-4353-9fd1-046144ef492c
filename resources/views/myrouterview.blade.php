<style>
    body {
        font-family: "msyh";
    }
    .home-center {
        height: 5vh;
        position: relative;
        top: 20vh;
    }
    .page_break { page-break-before: always; }
</style>
<div class="container">
<?php
    use Carbon\Carbon;
    use App\Models\Driver;
    use App\Models\Car;
    $total = 0;
    echo '<div style="width:600px; margin:0 auto; text-align:center;">';
    echo '<h3>乘車列表:</h3>';
    echo '<table style="border-collapse: collapse;">';
    echo '<tr>';
    echo '<th style="width: 40px;">序號</th>';
    echo '<th style="width: 60px;">日期</th>';
    echo '<th style="width: 180px;">路程</th>';
    echo '<th style="width: 60px;">車資</th>';
    // echo '<th style="width: 60px;">司機</th>';
    echo '<th style="width: 60px;">車號</th>';
    echo '<th style="width: 160px;">簽名</th>';
    echo '</tr>';

    foreach ($data as $key => $item) {
        $mykey = $key + 1;
        $total += $item->rental_cost;
        // $driverData = Driver::where('id', $item->driver_id)->first();
        $carData = Car::where('driver_id', $item->driver_id)->first();
        // Log::info('aa'.print_r($carData, true));
        $mydatetime = Carbon::parse($item->start_time)->format('Y-m-d');
        // $signature = asset('storage/' . $item['signature_file']); //  //'https://cars.chihlistudio.com/storage/' . $item['signature_file']; //
        $signature = $fullpath = storage_path('app/public/'.$item['signature_file']);
        echo "<tr>";
        echo "<td style='text-align: center;font-size: small; width: 15%;'>$mykey</td>";
        echo "<td style='text-align: center;font-size: xx-small; width: 15%;'>$mydatetime</td>";
        echo "<td style='text-align: center; font-size: xx-small;width: 30%;'>$item->route</td>";
        echo "<td style='text-align: center;' >$item->rental_cost</td>";
        // echo "<td style='text-align: center; font-size: xx-small; width: 15%;'>$driverData->name </td>";
        echo "<td style='text-align: center; font-size: xx-small; width: 15%;'>$carData->car_license</td>";
        if($item['signature_file'] == null) {
            echo "<td></td>";
        } else {
            echo "<td style='text-align: center; font-size: xx-small;'><img src='data:image/png;base64,".base64_encode(file_get_contents($signature))."' width='100' height='50' /></td>";
        }
        // echo "<td style='font-size: xx-small;'><img src='$signature' width='100' height='50' /></td>";
        echo "</tr>";
    }
    echo '</table>';
    echo '<div style="color: red; font-size: larger;">總計: '.$total.'</div>';
    echo '</div>';

?>
