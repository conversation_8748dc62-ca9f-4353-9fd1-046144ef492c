@php
    // 為每個小部件實例生成唯一ID，避免多個實例衝突
    $widgetId = 'sound-notifier-' . uniqid();
@endphp

<div class="p-4 bg-white rounded-lg shadow mb-4">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium">聲音通知設置</h3>
        <div class="flex items-center space-x-2">
            <button
                id="{{ $widgetId }}-test-button"
                class="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition"
            >
                測試聲音
            </button>
            <button
                id="{{ $widgetId }}-check-button"
                class="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition"
            >
                檢查未指派記錄
            </button>
        </div>
    </div>

    <div class="flex items-center space-x-4">
        <audio id="{{ $widgetId }}-audio" controls style="max-width: 300px;">
            <source src="{{ asset('sounds/alert-2316.wav') }}" type="audio/wav">
            <source src="{{ asset('sounds/alert-2316.mp3') }}" type="audio/mpeg">
            您的瀏覽器不支持音頻元素。
        </audio>
        <div id="{{ $widgetId }}-status" class="text-sm text-gray-600">
            系統將自動檢測未指派的記錄並播放提示音
        </div>
    </div>
</div>

<script>
    // 立即執行函數，避免全局變量污染
    (function() {
        // 等待頁面完全加載
        document.addEventListener('DOMContentLoaded', function() {
            initSoundNotifier('{{ $widgetId }}');
        });

        // 如果 DOMContentLoaded 已經觸發，立即初始化
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            initSoundNotifier('{{ $widgetId }}');
        }

        function initSoundNotifier(widgetId) {
            console.log(`初始化聲音通知系統 (${widgetId})`);

            // 獲取元素
            const audio = document.getElementById(`${widgetId}-audio`);
            const testButton = document.getElementById(`${widgetId}-test-button`);
            const checkButton = document.getElementById(`${widgetId}-check-button`);
            const statusElement = document.getElementById(`${widgetId}-status`);

            if (!audio || !testButton || !checkButton || !statusElement) {
                console.error(`找不到必要的元素，聲音通知系統初始化失敗 (${widgetId})`);
                return;
            }

            // 上一次檢查時的未指派記錄數量
            let previousUnassignedCount = 0;

            // 初始化時獲取未指派記錄數量
            setTimeout(() => {
                previousUnassignedCount = countUnassignedElements();
                updateStatus(`初始化完成，找到 ${previousUnassignedCount} 個未指派記錄`);
                console.log(`初始未指派數量: ${previousUnassignedCount} (${widgetId})`);
            }, 1000);

            // 測試聲音按鈕點擊事件
            testButton.addEventListener('click', function() {
                updateStatus('正在測試聲音...');
                playSound();
            });

            // 手動檢查按鈕點擊事件
            checkButton.addEventListener('click', function() {
                updateStatus('正在檢查未指派記錄...');
                checkUnassigned(true); // 強制檢查
            });

            // 設置定期檢查
            const intervalId = setInterval(checkUnassigned, 5000); // 每5秒檢查一次

            // 監聽 Livewire 更新事件
            if (typeof window.Livewire !== 'undefined') {
                window.Livewire.hook('message.processed', () => {
                    setTimeout(() => checkUnassigned(), 100); // 短暫延遲確保 DOM 已更新
                });
            }

            // 更新狀態消息
            function updateStatus(message) {
                if (statusElement) {
                    statusElement.textContent = message;
                }
            }

            // 播放聲音的函數
            function playSound() {
                // 重置音頻並播放
                audio.currentTime = 0;

                // 嘗試播放聲音
                const playPromise = audio.play();

                // 處理可能的錯誤
                if (playPromise !== undefined) {
                    playPromise
                        .then(() => {
                            updateStatus('聲音播放成功');
                        })
                        .catch(error => {
                            console.error(`播放聲音失敗 (${widgetId}):`, error);
                            updateStatus('無法自動播放聲音，請點擊音頻控制器手動播放');
                        });
                }
            }

            // 計算未指派元素數量的函數
            function countUnassignedElements() {
                // 嘗試多種選擇器
                let elements = document.querySelectorAll('[data-status="未指派"]');

                // 如果沒找到，嘗試查找包含文本的元素
                if (elements.length === 0) {
                    const badges = document.querySelectorAll('.fi-badge, .badge, [role="status"]');
                    const unassignedBadges = Array.from(badges).filter(el =>
                        el.textContent.includes('未指派')
                    );
                    elements = unassignedBadges;
                }

                console.log(`找到未指派元素數量: ${elements.length} (${widgetId})`);
                return elements.length;
            }

            // 檢查未指派記錄的函數
            function checkUnassigned(forceCheck = false) {
                const currentCount = countUnassignedElements();
                console.log(`當前未指派數量: ${currentCount}, 先前數量: ${previousUnassignedCount} (${widgetId})`);

                // 如果未指派記錄數量增加或強制檢查
                if ((currentCount > previousUnassignedCount && previousUnassignedCount > 0) || forceCheck) {
                    if (forceCheck) {
                        updateStatus(`找到 ${currentCount} 個未指派記錄`);
                    } else {
                        updateStatus(`檢測到新的未指派記錄！從 ${previousUnassignedCount} 增加到 ${currentCount}`);
                        console.log(`檢測到新的未指派記錄，播放聲音 (${widgetId})`);
                        playSound();
                    }
                }

                // 更新先前數量
                previousUnassignedCount = currentCount;
            }
        }
    })();
</script>
