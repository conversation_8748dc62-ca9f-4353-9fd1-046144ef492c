{{-- /home/<USER>/car-dispatch/resources/views/filament/tables/actions/copy-button.blade.php --}}
@php
    $recordType = gettype($record ?? null);
    $isObject = is_object($record ?? null);
    $className = $isObject ? get_class($record) : 'N/A';
    $isADispatch = $record instanceof \App\Models\ADispatch;
@endphp

<div>
    <p>--- Blade 視圖除錯 ---</p>
    <p>傳入的 $record 變數類型: {{ $recordType }}</p>
    <p>是否為物件: {{ $isObject ? '是' : '否' }}</p>
    <p>物件類別: {{ $className }}</p>
    <p>是否為 App\Models\ADispatch 實例: {{ $isADispatch ? '是' : '否' }}</p>

    @if ($isADispatch)
        <p style="color: green;">成功！$record 是 ADispatch 模型。</p>
        <p>ID: {{ $record->id }}</p>

        @php
            // 準備文字
            $dateFormatted = $record->start_date ? \Carbon\Carbon::parse($record->start_date)->format('Y-m-d H:i') : 'N/A';
            $textToCopy = "預約日期: " . $dateFormatted . "\n";
            $textToCopy .= "上車地點: " . ($record->start_location ?? 'N/A') . "\n";
            $textToCopy .= "下車地點: " . ($record->end_location ?? 'N/A');
            $escapedTextForJs = Illuminate\Support\Js::from($textToCopy);
        @endphp

        {{-- 複製按鈕的 Alpine.js 邏輯 --}}
        <div x-data="{
            copyToClipboard() {
                console.log('Copy button clicked via Blade view for record {{ $record->id }}');
                if (!navigator.clipboard) {
                    console.error('Clipboard API not available.');
                    new FilamentNotification().title('複製失敗').body('您的瀏覽器不支援剪貼簿功能。').danger().send();
                    return;
                }
                const text = {{ $escapedTextForJs }};
                console.log('Attempting to copy via Blade view:', text);
                navigator.clipboard.writeText(text)
                    .then(() => {
                        console.log('Clipboard writeText successful (Blade view).');
                        new FilamentNotification().title('內容已複製到剪貼簿').success().send();
                    })
                    .catch(err => {
                        console.error('Clipboard writeText failed (Blade view):', err);
                        new FilamentNotification().title('複製失敗').body('無法複製內容。請檢查控制台錯誤。').danger().send();
                    });
            }
        }">
            <x-filament::icon-button
                icon="heroicon-o-clipboard-document"
                label="複製內容"
                color="info"
                tooltip="複製內容到剪貼簿"
                x-on:click.prevent="copyToClipboard"
            />
        </div>

    @else
        <p style="color: red;">錯誤！$record 不是預期的 ADispatch 模型。</p>
        <p>請檢查 Resource Action 定義或是否有其他程式碼影響了變數傳遞。</p>
        <p>實際收到的 $record 內容:</p>
        {{-- 嘗試用 dump 看看到底是什麼 --}}
        @dump($record ?? null)
    @endif
    <p>--- 結束除錯 ---</p>
</div>
