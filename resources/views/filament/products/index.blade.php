@extends('filament::layouts.app')

@section('content')
    <!-- 显示通知 -->
    @foreach (auth()->user()->unreadNotifications as $notification)
        <div class="alert alert-success">
            <p>{{ $notification->data['message'] }} <a href="{{ $notification->data['download_url'] }}" target="_blank">Download here</a></p>
        </div>
        <!-- 标记通知为已读 -->
        {{ $notification->markAsRead() }}
    @endforeach

    <!-- Filament Table Component -->
    {{ $this->table() }}
@endsection
