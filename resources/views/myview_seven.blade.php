<style>
    body {
        font-family: "msyh";
    }
    .home-center {
        height: 5vh;
        position: relative;
        top: 20vh;
    }
    .page_break { page-break-before: always; }
</style>
<div class="container">
<?php
\Carbon\Carbon::setLocale('zh');

        echo '<div style="text-align: center;">';
        echo '<h4>七和-固定乘車單</h4>';
        echo '<h5>司機 - </h5>';
        echo '<table border="1" cellpadding="10" cellspacing="0" style="margin: 0 auto;">';
        echo '<tr>';
        echo '<th></th>';
        echo '<th></th>';
        echo '<th>上班</th>';
        echo '<th>下班</th>';
        echo '<th></th>';
        echo '<th></th>';
        echo '<th>上班</th>';
        echo '<th>下班</th>';
        echo '</tr>';
        echo '<tr>';
        echo '<th>日期</th>';
        echo '<th>星期</th>';
        echo '<th>簽名</th>';
        echo '<th>簽名</th>';
        echo '<th>日期</th>';
        echo '<th>星期</th>';
        echo '<th>簽名</th>';
        echo '<th>簽名</th>';
        echo '</tr>';

        $rowCount = 0;
        // var_dump($dispatch);die;
        foreach ($data as $key => $value) {
            $date = \Carbon\Carbon::parse($value['start_time'])->format('n月j日');
            $dayOfWeek = \Carbon\Carbon::parse($value['start_time'])->translatedFormat('D');
            // $name = ''.$value['customer_name'];
            $signatureArr =explode(',', $value['signature_file']);
            if(!empty($signatureArr[1])){
                $signature = storage_path('app/public/'.$signatureArr[0]);
                $signature1 = storage_path('app/public/'.$signatureArr[1]);
            }else{

                $signature = null;
            }
            // $signature1 = storage_path('app/public/'.$signatureArr[1]);
            $amount = 0; //$value['rental_cost'];
            // $id = $value['id'];
            // echo $date . ' ' . $dayOfWeek . ' ' . $name . ' ' . $amount . '<br>';
            if ($rowCount % 2 == 0) {
                echo '<tr>';
            }

            // 输出列
            echo "<td>$date</td>";
            echo "<td>$dayOfWeek</td>";
            // echo "<td><img src='$signature' width='100' height='50'></td>";
            if($value['signature_file'] == null) {
                echo "<td></td>";
                echo "<td></td>";
            } else {
                if($signature != null){
                    echo "<td style='font-size: xx-small;'><img src='data:image/png;base64,".base64_encode(file_get_contents($signature))."' width='80' height='40' /></td>";
                }else{
                    echo "<td></td>";
                }
                if($signature1 != null){
                    echo "<td style='font-size: xx-small;'><img src='data:image/png;base64,".base64_encode(file_get_contents($signature1))."' width='80' height='40' /></td>";
                }else{
                    echo "<td></td>";
                }
            }

            // 如果已经输出了两列，关闭一行标签
            if ($rowCount % 2 == 1) {
                echo '</tr>';
            }

            // 行计数器加一
            $rowCount++;
        }
        if ($rowCount % 2 != 0) {
            echo "<td colspan='4'></td></tr>";
        }

        echo '</table></div>';
?>
</div>
