<style>
    body {
        font-family: "msyh";
    }
    .home-center {
        height: 5vh;
        position: relative;
        top: 20vh;
    }
    .page_break { page-break-before: always; }
</style>
<div class="container">
    <div style="width:600px; margin:0 auto; padding-top: 230px; text-align:center;">
        <div>
            <h1>佳能乘車月結報表</h1>
        </div><br />
        <div>
            <h2>2024年7月20日 ~ 2024年8月19日</h2>
        </div><br />
        <div>
            <h3>製表單位: 雅潭車行</h3>
        </div><br />
    </div>
    <div class="page_break"></div>
    <div style="width:600px; margin:0 auto; text-align:center;">
        <h3>乘車統計表:</h3>
        <table style="border-collapse: collapse;">
            <tr>
                <th>序號</th>
                <th>日期</th>
                <th>搭乘地點</th>
                <th>車資</th>
                <th>人員</th>
            </tr>

            @php
                use Carbon\Carbon;
                $total = 0;
            @endphp
            @foreach ($data as $item)
                @foreach ($item->aa as $item2)
                <tr>
                    <td style="font-size: small;">{{ $item2->dispatch_id }}</td>
                    <td style="font-size: xx-small;">{{ Carbon::parse($item2->start_time)->format('Y-m-d') }}</td>
                    <td style="text-align: center; font-size: xx-small;">{{ $item2->route }}</td>
                    <td>{{ $item2->rental_cost }}</td>
                    <td style="font-size: xx-small;">{{ $item2->customer_address }}</td>
                </tr>
                @endforeach
            <tr style="border-top: 1px #000 solid; border-bottom: 1px #000 solid;">
                <td>{{ $item->customer_address }}</td>
                <td></td>
                <td>小計: {{ $item->sum }}</td>
                <td>搭乘: {{ $item->COUNT }}  次</td>
            </tr>
                @php
                    $userSubtotal = $item->sum;
                    $total += $userSubtotal;
                @endphp
            @endforeach
        </table>
        <div style="color: red; font-size: larger;">總計: {{ $total }}</div>

    </div>
    <div class="page_break"></div>
    <div style="width:600px; margin:0 auto; text-align:center;">
        <h3>附件: 簽單</h3>
        @foreach ($data[0]->all as $item3)
        <h6>NO: {{ $item3->dispatch_id }}</h6>
            <img src="{{public_path($item3->image_path)}}" alt="派車單圖檔" width="500" />
        @endforeach
    </div>
    {{-- {{ var_dump($aa[0]) }} --}}
