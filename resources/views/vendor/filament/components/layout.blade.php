<body x-data @copy-to-clipboard.window="
    navigator.clipboard.writeText($event.detail.text).then(() => {
        // Optional: You could send another notification specifically confirming the browser action
        // new FilamentNotification()
        //     .title('已成功複製!')
        //     .success()
        //     .send();
    }).catch(err => {
        console.error('無法複製文字: ', err);
        new FilamentNotification()
            .title('複製失敗')
            .body('請檢查瀏覽器權限或手動複製。')
            .danger()
            .send();
    })
" class="...">
    {{-- Rest of the layout --}}
</body>
